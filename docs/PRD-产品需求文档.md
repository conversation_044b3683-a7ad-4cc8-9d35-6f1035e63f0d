# 产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目简介
基于Spring Boot 3.x + Vue.js 3的响应式开发运维平台

### 1.2 目标用户
- **开发者和DevOps工程师**
- **企业技术团队**
- **系统集成商**

### 1.3 核心价值主张
- **统一管理**: 通过Web界面统一管理多个MCP服务器配置和实例
- **安全隔离**: 基于Docker的沙箱环境确保服务器运行安全
- **响应式架构**: 采用Spring WebFlux提供高性能、低延迟的响应式服务
- **协议转换**: 将基于stdio的MCP服务器转换为HTTP API，便于集成
- **DevOps集成**: 提供完整的CI/CD管理功能，支持项目全生命周期管理

### 1.4 技术栈说明

#### 后端技术栈
- **Spring Boot 3.2+**: 应用框架，支持GraalVM Native Image
- **Spring WebFlux**: 响应式Web框架，提供非阻塞I/O
- **Spring Security 6.0+**: 安全框架，JWT认证
- **Spring Data R2DBC**: 响应式数据访问层
- **H2/PostgreSQL**: 开发/生产环境数据库
- **Docker Java API**: 容器管理和沙箱环境
- **JSON-RPC 2.0**: MCP协议实现

#### 前端技术栈
- **Vue 3.3+**: 渐进式JavaScript框架，使用Composition API
- **TypeScript 5.0+**: 类型安全的JavaScript超集
- **Pinia 2.0+**: 状态管理库
- **Vue Router 4.0+**: 单页应用路由
- **Tailwind CSS 3.0+**: 原子化CSS框架
- **Vite 4.0+**: 现代化构建工具
- **Axios**: HTTP客户端库

### 1.5 项目架构概述
采用前后端分离的微服务架构，后端提供RESTful API和SSE（Server-Sent Events）接口，前端通过HTTP和WebSocket与后端通信。系统支持Docker容器化部署，具备水平扩展能力。

## 2. 功能需求详细说明

### 2.1 用户认证与授权模块

#### 2.1.1 功能描述
提供基于JWT的用户认证和授权机制，确保系统安全性。

#### 2.1.2 用户故事
- **作为用户**，我希望能够注册账户，以便使用系统功能
- **作为用户**，我希望能够安全登录，以便访问我的配置和实例
- **作为用户**，我希望系统能够记住我的登录状态，以便提升使用体验

#### 2.1.3 验收标准
- [ ] 用户可以通过用户名/密码注册新账户
- [ ] 用户可以通过用户名/密码登录系统
- [ ] 系统生成JWT令牌并设置合理的过期时间
- [ ] 前端自动处理令牌刷新和过期
- [ ] 用户可以安全退出登录

#### 2.1.4 API接口规范
```
POST /auth/register - 用户注册
POST /auth/login - 用户登录
POST /auth/logout - 用户退出
GET /auth/me - 获取当前用户信息
```

### 2.2 MCP服务器配置管理模块

#### 2.2.1 功能描述
提供MCP服务器配置的CRUD操作，支持Docker镜像、资源限制、网络配置等参数设置。

#### 2.2.2 用户故事
- **作为用户**，我希望能够创建MCP服务器配置，以便定义服务器运行参数
- **作为用户**，我希望能够编辑现有配置，以便调整服务器设置
- **作为用户**，我希望能够删除不需要的配置，以便保持配置列表整洁
- **作为用户**，我希望能够启用/禁用配置，以便控制服务器是否可用

#### 2.2.3 验收标准
- [ ] 用户可以创建新的MCP服务器配置
- [ ] 配置包含必要字段：名称、描述、命令、Docker镜像等
- [ ] 支持高级配置：资源限制、网络设置、卷挂载、环境变量
- [ ] 用户只能管理自己创建的配置
- [ ] 配置数据持久化存储
- [ ] 提供配置模板功能

#### 2.2.4 API接口规范
```
GET /api/mcp/configurations - 获取用户配置列表
POST /api/mcp/configurations - 创建新配置
PUT /api/mcp/configurations/{id} - 更新配置
DELETE /api/mcp/configurations/{id} - 删除配置
GET /api/mcp/configurations/{id} - 获取配置详情
```

### 2.3 MCP服务器实例管理模块

#### 2.3.1 功能描述
管理MCP服务器实例的生命周期，包括启动、停止、监控和资源使用统计。

#### 2.3.2 用户故事
- **作为用户**，我希望能够启动MCP服务器实例，以便使用MCP服务
- **作为用户**，我希望能够停止运行中的实例，以便释放资源
- **作为用户**，我希望能够查看实例状态，以便了解服务器运行情况
- **作为用户**，我希望能够查看资源使用情况，以便优化配置

#### 2.3.3 验收标准
- [ ] 用户可以基于配置启动MCP服务器实例
- [ ] 实例在Docker容器中安全运行
- [ ] 用户可以停止运行中的实例
- [ ] 实时显示实例状态（运行中、已停止、错误等）
- [ ] 提供资源使用监控（CPU、内存、网络）
- [ ] 支持实例自动重启功能

#### 2.3.4 API接口规范
```
GET /api/mcp/instances - 获取用户实例列表
POST /api/mcp/instances - 启动新实例
DELETE /api/mcp/instances/{id} - 停止实例
GET /api/mcp/instances/{id}/status - 获取实例状态
GET /api/mcp/instances/{id}/resources - 获取资源使用情况
```

### 2.4 MCP协议代理模块

#### 2.4.1 功能描述
实现MCP协议的HTTP代理功能，将HTTP请求转换为MCP JSON-RPC消息，支持工具发现和调用。

#### 2.4.2 用户故事
- **作为开发者**，我希望能够通过HTTP API调用MCP工具，以便集成到我的应用中
- **作为用户**，我希望能够测试MCP工具功能，以便验证配置正确性
- **作为用户**，我希望能够查看可用工具列表，以便了解服务器能力

#### 2.4.3 验收标准
- [ ] 支持MCP协议初始化流程
- [ ] 自动发现MCP服务器提供的工具
- [ ] 将HTTP请求转换为JSON-RPC消息
- [ ] 处理MCP服务器响应并转换为HTTP响应
- [ ] 支持工具参数验证
- [ ] 提供错误处理和重试机制

#### 2.4.4 API接口规范
```
GET /api/mcp/proxy/{serverName}/tools - 获取工具列表
POST /api/mcp/proxy/{serverName}/tools/{toolName} - 调用工具
GET /api/mcp/proxy/{serverName}/info - 获取服务器信息
```

### 2.5 SSE通信模块

#### 2.5.1 功能描述
提供基于Server-Sent Events的实时通信功能，支持MCP客户端与服务器的持续连接。

#### 2.5.2 用户故事
- **作为开发者**，我希望能够建立SSE连接，以便实时接收MCP服务器消息
- **作为用户**，我希望能够通过SSE发送消息，以便与MCP服务器交互

#### 2.5.3 验收标准
- [ ] 支持SSE连接建立和会话管理
- [ ] 实现MCP协议的SSE传输层
- [ ] 支持消息的双向传输
- [ ] 处理连接断开和重连
- [ ] 提供会话状态管理

#### 2.5.4 API接口规范
```
GET /api/mcp/sse/{mcpServerName} - 建立SSE会话
POST /api/mcp/sse/message - 发送消息到会话
```

### 2.6 DevOps项目管理模块

#### 2.6.1 功能描述
提供完整的DevOps项目生命周期管理，包括项目、应用、组件的层级管理。

#### 2.6.2 用户故事
- **作为项目经理**，我希望能够创建和管理项目，以便组织开发工作
- **作为开发者**，我希望能够管理应用和组件，以便跟踪代码仓库
- **作为运维人员**，我希望能够查看项目资源使用情况，以便优化配置

#### 2.6.3 验收标准
- [ ] 支持项目的创建、编辑、删除操作
- [ ] 支持应用的层级管理（隶属于项目）
- [ ] 支持组件的代码仓库关联
- [ ] 提供项目状态跟踪功能
- [ ] 支持资源使用统计和监控

#### 2.6.4 API接口规范
```
GET /api/devops/projects - 获取项目列表
POST /api/devops/projects - 创建项目
PUT /api/devops/projects/{id} - 更新项目
DELETE /api/devops/projects/{id} - 删除项目
GET /api/devops/applications - 获取应用列表
POST /api/devops/applications - 创建应用
GET /api/devops/components - 获取组件列表
POST /api/devops/components - 创建组件
```

### 2.7 CI/CD管理模块

#### 2.7.1 功能描述
提供持续集成和持续部署的任务管理功能，支持Tekton等CI/CD平台集成。

#### 2.7.2 用户故事
- **作为开发者**，我希望能够创建CI任务，以便自动化构建和测试
- **作为运维人员**，我希望能够创建CD任务，以便自动化部署
- **作为团队负责人**，我希望能够监控CI/CD任务状态，以便跟踪交付进度

#### 2.7.3 验收标准
- [ ] 支持CI任务的创建和配置
- [ ] 支持CD任务的创建和配置
- [ ] 提供任务执行状态监控
- [ ] 支持任务日志查看
- [ ] 提供任务模板功能
- [ ] 支持任务的启动、停止、取消操作

#### 2.7.4 API接口规范
```
GET /api/devops/ci/tasks - 获取CI任务列表
POST /api/devops/ci/tasks - 创建CI任务
PUT /api/devops/ci/tasks/{id} - 更新CI任务
DELETE /api/devops/ci/tasks/{id} - 删除CI任务
POST /api/devops/ci/tasks/{id}/execute - 执行CI任务
GET /api/devops/ci/tasks/{id}/logs - 获取任务日志
GET /api/devops/cd/tasks - 获取CD任务列表
POST /api/devops/cd/tasks - 创建CD任务
```

## 3. 用户界面设计

### 3.1 前端页面结构

系统采用单页应用（SPA）架构，主要页面包括：

1. **登录页面** (`/login`) - 用户认证入口
2. **MCP控制台** (`/mcp`) - MCP服务器管理主界面
3. **DevOps控制台** (`/devops`) - DevOps项目管理主界面
4. **项目管理** (`/devops/projects`) - 项目列表和管理
5. **应用管理** (`/devops/applications`) - 应用列表和管理
6. **组件管理** (`/devops/components`) - 组件列表和管理
7. **CI/CD管理** (`/devops/ci-cd`) - 持续集成和部署管理
8. **监控中心** (`/devops/monitoring`) - 系统监控和资源使用

### 3.2 导航流程图

```mermaid
graph TD
    A[用户访问系统] --> B{是否已登录?}
    B -->|否| C[登录页面 /login]
    B -->|是| D[重定向到MCP控制台]

    C --> E[用户输入凭据]
    E --> F{认证成功?}
    F -->|否| C
    F -->|是| D[MCP控制台 /mcp]

    D --> G[主导航菜单]
    G --> H[MCP管理]
    G --> I[DevOps管理]

    H --> H1[MCP服务器配置]
    H --> H2[MCP实例管理]
    H --> H3[工具测试]

    I --> I1[项目管理 /devops/projects]
    I --> I2[应用管理 /devops/applications]
    I --> I3[组件管理 /devops/components]
    I --> I4[CI/CD管理 /devops/ci-cd]
    I --> I5[监控中心 /devops/monitoring]

    H1 --> H1A[创建配置]
    H1 --> H1B[编辑配置]
    H1 --> H1C[删除配置]

    H2 --> H2A[启动实例]
    H2 --> H2B[停止实例]
    H2 --> H2C[查看状态]

    I1 --> I1A[创建项目]
    I1 --> I1B[项目详情]

    I2 --> I2A[创建应用]
    I2 --> I2B[应用详情]

    I3 --> I3A[创建组件]
    I3 --> I3B[组件详情]

    I4 --> I4A[创建CI任务]
    I4 --> I4B[创建CD任务]
    I4 --> I4C[任务监控]
```

### 3.3 用户操作流程图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端界面
    participant B as 后端API
    participant D as 数据库
    participant Docker as Docker引擎

    Note over U,Docker: MCP服务器配置创建流程
    U->>F: 点击"创建配置"
    F->>U: 显示配置表单
    U->>F: 填写配置信息
    F->>B: POST /api/mcp/configurations
    B->>D: 保存配置到数据库
    D-->>B: 返回配置ID
    B-->>F: 返回创建成功
    F-->>U: 显示配置列表

    Note over U,Docker: MCP服务器实例启动流程
    U->>F: 点击"启动实例"
    F->>B: POST /api/mcp/instances
    B->>D: 查询配置信息
    D-->>B: 返回配置详情
    B->>Docker: 创建Docker容器
    Docker-->>B: 返回容器ID
    B->>D: 保存实例信息
    D-->>B: 确认保存
    B-->>F: 返回实例状态
    F-->>U: 显示实例运行中

    Note over U,Docker: MCP工具调用流程
    U->>F: 选择工具并输入参数
    F->>B: POST /api/mcp/proxy/{server}/tools/{tool}
    B->>Docker: 发送JSON-RPC请求到容器
    Docker-->>B: 返回工具执行结果
    B-->>F: 返回结果数据
    F-->>U: 显示执行结果
```

### 3.4 响应式设计要求

#### 3.4.1 桌面端设计 (≥1024px)
- 采用侧边栏导航 + 主内容区域布局
- 支持多列数据展示和复杂表单
- 提供丰富的交互组件和数据可视化

#### 3.4.2 平板端设计 (768px-1023px)
- 可折叠侧边栏导航
- 自适应表格和卡片布局
- 优化触摸操作体验

#### 3.4.3 移动端设计 (≤767px)
- 底部标签栏导航
- 单列卡片布局
- 简化操作流程，突出核心功能

## 4. 技术架构图

### 4.1 前后端交互流程图

```mermaid
graph TB
    subgraph "前端层 (Frontend)"
        A[Vue.js 3 + TypeScript]
        B[Pinia 状态管理]
        C[Vue Router 路由]
        D[Tailwind CSS 样式]
        E[Axios HTTP客户端]
    end

    subgraph "网关层 (Gateway)"
        F[Nginx 反向代理]
        G[SSL/TLS 终端]
    end

    subgraph "应用层 (Backend)"
        H[Spring Boot 3.x]
        I[Spring WebFlux]
        J[Spring Security]
        K[JWT 认证]
    end

    subgraph "业务服务层 (Services)"
        L[MCP配置服务]
        M[MCP代理服务]
        N[DevOps管理服务]
        O[用户认证服务]
        P[沙箱管理服务]
    end

    subgraph "数据访问层 (Data Access)"
        Q[Spring Data R2DBC]
        R[Repository 接口]
    end

    subgraph "数据存储层 (Storage)"
        S[(H2/PostgreSQL 数据库)]
        T[(Redis 缓存)]
    end

    subgraph "容器化层 (Containerization)"
        U[Docker Engine]
        V[MCP服务器容器]
        W[应用容器]
    end

    subgraph "外部集成 (External)"
        X[Tekton CI/CD]
        Y[监控系统]
        Z[日志系统]
    end

    A --> F
    B --> A
    C --> A
    D --> A
    E --> A

    F --> H
    G --> F

    H --> I
    H --> J
    H --> K

    I --> L
    I --> M
    I --> N
    I --> O
    I --> P

    L --> Q
    M --> Q
    N --> Q
    O --> Q
    P --> Q

    Q --> R
    R --> S
    R --> T

    P --> U
    U --> V
    U --> W

    N --> X
    H --> Y
    H --> Z
```

### 4.2 数据库设计和实体关系图

```mermaid
erDiagram
    USERS {
        bigint id PK
        varchar username UK
        varchar password
        varchar email UK
        varchar full_name
        boolean enabled
    }

    MCP_SERVER_CONFIGURATIONS {
        bigint id PK
        varchar name
        clob description
        varchar command
        clob arguments
        clob environment
        varchar working_directory
        varchar docker_image
        clob resource_limits
        clob network_config
        clob volume_mounts
        clob host_entries
        integer timeout_seconds
        boolean auto_restart
        boolean enabled
        bigint user_id FK
        timestamp created_at
        timestamp updated_at
    }

    MCP_SERVER_INSTANCES {
        bigint id PK
        bigint configuration_id FK
        varchar sandbox_id
        varchar status
        varchar sandbox_type
        timestamp started_at
        timestamp stopped_at
        varchar error_message
        bigint user_id FK
        timestamp created_at
        timestamp updated_at
    }

    DEVOPS_PROJECTS {
        bigint id PK
        varchar name
        clob description
        varchar status
        bigint user_id FK
        timestamp created_at
        timestamp updated_at
    }

    DEVOPS_APPLICATIONS {
        bigint id PK
        varchar name
        clob description
        bigint project_id FK
        varchar status
        bigint user_id FK
        timestamp created_at
        timestamp updated_at
    }

    DEVOPS_COMPONENTS {
        bigint id PK
        varchar name
        clob description
        bigint application_id FK
        varchar repository_url
        varchar repository_type
        varchar status
        bigint user_id FK
        timestamp created_at
        timestamp updated_at
    }

    DEVOPS_CI_TASKS {
        bigint id PK
        varchar name
        clob description
        bigint component_id FK
        varchar status
        clob configuration
        bigint user_id FK
        timestamp created_at
        timestamp updated_at
    }

    DEVOPS_CD_TASKS {
        bigint id PK
        varchar name
        clob description
        bigint application_id FK
        clob component_versions
        varchar status
        clob configuration
        bigint user_id FK
        timestamp created_at
        timestamp updated_at
    }

    USERS ||--o{ MCP_SERVER_CONFIGURATIONS : "owns"
    USERS ||--o{ MCP_SERVER_INSTANCES : "owns"
    USERS ||--o{ DEVOPS_PROJECTS : "owns"
    USERS ||--o{ DEVOPS_APPLICATIONS : "owns"
    USERS ||--o{ DEVOPS_COMPONENTS : "owns"
    USERS ||--o{ DEVOPS_CI_TASKS : "owns"
    USERS ||--o{ DEVOPS_CD_TASKS : "owns"

    MCP_SERVER_CONFIGURATIONS ||--o{ MCP_SERVER_INSTANCES : "spawns"

    DEVOPS_PROJECTS ||--o{ DEVOPS_APPLICATIONS : "contains"
    DEVOPS_APPLICATIONS ||--o{ DEVOPS_COMPONENTS : "contains"
    DEVOPS_APPLICATIONS ||--o{ DEVOPS_CD_TASKS : "deploys"
    DEVOPS_COMPONENTS ||--o{ DEVOPS_CI_TASKS : "builds"
```

### 4.3 MCP服务架构图

```mermaid
graph LR
    subgraph "客户端层"
        A[Web浏览器]
        B[移动应用]
        C[第三方集成]
    end

    subgraph "API网关层"
        D[Spring WebFlux]
        E[认证中间件]
        F[限流中间件]
    end

    subgraph "MCP协议层"
        G[HTTP-to-MCP转换器]
        H[SSE事件流处理器]
        I[JSON-RPC协议处理器]
    end

    subgraph "沙箱管理层"
        J[沙箱抽象接口]
        K[Docker沙箱实现]
        L[Kubernetes沙箱实现]
    end

    subgraph "MCP服务器容器"
        M[Python MCP服务器]
        N[Node.js MCP服务器]
        O[自定义MCP服务器]
    end

    subgraph "监控和日志"
        P[资源监控]
        Q[日志收集]
        R[性能指标]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F
    F --> G
    F --> H

    G --> I
    H --> I
    I --> J

    J --> K
    J --> L

    K --> M
    K --> N
    K --> O

    L --> M
    L --> N
    L --> O

    K --> P
    L --> P
    P --> Q
    P --> R
```

### 4.4 Docker容器化部署架构

```mermaid
graph TB
    subgraph "负载均衡层"
        A[Nginx 反向代理]
        B[SSL终端]
    end

    subgraph "应用容器层"
        C[Frontend Container<br/>Vue.js + Nginx]
        D[Backend Container<br/>Spring Boot JVM]
        E[Backend Native Container<br/>Spring Boot Native]
    end

    subgraph "数据存储层"
        F[(PostgreSQL Container)]
        G[(Redis Container)]
        H[Volume: postgres_data]
        I[Volume: redis_data]
        J[Volume: mcp_data]
    end

    subgraph "MCP沙箱层"
        K[Docker Engine]
        L[MCP Server Container 1]
        M[MCP Server Container 2]
        N[MCP Server Container N]
    end

    subgraph "网络层"
        O[mcp-network Bridge]
    end

    subgraph "外部服务"
        P[Tekton CI/CD]
        Q[监控系统]
        R[日志系统]
    end

    A --> C
    A --> D
    A --> E
    B --> A

    C --> O
    D --> O
    E --> O
    F --> O
    G --> O

    D --> K
    E --> K
    K --> L
    K --> M
    K --> N

    F --> H
    G --> I
    D --> J
    E --> J

    D --> P
    E --> P
    D --> Q
    E --> Q
    D --> R
    E --> R
```

## 5. 非功能性需求

### 5.1 性能要求

#### 5.1.1 响应时间要求
- **API响应时间**: 95%的请求在500ms内响应
- **页面加载时间**: 首屏加载时间不超过2秒
- **MCP工具调用**: 单次工具调用响应时间不超过30秒
- **实时数据更新**: SSE事件推送延迟不超过100ms

#### 5.1.2 并发性能要求
- **并发用户数**: 支持1000个并发用户同时在线
- **MCP实例数**: 单用户最多同时运行10个MCP服务器实例
- **API吞吐量**: 支持每秒1000次API调用

#### 5.1.3 资源使用要求
- **内存使用**: 后端应用内存使用不超过2GB
- **CPU使用**: 正常负载下CPU使用率不超过70%
- **存储空间**: 支持TB级别的数据存储

### 5.2 安全要求

#### 5.2.1 认证和授权
- **JWT令牌**: 使用256位密钥加密，有效期24小时
- **密码安全**: 使用BCrypt加密存储，最少8位包含特殊字符
- **会话管理**: 支持令牌自动刷新和安全退出
- **权限控制**: 基于用户的资源访问控制

#### 5.2.2 数据安全
- **数据传输**: 所有API调用使用HTTPS加密
- **数据存储**: 敏感数据加密存储
- **输入验证**: 所有用户输入进行严格验证和过滤
- **SQL注入防护**: 使用参数化查询防止SQL注入

#### 5.2.3 容器安全
- **沙箱隔离**: MCP服务器在独立容器中运行
- **资源限制**: 严格限制容器CPU和内存使用
- **网络隔离**: 容器网络隔离，仅开放必要端口
- **镜像安全**: 使用官方基础镜像，定期更新安全补丁

### 5.3 可扩展性要求

#### 5.3.1 水平扩展
- **无状态设计**: 后端服务无状态，支持水平扩展
- **负载均衡**: 支持多实例负载均衡
- **数据库扩展**: 支持读写分离和分库分表
- **缓存策略**: 使用Redis缓存提升性能

#### 5.3.2 功能扩展
- **插件架构**: 支持新的沙箱环境实现
- **协议扩展**: 支持MCP协议的版本升级
- **集成扩展**: 支持新的CI/CD平台集成
- **监控扩展**: 支持自定义监控指标

## 6. 浏览器兼容性和移动端适配

### 6.1 浏览器兼容性

#### 6.1.1 桌面浏览器支持
- **Chrome**: 版本90+（推荐）
- **Firefox**: 版本88+
- **Safari**: 版本14+
- **Edge**: 版本90+

#### 6.1.2 移动浏览器支持
- **Chrome Mobile**: 版本90+
- **Safari Mobile**: 版本14+
- **Firefox Mobile**: 版本88+

## 7. 开发和部署规范

### 7.1 代码规范

#### 7.1.1 后端代码规范
- **Java代码风格**: 遵循Google Java Style Guide
- **包命名**: 使用com.example.springvueapp作为根包
- **类命名**: 使用PascalCase，接口以I开头
- **方法命名**: 使用camelCase，布尔方法以is/has开头
- **注释规范**: 使用JavaDoc注释公共API
- **异常处理**: 使用统一的异常处理机制
- **文件大小**: 后端代码文件大小不超过800行

#### 7.1.2 前端代码规范
- **TypeScript**: 启用严格模式，完整类型检查
- **Vue组件**: 使用Composition API和`<script setup>`语法
- **命名规范**: 组件使用PascalCase，文件使用kebab-case
- **样式规范**: 使用Tailwind CSS，避免自定义CSS
- **ESLint配置**: 使用Vue官方ESLint配置
- **文件大小**: 前端代码文件大小不超过800行

### 7.2 测试策略

#### 7.2.1 后端测试
- **单元测试**: 使用JUnit 5和Mockito，覆盖率≥80%
- **集成测试**: 使用@SpringBootTest测试完整流程
- **API测试**: 使用WebTestClient测试REST API
- **容器测试**: 使用Testcontainers测试Docker集成

#### 7.2.2 前端测试
- **单元测试**: 使用Vitest和Vue Test Utils
- **组件测试**: 测试Vue组件的渲染和交互
- **E2E测试**: 使用Playwright测试用户流程
- **类型检查**: 使用vue-tsc进行TypeScript类型检查

---

## 总结

本PRD文档基于实际代码库分析，详细描述了完整产品需求。该平台采用现代化的技术栈，提供了完整的MCP协议代理功能和DevOps项目管理能力，具备良好的扩展性和安全性。

通过响应式架构设计和Docker容器化部署，系统能够支持高并发访问和水平扩展。完善的测试策略和CI/CD流程确保了代码质量和交付效率。

该平台将为开发者和企业提供统一的AI工具管理解决方案，简化MCP服务器的集成和管理工作，提升开发效率和系统可靠性。
