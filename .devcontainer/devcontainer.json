{"name": "GraalVM Native Development with <PERSON><PERSON>", "build": {"dockerfile": "Dockerfile"}, "customizations": {"vscode": {"extensions": ["vscjava.vscode-maven", "redhat.java"]}}, "postCreateCommand": "echo '✅ DevContainer is ready!' && java --version && mvn -v && git config --global --add safe.directory /workspace", "mounts": ["source=${localWorkspaceFolder},target=/workspace,type=bind", "source=${env:HOME}${env:USERPROFILE}/.m2,target=/root/.m2,type=bind"], "workspaceFolder": "/workspace"}