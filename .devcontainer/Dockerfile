FROM mirrors-ssl.aliyuncs.com/ubuntu:22.04

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        wget \
        curl \
        gnupg \
        git \
        zip \
        unzip \
        software-properties-common build-essential zlib1g-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

COPY graalvm-community-jdk-21.0.2_linux-x64_bin.tar.gz /tmp/

RUN cd /tmp && \
    tar -xzf graalvm-community-jdk-21.0.2_linux-x64_bin.tar.gz -C /usr/local && \
    rm -rf graalvm-community-jdk-21.0.2_linux-x64_bin.tar.gz

#RUN --mount=type=tmpfs,target=/etc \
#    echo "$GITHUB_IP github.com" >> /etc/hosts \
#    cd /tmp && \
#    wget --no-check-certificate https://github.com/graalvm/graalvm-ce-builds/releases/download/jdk-21.0.2/graalvm-community-jdk-21.0.2_linux-x64_bin.tar.gz  && \
#    tar -xzf graalvm-community-jdk-21.0.2_linux-x64_bin.tar.gz -C /usr/local && \
#    rm -rf graalvm-community-jdk-21.0.2_linux-x64_bin.tar.gz

ENV JAVA_HOME=/usr/local/graalvm-community-openjdk-21.0.2+13.1
ENV PATH=$JAVA_HOME/bin:$PATH

# RUN gu install native-image
