package com.example.springvueapp.mcp.service;

import com.example.springvueapp.mcp.entity.McpServerInstanceEntity;
import com.example.springvueapp.mcp.mapper.McpInstanceMapper;
import com.example.springvueapp.mcp.model.McpServerConfiguration;
import com.example.springvueapp.mcp.model.McpServerInstance;
import com.example.springvueapp.mcp.repository.McpServerInstanceRepository;
import com.example.springvueapp.sandbox.SandboxConfig;
import com.example.springvueapp.sandbox.SandboxEnvironment;
import com.example.springvueapp.sandbox.SandboxInstance;
import com.example.springvueapp.sandbox.SandboxStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class McpProxyServiceTest {
    
    @Mock
    private SandboxEnvironment sandboxEnvironment;

    @Mock
    private McpServerInstanceRepository instanceRepository;

    @Mock
    private McpInstanceMapper instanceMapper;

    @Mock
    private SandboxInstance sandboxInstance;

    private McpProxyService mcpProxyService;

    @BeforeEach
    void setUp() {
        mcpProxyService = new McpProxyService(sandboxEnvironment, instanceRepository, instanceMapper);
    }
    
    @Test
    void testStartServer() {
        // Given
        McpServerConfiguration config = McpServerConfiguration.builder()
                .id(1L)
                .name("test-server")
                .command("node")
                .dockerImage("node:18-alpine")
                .build();
        
        Long userId = 1L;
        String sandboxId = "test-sandbox-123";
        
        when(sandboxEnvironment.createSandbox(any(SandboxConfig.class)))
                .thenReturn(Mono.just(sandboxInstance));
        when(sandboxInstance.getId()).thenReturn(sandboxId);
        when(sandboxInstance.start()).thenReturn(Mono.empty());

        when(sandboxInstance.getStdoutFlux()).thenReturn(Flux.empty());
        when(sandboxInstance.getStderrFlux()).thenReturn(Flux.empty());
        when(sandboxInstance.getStderrFlux()).thenReturn(Flux.empty());

        when(sandboxEnvironment.getType()).thenReturn("docker");

        // Mock mapper behavior
        McpServerInstanceEntity savedEntity = createTestInstanceEntity(sandboxId);
        savedEntity.setStatus(SandboxStatus.RUNNING);
        savedEntity.setStartedAt(LocalDateTime.now());

        when(instanceMapper.toEntity(any())).thenReturn(savedEntity);

        // Create expected DTO result
        McpServerInstance expectedDto = McpServerInstance.builder()
                .id(1L)
                .configurationId(1L)
                .sandboxId(sandboxId)
                .status(SandboxStatus.RUNNING)
                .sandboxType("docker")
                .userId(1L)
                .startedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .build();

        when(instanceMapper.toDto(any())).thenReturn(expectedDto);

        when(instanceRepository.save(any(McpServerInstanceEntity.class)))
                .thenReturn(Mono.just(savedEntity));

        // Mock findByConfigurationId to return empty (no existing instance)
        when(instanceRepository.findByConfigurationId(config.getId()))
                .thenReturn(Mono.empty());
        
        // When & Then
        StepVerifier.create(mcpProxyService.startServer(config, userId))
                .expectNextMatches(instance -> 
                        instance.getSandboxId().equals(sandboxId) &&
                        instance.getConfigurationId().equals(config.getId()) &&
                        instance.getUserId().equals(userId) &&
                        instance.getStatus() == SandboxStatus.RUNNING
                )
                .verifyComplete();
        
        verify(sandboxEnvironment).createSandbox(any(SandboxConfig.class));
        verify(sandboxInstance).start();
        verify(instanceRepository, times(2)).save(any(McpServerInstanceEntity.class));
    }
    
    @Test
    void testStopServer() {
        // Given
        String sandboxId = "test-sandbox-123";
        
        when(instanceRepository.findBySandboxId(sandboxId))
                .thenReturn(Mono.just(createTestInstanceEntity(sandboxId)));
        when(sandboxEnvironment.getSandbox(sandboxId))
                .thenReturn(Mono.just(sandboxInstance));
        when(sandboxInstance.stop()).thenReturn(Mono.empty());
        when(sandboxEnvironment.destroySandbox(sandboxId)).thenReturn(Mono.empty());
        when(instanceRepository.save(any(McpServerInstanceEntity.class)))
                .thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));
        
        // When & Then
        StepVerifier.create(mcpProxyService.stopServer(sandboxId))
                .verifyComplete();
        
        verify(sandboxInstance).stop();
        verify(sandboxEnvironment).destroySandbox(sandboxId);
        verify(instanceRepository).save(any(McpServerInstanceEntity.class));
    }
    
    private McpServerInstanceEntity createTestInstanceEntity(String sandboxId) {
        return McpServerInstanceEntity.builder()
                .id(1L)
                .configurationId(1L)
                .sandboxId(sandboxId)
                .status(SandboxStatus.RUNNING)
                .sandboxType("docker")
                .userId(1L)
                .startedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .build();
    }

}
