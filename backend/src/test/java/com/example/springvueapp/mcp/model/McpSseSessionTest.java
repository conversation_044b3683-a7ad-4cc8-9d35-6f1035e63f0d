package com.example.springvueapp.mcp.model;

import com.example.springvueapp.mcp.client.McpClient;
import com.example.springvueapp.sandbox.SandboxInstance;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * McpSseSession单元测试
 */
@ExtendWith(MockitoExtension.class)
class McpSseSessionTest {

    @Mock
    private SandboxInstance sandboxInstance;

    @Mock
    private McpClient mcpClient;

    private McpSseSession session;

    @BeforeEach
    void setUp() {
        session = new McpSseSession("test-session", "test-server", 1L);
    }

    @Test
    void testSessionCreation() {
        // 验证初始状态
        assertEquals("test-session", session.getSessionId());
        assertEquals("test-server", session.getMcpServerName());
        assertEquals(1L, session.getUserId());
        assertEquals(McpSseSession.SessionStatus.INITIALIZING, session.getStatus());
        assertNotNull(session.getCreatedAt());
        assertNotNull(session.getLastActiveAt());
        assertNotNull(session.getEventSink());
        assertNull(session.getSandboxInstance());
        assertNull(session.getMcpClient());
    }

    @Test
    void testStatusUpdate() {
        // 测试状态更新
        assertTrue(session.updateStatus(McpSseSession.SessionStatus.INITIALIZING, McpSseSession.SessionStatus.ACTIVE));
        assertEquals(McpSseSession.SessionStatus.ACTIVE, session.getStatus());
        assertTrue(session.isActive());
        assertFalse(session.isClosed());

        // 测试错误的状态更新
        assertFalse(session.updateStatus(McpSseSession.SessionStatus.INITIALIZING, McpSseSession.SessionStatus.CLOSED));
        assertEquals(McpSseSession.SessionStatus.ACTIVE, session.getStatus());
    }

    @Test
    void testSettersAndGetters() {
        // 测试设置沙箱实例
        session.setSandboxInstance(sandboxInstance);
        assertEquals(sandboxInstance, session.getSandboxInstance());

        // 测试设置MCP客户端
        session.setMcpClient(mcpClient);
        assertEquals(mcpClient, session.getMcpClient());

        // 测试更新活跃时间
        LocalDateTime beforeUpdate = session.getLastActiveAt();
        try {
            Thread.sleep(1); // 确保时间差异
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        session.updateLastActiveAt();
        assertTrue(session.getLastActiveAt().isAfter(beforeUpdate));
    }

    @Test
    void testSendEvent() {
        // 设置会话为活跃状态
        session.setStatus(McpSseSession.SessionStatus.ACTIVE);

        // 发送事件
        String testData = "test data";
        session.sendEvent(SseEventType.MESSAGE_EVENT_TYPE, testData);

        // 验证事件被发送
        StepVerifier.create(session.getEventSink().asFlux().take(1))
                .assertNext(event -> {
                    assertEquals("message", event.event());
                    assertEquals(testData, event.data());
                })
                .verifyComplete();
    }

    @Test
    void testSendEventWhenClosed() {
        // 设置会话为关闭状态
        session.setStatus(McpSseSession.SessionStatus.CLOSED);

        // 尝试发送事件
        session.sendEvent(SseEventType.MESSAGE_EVENT_TYPE, "test data");

        // 验证事件不会被发送
        StepVerifier.create(session.getEventSink().asFlux().take(1))
                .expectTimeout(java.time.Duration.ofMillis(100))
                .verify();
    }

    @Test
    void testCloseSession() {
        // 设置会话为活跃状态并添加依赖
        session.setStatus(McpSseSession.SessionStatus.ACTIVE);
        session.setSandboxInstance(sandboxInstance);
        session.setMcpClient(mcpClient);

        // 模拟依赖的关闭行为
        when(mcpClient.close()).thenReturn(reactor.core.publisher.Mono.empty());
        when(sandboxInstance.destroy()).thenReturn(reactor.core.publisher.Mono.empty());

        // 关闭会话
        session.close();

        // 验证状态变化
        assertEquals(McpSseSession.SessionStatus.CLOSED, session.getStatus());
        assertTrue(session.isClosed());

        // 验证依赖被关闭
        verify(mcpClient).close();
        verify(sandboxInstance).destroy();

        // 验证事件流被完成
        StepVerifier.create(session.getEventSink().asFlux())
                .verifyComplete();
    }

    @Test
    void testCloseSessionFromInitializing() {
        // 从初始化状态关闭会话
        assertEquals(McpSseSession.SessionStatus.INITIALIZING, session.getStatus());
        
        session.close();
        
        // 验证状态变化
        assertEquals(McpSseSession.SessionStatus.CLOSED, session.getStatus());
        assertTrue(session.isClosed());
    }

    @Test
    void testCloseSessionAlreadyClosed() {
        // 设置会话为已关闭状态
        session.setStatus(McpSseSession.SessionStatus.CLOSED);
        session.setSandboxInstance(sandboxInstance);
        session.setMcpClient(mcpClient);

        // 尝试再次关闭
        session.close();

        // 验证依赖不会被重复关闭
        verifyNoInteractions(mcpClient);
        verifyNoInteractions(sandboxInstance);
    }
}
