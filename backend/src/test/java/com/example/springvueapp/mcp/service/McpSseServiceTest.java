package com.example.springvueapp.mcp.service;

import com.example.springvueapp.mcp.model.McpSseSession;
import com.example.springvueapp.mcp.model.McpServerConfiguration;
import com.example.springvueapp.mcp.model.McpServerInstance;
import com.example.springvueapp.sandbox.SandboxEnvironment;
import com.example.springvueapp.sandbox.SandboxInstance;
import com.example.springvueapp.sandbox.SandboxStatus;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * McpSseService单元测试
 */
@ExtendWith(MockitoExtension.class)
class McpSseServiceTest {

    @Mock
    private McpConfigurationService configurationService;

    @Mock
    private McpProxyService proxyService;

    @Mock
    private SandboxEnvironment sandboxEnvironment;

    @Mock
    private SandboxInstance sandboxInstance;

    private ObjectMapper objectMapper;
    private McpSseService sseService;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        sseService = new McpSseService(configurationService, proxyService, sandboxEnvironment, objectMapper);
    }

    @Test
    void testCreateSession_Success() {
        // 准备测试数据
        String mcpServerName = "test-server";
        Long userId = 1L;
        
        McpServerConfiguration config = new McpServerConfiguration();
        config.setId(1L);
        config.setName(mcpServerName);
        config.setCommand("test-command");
        config.setDockerImage("test-image");
        
        McpServerInstance instance = new McpServerInstance();
        instance.setSandboxId("sandbox-123");
        instance.setStatus(SandboxStatus.RUNNING);

        // 模拟依赖行为
        when(configurationService.getConfigurationByName(mcpServerName, userId))
                .thenReturn(Mono.just(config));
        when(proxyService.startServer(config, userId))
                .thenReturn(Mono.just(instance));
        when(sandboxEnvironment.getSandbox("sandbox-123"))
                .thenReturn(Mono.just(sandboxInstance));

        // Mock SandboxInstance methods
        when(sandboxInstance.getStdoutFlux()).thenReturn(reactor.core.publisher.Flux.empty());
        when(sandboxInstance.getStderrFlux()).thenReturn(reactor.core.publisher.Flux.empty());

        // 执行测试
        Mono<Flux<ServerSentEvent<String>>> result = sseService.createSession(mcpServerName, userId);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(eventStream -> {
                    assertNotNull(eventStream);
                    // 验证事件流不为空
                    StepVerifier.create(eventStream.take(1))
                            .expectNextCount(1)
                            .thenCancel()
                            .verify();
                })
                .verifyComplete();

        // 验证方法调用
        verify(configurationService).getConfigurationByName(mcpServerName, userId);
        verify(proxyService).startServer(config, userId);
        verify(sandboxEnvironment).getSandbox("sandbox-123");
    }

    @Test
    void testCreateSession_ConfigurationNotFound() {
        // 准备测试数据
        String mcpServerName = "non-existent-server";
        Long userId = 1L;

        // 模拟配置不存在
        when(configurationService.getConfigurationByName(mcpServerName, userId))
                .thenReturn(Mono.empty());

        // 执行测试
        Mono<Flux<ServerSentEvent<String>>> result = sseService.createSession(mcpServerName, userId);

        // 验证结果
        StepVerifier.create(result)
                .expectErrorMatches(throwable -> 
                    throwable instanceof RuntimeException && 
                    throwable.getMessage().contains("MCP server configuration not found"))
                .verify();

        // 验证方法调用
        verify(configurationService).getConfigurationByName(mcpServerName, userId);
        verifyNoInteractions(proxyService);
        verifyNoInteractions(sandboxEnvironment);
    }

    @Test
    void testSendMessage_SessionNotFound() {
        // 准备测试数据
        String sessionId = "non-existent-session";
        Object message = Map.of("method", "test", "params", Map.of());

        // 执行测试
        Mono<Void> result = sseService.sendMessage(sessionId, message);

        // 验证结果
        StepVerifier.create(result)
                .expectErrorMatches(throwable -> 
                    throwable instanceof RuntimeException && 
                    throwable.getMessage().contains("Session not found"))
                .verify();
    }

    @Test
    void testCloseSession() {
        // 准备测试数据
        String sessionId = "test-session";

        // 执行测试
        Mono<Void> result = sseService.closeSession(sessionId);

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void testGetActiveSessionCount() {
        // 执行测试
        int count = sseService.getActiveSessionCount();

        // 验证结果
        assertEquals(0, count);
    }

    @Test
    void testGetUserSessions() {
        // 准备测试数据
        Long userId = 1L;

        // 执行测试
        Map<String, McpSseSession> sessions = sseService.getUserSessions(userId);

        // 验证结果
        assertNotNull(sessions);
        assertTrue(sessions.isEmpty());
    }
}
