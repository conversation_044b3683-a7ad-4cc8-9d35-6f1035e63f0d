package com.example.springvueapp.mcp.mapper;

import com.example.springvueapp.mcp.entity.McpServerConfigurationEntity;
import com.example.springvueapp.sandbox.model.HostEntry;
import com.example.springvueapp.mcp.model.McpServerConfiguration;
import com.example.springvueapp.sandbox.model.VolumeMount;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MCP配置映射器的单元测试
 * 重点测试Volume挂载和Host域名解析配置的序列化和反序列化
 */
@ExtendWith(MockitoExtension.class)
class McpConfigurationMapperTest {

    private McpConfigurationMapper mapper;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mapper = new McpConfigurationMapper(objectMapper);
    }

    @Test
    void testToDtoWithVolumeMounts() {
        // 准备Entity数据
        McpServerConfigurationEntity entity = McpServerConfigurationEntity.builder()
                .id(1L)
                .name("test-config")
                .description("测试配置")
                .command("python")
                .dockerImage("python:3.9")
                .volumeMounts("[{\"hostPath\":\"/host/data\",\"containerPath\":\"/container/data\",\"readOnly\":false},{\"hostPath\":\"/host/logs\",\"containerPath\":\"/container/logs\",\"readOnly\":true}]")
                .userId(100L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // 执行转换
        McpServerConfiguration dto = mapper.toDto(entity);

        // 验证基本字段
        assertNotNull(dto);
        assertEquals(1L, dto.getId());
        assertEquals("test-config", dto.getName());
        assertEquals("测试配置", dto.getDescription());
        assertEquals("python", dto.getCommand());
        assertEquals("python:3.9", dto.getDockerImage());
        assertEquals(100L, dto.getUserId());

        // 验证Volume挂载
        List<VolumeMount> volumeMounts = dto.getVolumeMounts();
        assertNotNull(volumeMounts);
        assertEquals(2, volumeMounts.size());

        VolumeMount mount1 = volumeMounts.get(0);
        assertEquals("/host/data", mount1.getHostPath());
        assertEquals("/container/data", mount1.getContainerPath());
        assertFalse(mount1.getReadOnly());

        VolumeMount mount2 = volumeMounts.get(1);
        assertEquals("/host/logs", mount2.getHostPath());
        assertEquals("/container/logs", mount2.getContainerPath());
        assertTrue(mount2.getReadOnly());
    }

    @Test
    void testToDtoWithHostEntries() {
        // 准备Entity数据
        McpServerConfigurationEntity entity = McpServerConfigurationEntity.builder()
                .id(2L)
                .name("test-config-host")
                .command("node")
                .dockerImage("node:16")
                .hostEntries("[{\"hostname\":\"example.com\",\"ipAddress\":\"*************\"},{\"hostname\":\"api.example.com\",\"ipAddress\":\"*************\"}]")
                .userId(200L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // 执行转换
        McpServerConfiguration dto = mapper.toDto(entity);

        // 验证基本字段
        assertNotNull(dto);
        assertEquals(2L, dto.getId());
        assertEquals("test-config-host", dto.getName());
        assertEquals("node", dto.getCommand());
        assertEquals("node:16", dto.getDockerImage());
        assertEquals(200L, dto.getUserId());

        // 验证Host条目
        List<HostEntry> hostEntries = dto.getHostEntries();
        assertNotNull(hostEntries);
        assertEquals(2, hostEntries.size());

        HostEntry entry1 = hostEntries.get(0);
        assertEquals("example.com", entry1.getHostname());
        assertEquals("*************", entry1.getIpAddress());

        HostEntry entry2 = hostEntries.get(1);
        assertEquals("api.example.com", entry2.getHostname());
        assertEquals("*************", entry2.getIpAddress());
    }

    @Test
    void testToEntityWithVolumeMounts() {
        // 准备DTO数据
        VolumeMount mount1 = new VolumeMount("/host/app", "/container/app", false);
        VolumeMount mount2 = new VolumeMount("/host/config", "/container/config", true);

        McpServerConfiguration dto = McpServerConfiguration.builder()
                .id(3L)
                .name("test-dto-volume")
                .command("java")
                .dockerImage("openjdk:11")
                .volumeMounts(Arrays.asList(mount1, mount2))
                .userId(300L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // 执行转换
        McpServerConfigurationEntity entity = mapper.toEntity(dto);

        // 验证基本字段
        assertNotNull(entity);
        assertEquals(3L, entity.getId());
        assertEquals("test-dto-volume", entity.getName());
        assertEquals("java", entity.getCommand());
        assertEquals("openjdk:11", entity.getDockerImage());
        assertEquals(300L, entity.getUserId());

        // 验证Volume挂载JSON序列化
        String volumeMountsJson = entity.getVolumeMounts();
        assertNotNull(volumeMountsJson);
        assertTrue(volumeMountsJson.contains("/host/app"));
        assertTrue(volumeMountsJson.contains("/container/app"));
        assertTrue(volumeMountsJson.contains("/host/config"));
        assertTrue(volumeMountsJson.contains("/container/config"));
        assertTrue(volumeMountsJson.contains("\"readOnly\":false"));
        assertTrue(volumeMountsJson.contains("\"readOnly\":true"));
    }

    @Test
    void testToEntityWithHostEntries() {
        // 准备DTO数据
        HostEntry entry1 = new HostEntry("database.local", "********");
        HostEntry entry2 = new HostEntry("cache.local", "********");

        McpServerConfiguration dto = McpServerConfiguration.builder()
                .id(4L)
                .name("test-dto-host")
                .command("go")
                .dockerImage("golang:1.19")
                .hostEntries(Arrays.asList(entry1, entry2))
                .userId(400L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // 执行转换
        McpServerConfigurationEntity entity = mapper.toEntity(dto);

        // 验证基本字段
        assertNotNull(entity);
        assertEquals(4L, entity.getId());
        assertEquals("test-dto-host", entity.getName());
        assertEquals("go", entity.getCommand());
        assertEquals("golang:1.19", entity.getDockerImage());
        assertEquals(400L, entity.getUserId());

        // 验证Host条目JSON序列化
        String hostEntriesJson = entity.getHostEntries();
        assertNotNull(hostEntriesJson);
        assertTrue(hostEntriesJson.contains("database.local"));
        assertTrue(hostEntriesJson.contains("********"));
        assertTrue(hostEntriesJson.contains("cache.local"));
        assertTrue(hostEntriesJson.contains("********"));
    }

    @Test
    void testRoundTripConversion() {
        // 准备完整的DTO数据
        VolumeMount mount = new VolumeMount("/host/shared", "/container/shared", false);
        HostEntry entry = new HostEntry("service.local", "***********");

        McpServerConfiguration originalDto = McpServerConfiguration.builder()
                .id(5L)
                .name("round-trip-test")
                .description("往返转换测试")
                .command("python")
                .dockerImage("python:3.9-slim")
                .volumeMounts(Arrays.asList(mount))
                .hostEntries(Arrays.asList(entry))
                .userId(500L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // DTO -> Entity -> DTO
        McpServerConfigurationEntity entity = mapper.toEntity(originalDto);
        McpServerConfiguration convertedDto = mapper.toDto(entity);

        // 验证往返转换后数据一致性
        assertNotNull(convertedDto);
        assertEquals(originalDto.getId(), convertedDto.getId());
        assertEquals(originalDto.getName(), convertedDto.getName());
        assertEquals(originalDto.getDescription(), convertedDto.getDescription());
        assertEquals(originalDto.getCommand(), convertedDto.getCommand());
        assertEquals(originalDto.getDockerImage(), convertedDto.getDockerImage());
        assertEquals(originalDto.getUserId(), convertedDto.getUserId());

        // 验证Volume挂载
        List<VolumeMount> convertedMounts = convertedDto.getVolumeMounts();
        assertNotNull(convertedMounts);
        assertEquals(1, convertedMounts.size());
        VolumeMount convertedMount = convertedMounts.get(0);
        assertEquals("/host/shared", convertedMount.getHostPath());
        assertEquals("/container/shared", convertedMount.getContainerPath());
        assertFalse(convertedMount.getReadOnly());

        // 验证Host条目
        List<HostEntry> convertedEntries = convertedDto.getHostEntries();
        assertNotNull(convertedEntries);
        assertEquals(1, convertedEntries.size());
        HostEntry convertedEntry = convertedEntries.get(0);
        assertEquals("service.local", convertedEntry.getHostname());
        assertEquals("***********", convertedEntry.getIpAddress());
    }

    @Test
    void testToDtoWithNullVolumeMounts() {
        // 测试null Volume挂载
        McpServerConfigurationEntity entity = McpServerConfigurationEntity.builder()
                .id(6L)
                .name("null-volume-test")
                .command("bash")
                .dockerImage("ubuntu:20.04")
                .volumeMounts(null)
                .userId(600L)
                .build();

        McpServerConfiguration dto = mapper.toDto(entity);

        assertNotNull(dto);
        assertNull(dto.getVolumeMounts());
    }

    @Test
    void testToDtoWithEmptyHostEntries() {
        // 测试空字符串Host条目
        McpServerConfigurationEntity entity = McpServerConfigurationEntity.builder()
                .id(7L)
                .name("empty-host-test")
                .command("sh")
                .dockerImage("alpine:latest")
                .hostEntries("")
                .userId(700L)
                .build();

        McpServerConfiguration dto = mapper.toDto(entity);

        assertNotNull(dto);
        assertNull(dto.getHostEntries());
    }

    @Test
    void testToDtoWithInvalidJson() {
        // 测试无效JSON
        McpServerConfigurationEntity entity = McpServerConfigurationEntity.builder()
                .id(8L)
                .name("invalid-json-test")
                .command("echo")
                .dockerImage("busybox:latest")
                .volumeMounts("{invalid json}")
                .hostEntries("[{\"hostname\":\"incomplete\"}")
                .userId(800L)
                .build();

        McpServerConfiguration dto = mapper.toDto(entity);

        // 应该能够处理无效JSON，返回null而不是抛出异常
        assertNotNull(dto);
        assertNull(dto.getVolumeMounts());
        assertNull(dto.getHostEntries());
    }

    @Test
    void testToEntityWithNullCollections() {
        // 测试null集合
        McpServerConfiguration dto = McpServerConfiguration.builder()
                .id(9L)
                .name("null-collections-test")
                .command("test")
                .dockerImage("test:latest")
                .volumeMounts(null)
                .hostEntries(null)
                .userId(900L)
                .build();

        McpServerConfigurationEntity entity = mapper.toEntity(dto);

        assertNotNull(entity);
        assertNull(entity.getVolumeMounts());
        assertNull(entity.getHostEntries());
    }
}
