package com.example.springvueapp.devops.mapper;

import com.example.springvueapp.devops.entity.DevOpsCdTaskEntity;
import com.example.springvueapp.devops.model.DevOpsCdTask;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DevOpsCdTaskMapper的单元测试
 */
class DevOpsCdTaskMapperTest {

    private ObjectMapper objectMapper;
    private DevOpsCdTaskMapper mapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mapper = new DevOpsCdTaskMapper(objectMapper);
    }

    @Test
    void testToModel_WithValidEntity_ShouldReturnModel() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        
        DevOpsCdTaskEntity entity = DevOpsCdTaskEntity.builder()
                .id(1L)
                .name("test-task")
                .description("测试任务")
                .applicationId(10L)
                .componentVersions("{\"component1\":\"v1.0.0\"}")
                .configuration("{\"deploymentStrategy\":\"rolling\"}")
                .status("ACTIVE")
                .userId(100L)
                .createdAt(now)
                .updatedAt(now)
                .build();

        // When
        DevOpsCdTask result = mapper.toModel(entity);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("test-task", result.getName());
        assertEquals("测试任务", result.getDescription());
        assertEquals(10L, result.getApplicationId());
        assertEquals("v1.0.0", result.getComponentVersions().get("component1"));
        assertEquals("rolling", result.getConfiguration().get("deploymentStrategy"));
        assertEquals("ACTIVE", result.getStatus());
        assertEquals(100L, result.getUserId());
        assertEquals(now, result.getCreatedAt());
        assertEquals(now, result.getUpdatedAt());
    }

    @Test
    void testToModel_WithNullComponentVersions_ShouldReturnEmptyMap() {
        // Given
        DevOpsCdTaskEntity entity = DevOpsCdTaskEntity.builder()
                .id(1L)
                .name("test-task")
                .componentVersions(null)
                .configuration(null)
                .build();

        // When
        DevOpsCdTask result = mapper.toModel(entity);

        // Then
        assertNotNull(result);
        assertNotNull(result.getComponentVersions());
        assertTrue(result.getComponentVersions().isEmpty());
        assertNotNull(result.getConfiguration());
        assertTrue(result.getConfiguration().isEmpty());
    }

    @Test
    void testToEntity_WithValidModel_ShouldReturnEntity() {
        // Given
        Map<String, String> componentVersions = new HashMap<>();
        componentVersions.put("component1", "v1.0.0");
        
        Map<String, Object> configuration = new HashMap<>();
        configuration.put("deploymentStrategy", "rolling");

        DevOpsCdTask model = DevOpsCdTask.builder()
                .id(1L)
                .name("test-task")
                .description("测试任务")
                .applicationId(10L)
                .componentVersions(componentVersions)
                .configuration(configuration)
                .status("ACTIVE")
                .userId(100L)
                .build();

        // When
        DevOpsCdTaskEntity result = mapper.toEntity(model);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("test-task", result.getName());
        assertEquals("测试任务", result.getDescription());
        assertEquals(10L, result.getApplicationId());
        assertEquals("ACTIVE", result.getStatus());
        assertEquals(100L, result.getUserId());
        assertTrue(result.getComponentVersions().contains("component1"));
        assertTrue(result.getConfiguration().contains("deploymentStrategy"));
    }

    @Test
    void testToNewEntity_WithValidModelAndIds_ShouldReturnNewEntity() {
        // Given
        Map<String, String> componentVersions = new HashMap<>();
        componentVersions.put("component1", "v1.0.0");
        
        Map<String, Object> configuration = new HashMap<>();
        configuration.put("deploymentStrategy", "rolling");

        DevOpsCdTask model = DevOpsCdTask.builder()
                .name("test-task")
                .description("测试任务")
                .componentVersions(componentVersions)
                .configuration(configuration)
                .build();

        Long applicationId = 10L;
        Long userId = 100L;

        // When
        DevOpsCdTaskEntity result = mapper.toNewEntity(model, applicationId, userId);

        // Then
        assertNotNull(result);
        assertNull(result.getId()); // 新实体不应该有ID
        assertEquals("test-task", result.getName());
        assertEquals("测试任务", result.getDescription());
        assertEquals(applicationId, result.getApplicationId());
        assertEquals("INACTIVE", result.getStatus()); // 默认状态
        assertEquals(userId, result.getUserId());
        assertNotNull(result.getCreatedAt());
        assertNotNull(result.getUpdatedAt());
        assertTrue(result.getComponentVersions().contains("component1"));
        assertTrue(result.getConfiguration().contains("deploymentStrategy"));
    }

    @Test
    void testToUpdateEntity_WithValidModelAndExistingEntity_ShouldReturnUpdatedEntity() {
        // Given
        LocalDateTime originalTime = LocalDateTime.now().minusHours(1);
        
        DevOpsCdTaskEntity existingEntity = DevOpsCdTaskEntity.builder()
                .id(1L)
                .name("old-task")
                .description("旧任务")
                .applicationId(10L)
                .componentVersions("{\"component1\":\"v0.9.0\"}")
                .configuration("{\"deploymentStrategy\":\"blue-green\"}")
                .status("ACTIVE")
                .userId(100L)
                .createdAt(originalTime)
                .updatedAt(originalTime)
                .build();

        Map<String, String> newComponentVersions = new HashMap<>();
        newComponentVersions.put("component1", "v1.0.0");
        
        Map<String, Object> newConfiguration = new HashMap<>();
        newConfiguration.put("deploymentStrategy", "rolling");

        DevOpsCdTask updateModel = DevOpsCdTask.builder()
                .name("updated-task")
                .description("更新的任务")
                .componentVersions(newComponentVersions)
                .configuration(newConfiguration)
                .build();

        // When
        DevOpsCdTaskEntity result = mapper.toUpdateEntity(updateModel, existingEntity);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId()); // ID保持不变
        assertEquals("updated-task", result.getName());
        assertEquals("更新的任务", result.getDescription());
        assertEquals(10L, result.getApplicationId()); // 应用ID保持不变
        assertTrue(result.getComponentVersions().contains("v1.0.0"));
        assertTrue(result.getConfiguration().contains("rolling"));
        assertEquals("ACTIVE", result.getStatus()); // 状态保持不变
        assertEquals(100L, result.getUserId()); // 用户ID保持不变
        assertEquals(originalTime, result.getCreatedAt()); // 创建时间保持不变
        assertNotNull(result.getUpdatedAt());
        assertTrue(result.getUpdatedAt().isAfter(originalTime)); // 更新时间应该更新
    }

    @Test
    void testToModel_WithInvalidJson_ShouldHandleGracefully() {
        // Given
        DevOpsCdTaskEntity entity = DevOpsCdTaskEntity.builder()
                .id(1L)
                .name("test-task")
                .componentVersions("invalid-json")
                .configuration("invalid-json")
                .build();

        // When
        DevOpsCdTask result = mapper.toModel(entity);

        // Then
        assertNotNull(result);
        assertNotNull(result.getComponentVersions());
        assertTrue(result.getComponentVersions().isEmpty());
        assertNotNull(result.getConfiguration());
        assertTrue(result.getConfiguration().isEmpty());
    }

    @Test
    void testToEntity_WithNullValues_ShouldHandleGracefully() {
        // Given
        DevOpsCdTask model = DevOpsCdTask.builder()
                .name("test-task")
                .componentVersions(null)
                .configuration(null)
                .build();

        // When
        DevOpsCdTaskEntity result = mapper.toEntity(model);

        // Then
        assertNotNull(result);
        assertEquals("test-task", result.getName());
        assertEquals("{}", result.getComponentVersions());
        assertEquals("{}", result.getConfiguration());
    }
}
