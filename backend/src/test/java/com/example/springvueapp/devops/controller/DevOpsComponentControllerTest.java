package com.example.springvueapp.devops.controller;

import com.example.springvueapp.common.config.TestSecurityConfig;
import com.example.springvueapp.devops.model.DevOpsComponent;
import com.example.springvueapp.devops.service.DevOpsComponentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@WebFluxTest(DevOpsComponentController.class)
@Import(TestSecurityConfig.class)
class DevOpsComponentControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private DevOpsComponentService componentService;

    private DevOpsComponent testComponent;

    @BeforeEach
    void setUp() {
        testComponent = new DevOpsComponent();
        testComponent.setId(1L);
        testComponent.setName("测试组件");
        testComponent.setDescription("这是一个测试组件");
        testComponent.setApplicationId(1L);
        testComponent.setRepositoryUrl("https://github.com/test/repo.git");
        testComponent.setRepositoryType("GIT");
        testComponent.setStatus("ACTIVE");
        testComponent.setUserId(1L);
        testComponent.setCreatedAt(LocalDateTime.now());
        testComponent.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void createComponent_ShouldReturnCreatedComponent() throws Exception {
        // Given
        DevOpsComponent newComponent = new DevOpsComponent();
        newComponent.setName("新组件");
        newComponent.setDescription("新组件描述");
        newComponent.setRepositoryUrl("https://github.com/test/new-repo.git");
        newComponent.setStatus("ACTIVE");

        when(componentService.createComponent(any(DevOpsComponent.class), eq(1L), anyLong()))
                .thenReturn(Mono.just(testComponent));

        // When & Then
        webTestClient.post()
                .uri("/api/devops/components/application/1")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(newComponent)
                .exchange()
                .expectStatus().isCreated()
                .expectBody(DevOpsComponent.class)
                .value(component -> {
                    assert component.getId().equals(1L);
                    assert component.getName().equals("测试组件");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getAllComponents_ShouldReturnComponentList() {
        // Given
        when(componentService.getAllComponents(anyLong()))
                .thenReturn(Flux.just(testComponent));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/components")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsComponent.class)
                .hasSize(1)
                .value(components -> {
                    assert components.get(0).getName().equals("测试组件");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getComponentsByApplication_ShouldReturnComponentList() {
        // Given
        when(componentService.getComponentsByApplication(eq(1L), anyLong()))
                .thenReturn(Flux.just(testComponent));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/components/application/1")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsComponent.class)
                .hasSize(1)
                .value(components -> {
                    assert components.get(0).getApplicationId().equals(1L);
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getComponentById_ShouldReturnComponent() {
        // Given
        when(componentService.getComponentById(eq(1L), anyLong()))
                .thenReturn(Mono.just(testComponent));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/components/1")
                .exchange()
                .expectStatus().isOk()
                .expectBody(DevOpsComponent.class)
                .value(component -> {
                    assert component.getId().equals(1L);
                    assert component.getName().equals("测试组件");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getComponentById_WhenNotFound_ShouldReturn404() {
        // Given
        when(componentService.getComponentById(eq(999L), anyLong()))
                .thenReturn(Mono.error(new IllegalArgumentException("Component not found")));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/components/999")
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void updateComponent_ShouldReturnUpdatedComponent() throws Exception {
        // Given
        DevOpsComponent updatedComponent = new DevOpsComponent();
        updatedComponent.setName("更新的组件");
        updatedComponent.setDescription("更新的描述");
        updatedComponent.setStatus("INACTIVE");

        DevOpsComponent returnComponent = new DevOpsComponent();
        returnComponent.setId(1L);
        returnComponent.setName("更新的组件");
        returnComponent.setDescription("更新的描述");
        returnComponent.setStatus("INACTIVE");
        returnComponent.setUserId(1L);

        when(componentService.updateComponent(eq(1L), any(DevOpsComponent.class), anyLong()))
                .thenReturn(Mono.just(returnComponent));

        // When & Then
        webTestClient.put()
                .uri("/api/devops/components/1")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(updatedComponent)
                .exchange()
                .expectStatus().isOk()
                .expectBody(DevOpsComponent.class)
                .value(component -> {
                    assert component.getName().equals("更新的组件");
                    assert component.getStatus().equals("INACTIVE");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void deleteComponent_ShouldReturnNoContent() {
        // Given
        when(componentService.deleteComponent(eq(1L), anyLong()))
                .thenReturn(Mono.just(true));

        // When & Then
        webTestClient.delete()
                .uri("/api/devops/components/1")
                .exchange()
                .expectStatus().isNoContent();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void deleteComponent_WhenNotFound_ShouldReturn404() {
        // Given
        when(componentService.deleteComponent(eq(999L), anyLong()))
                .thenReturn(Mono.error(new IllegalArgumentException("Component not found")));

        // When & Then
        webTestClient.delete()
                .uri("/api/devops/components/999")
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getComponentsByRepositoryType_ShouldReturnComponentList() {
        // Given
        when(componentService.getComponentsByRepositoryType(anyLong(), eq("GIT")))
                .thenReturn(Flux.just(testComponent));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/components/repository-type/GIT")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsComponent.class)
                .hasSize(1)
                .value(components -> {
                    assert components.get(0).getRepositoryType().equals("GIT");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void searchComponents_ShouldReturnMatchingComponents() {
        // Given
        when(componentService.searchComponents(anyLong(), eq("测试")))
                .thenReturn(Flux.just(testComponent));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/components/search?keyword=测试")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsComponent.class)
                .hasSize(1)
                .value(components -> {
                    assert components.get(0).getName().equals("测试组件");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void searchComponentsInApplication_ShouldReturnMatchingComponents() {
        // Given
        when(componentService.searchComponentsInApplication(eq(1L), anyLong(), eq("测试")))
                .thenReturn(Flux.just(testComponent));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/components/application/1/search?keyword=测试")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsComponent.class)
                .hasSize(1)
                .value(components -> {
                    assert components.get(0).getName().equals("测试组件");
                });
    }


}
