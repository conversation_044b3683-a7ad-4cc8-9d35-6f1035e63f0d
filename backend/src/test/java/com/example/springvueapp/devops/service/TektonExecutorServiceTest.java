package com.example.springvueapp.devops.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;

/**
 * TektonExecutorService的单元测试
 */
@ExtendWith(MockitoExtension.class)
class TektonExecutorServiceTest {

    private TektonExecutorService tektonExecutorService;

    @BeforeEach
    void setUp() {
        tektonExecutorService = new TektonExecutorService();
    }

    @Test
    void testSubmitDeploymentTask_WithValidInput_ShouldReturnTaskInfo() {
        // Given
        String taskName = "deploy-app";
        String deploymentCommand = "kubectl apply -f deployment.yaml";
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("namespace", "default");
        parameters.put("image", "app:v1.0.0");

        // When & Then
        StepVerifier.create(tektonExecutorService.submitDeploymentTask(taskName, deploymentCommand, parameters))
                .expectNextMatches(result -> {
                    return result.containsKey("taskId") &&
                           result.get("taskName").equals(taskName) &&
                           result.get("status").equals("SUBMITTED") &&
                           result.get("command").equals(deploymentCommand) &&
                           result.containsKey("submittedAt");
                })
                .verifyComplete();
    }

    @Test
    void testGetTaskStatus_WithValidTaskId_ShouldReturnStatus() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.getTaskStatus(taskId))
                .expectNextMatches(status -> {
                    return status.get("taskId").equals(taskId) &&
                           status.get("status").equals("RUNNING") &&
                           status.containsKey("progress") &&
                           status.containsKey("startTime") &&
                           status.containsKey("message");
                })
                .verifyComplete();
    }

    @Test
    void testStopTask_WithValidTaskId_ShouldReturnTrue() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.stopTask(taskId))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testCancelTask_WithValidTaskId_ShouldReturnTrue() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.cancelTask(taskId))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testGetTaskLogs_WithValidTaskId_ShouldReturnLogs() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.getTaskLogs(taskId))
                .expectNextMatches(logs -> {
                    return logs.contains(taskId) &&
                           logs.contains("开始部署任务") &&
                           logs.contains("部署进行中");
                })
                .verifyComplete();
    }

    @Test
    void testGetTaskResult_WithValidTaskId_ShouldReturnResult() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.getTaskResult(taskId))
                .expectNextMatches(result -> {
                    return result.get("taskId").equals(taskId) &&
                           result.get("status").equals("COMPLETED") &&
                           result.get("exitCode").equals(0) &&
                           result.containsKey("completedAt") &&
                           result.containsKey("duration") &&
                           result.containsKey("artifacts");
                })
                .verifyComplete();
    }

    @Test
    void testListTasks_WithValidNamespace_ShouldReturnTaskList() {
        // Given
        String namespace = "default";

        // When & Then
        StepVerifier.create(tektonExecutorService.listTasks(namespace))
                .expectNextCount(2)
                .verifyComplete();
    }

    @Test
    void testCleanupCompletedTasks_WithValidInput_ShouldReturnCleanedCount() {
        // Given
        String namespace = "default";
        int keepCount = 5;

        // When & Then
        StepVerifier.create(tektonExecutorService.cleanupCompletedTasks(namespace, keepCount))
                .expectNext(3)
                .verifyComplete();
    }

    @Test
    void testCreateNamespace_WithValidNamespace_ShouldReturnTrue() {
        // Given
        String namespace = "test-namespace";

        // When & Then
        StepVerifier.create(tektonExecutorService.createNamespace(namespace))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testDeleteNamespace_WithValidNamespace_ShouldReturnTrue() {
        // Given
        String namespace = "test-namespace";

        // When & Then
        StepVerifier.create(tektonExecutorService.deleteNamespace(namespace))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testListNamespaces_ShouldReturnNamespaceList() {
        // When & Then
        StepVerifier.create(tektonExecutorService.listNamespaces())
                .expectNext("default")
                .expectNext("tekton-pipelines")
                .expectNext("devops-prod")
                .expectNext("devops-staging")
                .verifyComplete();
    }

    @Test
    void testCheckConnection_ShouldReturnTrue() {
        // When & Then
        StepVerifier.create(tektonExecutorService.checkConnection())
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testGetExecutorInfo_ShouldReturnExecutorDetails() {
        // When & Then
        StepVerifier.create(tektonExecutorService.getExecutorInfo())
                .expectNextMatches(info -> {
                    return "Tekton".equals(info.get("executor")) &&
                           "v0.50.0".equals(info.get("version")) &&
                           "connected".equals(info.get("status")) &&
                           info.containsKey("capabilities");
                })
                .verifyComplete();
    }

    @Test
    void testValidateDeploymentCommand_WithValidCommand_ShouldReturnTrue() {
        // Given
        String validCommand = "kubectl apply -f deployment.yaml";

        // When & Then
        StepVerifier.create(tektonExecutorService.validateDeploymentCommand(validCommand))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testValidateDeploymentCommand_WithInvalidCommand_ShouldReturnFalse() {
        // Given
        String invalidCommand = "invalid command";

        // When & Then
        StepVerifier.create(tektonExecutorService.validateDeploymentCommand(invalidCommand))
                .expectNext(false)
                .verifyComplete();
    }

    @Test
    void testValidateDeploymentCommand_WithNullCommand_ShouldReturnFalse() {
        // When & Then
        StepVerifier.create(tektonExecutorService.validateDeploymentCommand(null))
                .expectNext(false)
                .verifyComplete();
    }

    @Test
    void testValidateDeploymentCommand_WithEmptyCommand_ShouldReturnFalse() {
        // When & Then
        StepVerifier.create(tektonExecutorService.validateDeploymentCommand(""))
                .expectNext(false)
                .verifyComplete();
    }

    @Test
    void testGetSupportedCommandTypes_ShouldReturnCommandTypes() {
        // When & Then
        StepVerifier.create(tektonExecutorService.getSupportedCommandTypes())
                .expectNext("kubectl")
                .expectNext("helm")
                .expectNext("docker")
                .expectNext("shell")
                .expectNext("tekton")
                .verifyComplete();
    }

    @Test
    void testMonitorTaskProgress_WithValidTaskId_ShouldReturnProgressStream() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.monitorTaskProgress(taskId))
                .expectNextCount(3)
                .verifyComplete();
    }

    @Test
    void testGetTaskMetrics_WithValidTaskId_ShouldReturnMetrics() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.getTaskMetrics(taskId))
                .expectNextMatches(metrics -> {
                    return metrics.get("taskId").equals(taskId) &&
                           metrics.containsKey("cpuUsage") &&
                           metrics.containsKey("memoryUsage") &&
                           metrics.containsKey("duration") &&
                           metrics.containsKey("networkIO");
                })
                .verifyComplete();
    }

    @Test
    void testRetryTask_WithValidTaskId_ShouldReturnRetryInfo() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.retryTask(taskId))
                .expectNextMatches(result -> {
                    return result.get("originalTaskId").equals(taskId) &&
                           result.containsKey("newTaskId") &&
                           result.get("status").equals("RETRYING") &&
                           result.containsKey("retriedAt");
                })
                .verifyComplete();
    }

    @Test
    void testPauseTask_WithValidTaskId_ShouldReturnTrue() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.pauseTask(taskId))
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    void testResumeTask_WithValidTaskId_ShouldReturnTrue() {
        // Given
        String taskId = "deploy-app-12345678";

        // When & Then
        StepVerifier.create(tektonExecutorService.resumeTask(taskId))
                .expectNext(true)
                .verifyComplete();
    }
}
