package com.example.springvueapp.devops.controller;

import com.example.springvueapp.common.config.TestSecurityConfig;
import com.example.springvueapp.devops.model.DevOpsCdTask;
import com.example.springvueapp.devops.service.CdTaskService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * DevOpsCdTaskController的单元测试
 */
@WebFluxTest(DevOpsCdTaskController.class)
@Import(TestSecurityConfig.class)
class DevOpsCdTaskControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private CdTaskService cdTaskService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @WithMockUser(username = "1")
    void testCreateCdTask_WithValidInput_ShouldReturnCreatedTask() throws Exception {
        // Given
        Long applicationId = 1L;
        
        Map<String, String> componentVersions = new HashMap<>();
        componentVersions.put("component1", "v1.0.0");
        
        Map<String, Object> configuration = new HashMap<>();
        configuration.put("deploymentStrategy", "rolling");

        DevOpsCdTask inputTask = DevOpsCdTask.builder()
                .name("test-cd-task")
                .description("测试CD任务")
                .componentVersions(componentVersions)
                .configuration(configuration)
                .build();

        DevOpsCdTask createdTask = DevOpsCdTask.builder()
                .id(1L)
                .name("test-cd-task")
                .description("测试CD任务")
                .applicationId(applicationId)
                .componentVersions(componentVersions)
                .configuration(configuration)
                .status("ACTIVE")
                .userId(1L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // When
        when(cdTaskService.createCdTask(any(DevOpsCdTask.class), eq(applicationId), eq(1L)))
                .thenReturn(Mono.just(createdTask));

        // Then
        webTestClient.post()
                .uri("/api/devops/cd/tasks/application/{applicationId}", applicationId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(objectMapper.writeValueAsString(inputTask))
                .exchange()
                .expectStatus().isCreated()
                .expectBody()
                .jsonPath("$.id").isEqualTo(1)
                .jsonPath("$.name").isEqualTo("test-cd-task")
                .jsonPath("$.description").isEqualTo("测试CD任务")
                .jsonPath("$.applicationId").isEqualTo(applicationId)
                .jsonPath("$.status").isEqualTo("ACTIVE")
                .jsonPath("$.userId").isEqualTo(1);
    }

    @Test
    @WithMockUser(username = "1")
    void testCreateCdTask_WithInvalidInput_ShouldReturnBadRequest() throws Exception {
        // Given
        Long applicationId = 1L;
        
        DevOpsCdTask inputTask = DevOpsCdTask.builder()
                .name("test-cd-task")
                .build();

        // When
        when(cdTaskService.createCdTask(any(DevOpsCdTask.class), eq(applicationId), eq(1L)))
                .thenReturn(Mono.error(new IllegalArgumentException("应用不存在或无权限访问")));

        // Then
        webTestClient.post()
                .uri("/api/devops/cd/tasks/application/{applicationId}", applicationId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(objectMapper.writeValueAsString(inputTask))
                .exchange()
                .expectStatus().isBadRequest();
    }

    @Test
    @WithMockUser(username = "1")
    void testGetAllCdTasks_ShouldReturnAllTasks() {
        // Given
        DevOpsCdTask task1 = DevOpsCdTask.builder()
                .id(1L)
                .name("task1")
                .userId(1L)
                .build();
                
        DevOpsCdTask task2 = DevOpsCdTask.builder()
                .id(2L)
                .name("task2")
                .userId(1L)
                .build();

        // When
        when(cdTaskService.getAllCdTasks(1L))
                .thenReturn(Flux.just(task1, task2));

        // Then
        webTestClient.get()
                .uri("/api/devops/cd/tasks")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsCdTask.class)
                .hasSize(2);
    }

    @Test
    @WithMockUser(username = "1")
    void testGetCdTasksByApplication_ShouldReturnTasksForApplication() {
        // Given
        Long applicationId = 1L;

        DevOpsCdTask task1 = DevOpsCdTask.builder()
                .id(1L)
                .name("task1")
                .applicationId(applicationId)
                .userId(1L)
                .build();

        // When
        when(cdTaskService.getCdTasksByApplication(applicationId, 1L))
                .thenReturn(Flux.just(task1));

        // Then
        webTestClient.get()
                .uri("/api/devops/cd/tasks/application/{applicationId}", applicationId)
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsCdTask.class)
                .hasSize(1);
    }

    @Test
    @WithMockUser(username = "1")
    void testGetCdTaskById_WithValidId_ShouldReturnTask() {
        // Given
        Long taskId = 1L;
        
        DevOpsCdTask task = DevOpsCdTask.builder()
                .id(taskId)
                .name("test-task")
                .userId(1L)
                .build();

        // When
        when(cdTaskService.getCdTaskById(taskId, 1L))
                .thenReturn(Mono.just(task));

        // Then
        webTestClient.get()
                .uri("/api/devops/cd/tasks/{taskId}", taskId)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.id").isEqualTo(taskId)
                .jsonPath("$.name").isEqualTo("test-task")
                .jsonPath("$.userId").isEqualTo(1);
    }

    @Test
    @WithMockUser(username = "1")
    void testGetCdTaskById_WithInvalidId_ShouldReturnNotFound() {
        // Given
        Long taskId = 999L;

        // When
        when(cdTaskService.getCdTaskById(taskId, 1L))
                .thenReturn(Mono.error(new IllegalArgumentException("CD任务不存在或无权限访问")));

        // Then
        webTestClient.get()
                .uri("/api/devops/cd/tasks/{taskId}", taskId)
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "1")
    void testUpdateCdTask_WithValidInput_ShouldReturnUpdatedTask() throws Exception {
        // Given
        Long taskId = 1L;
        
        DevOpsCdTask inputTask = DevOpsCdTask.builder()
                .name("updated-task")
                .description("更新的任务")
                .build();

        DevOpsCdTask updatedTask = DevOpsCdTask.builder()
                .id(taskId)
                .name("updated-task")
                .description("更新的任务")
                .userId(1L)
                .updatedAt(LocalDateTime.now())
                .build();

        // When
        when(cdTaskService.updateCdTask(eq(taskId), any(DevOpsCdTask.class), eq(1L)))
                .thenReturn(Mono.just(updatedTask));

        // Then
        webTestClient.put()
                .uri("/api/devops/cd/tasks/{taskId}", taskId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(objectMapper.writeValueAsString(inputTask))
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.id").isEqualTo(taskId)
                .jsonPath("$.name").isEqualTo("updated-task")
                .jsonPath("$.description").isEqualTo("更新的任务")
                .jsonPath("$.userId").isEqualTo(1);
    }

    @Test
    @WithMockUser(username = "1")
    void testDeleteCdTask_WithValidId_ShouldReturnNoContent() {
        // Given
        Long taskId = 1L;

        // When
        when(cdTaskService.deleteCdTask(taskId, 1L))
                .thenReturn(Mono.just(true));

        // Then
        webTestClient.delete()
                .uri("/api/devops/cd/tasks/{taskId}", taskId)
                .exchange()
                .expectStatus().isNoContent();
    }

    @Test
    @WithMockUser(username = "1")
    void testDeleteCdTask_WithInvalidId_ShouldReturnNotFound() {
        // Given
        Long taskId = 999L;

        // When
        when(cdTaskService.deleteCdTask(taskId, 1L))
                .thenReturn(Mono.just(false));

        // Then
        webTestClient.delete()
                .uri("/api/devops/cd/tasks/{taskId}", taskId)
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "1")
    void testStartCdTask_WithValidId_ShouldReturnDeploymentResult() throws Exception {
        // Given
        Long taskId = 1L;
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("version", "v1.0.0");
        
        Map<String, Object> deploymentResult = new HashMap<>();
        deploymentResult.put("taskId", taskId);
        deploymentResult.put("deploymentId", "deploy-123");
        deploymentResult.put("status", "DEPLOYING");

        // When
        when(cdTaskService.startCdTask(eq(taskId), eq(1L), any()))
                .thenReturn(Mono.just(deploymentResult));

        // Then
        webTestClient.post()
                .uri("/api/devops/cd/tasks/{taskId}/start", taskId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(objectMapper.writeValueAsString(parameters))
                .exchange()
                .expectStatus().isCreated()
                .expectBody()
                .jsonPath("$.taskId").isEqualTo(taskId)
                .jsonPath("$.deploymentId").isEqualTo("deploy-123")
                .jsonPath("$.status").isEqualTo("DEPLOYING");
    }

    @Test
    @WithMockUser(username = "1")
    void testStopCdTask_WithValidId_ShouldReturnOk() {
        // Given
        Long taskId = 1L;

        // When
        when(cdTaskService.stopCdTask(taskId, 1L))
                .thenReturn(Mono.just(true));

        // Then
        webTestClient.post()
                .uri("/api/devops/cd/tasks/{taskId}/stop", taskId)
                .exchange()
                .expectStatus().isOk();
    }

    @Test
    @WithMockUser(username = "1")
    void testGetCdTaskStatus_WithValidId_ShouldReturnStatus() {
        // Given
        Long taskId = 1L;
        
        Map<String, Object> status = new HashMap<>();
        status.put("taskId", taskId);
        status.put("status", "DEPLOYING");
        status.put("progress", 50);

        // When
        when(cdTaskService.getCdTaskStatus(taskId, 1L))
                .thenReturn(Mono.just(status));

        // Then
        webTestClient.get()
                .uri("/api/devops/cd/tasks/{taskId}/status", taskId)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.taskId").isEqualTo(taskId)
                .jsonPath("$.status").isEqualTo("DEPLOYING")
                .jsonPath("$.progress").isEqualTo(50);
    }
}
