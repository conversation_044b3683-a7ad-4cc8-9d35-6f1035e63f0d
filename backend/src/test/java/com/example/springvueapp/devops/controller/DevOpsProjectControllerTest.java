package com.example.springvueapp.devops.controller;

import com.example.springvueapp.common.config.TestSecurityConfig;
import com.example.springvueapp.devops.model.DevOpsProject;
import com.example.springvueapp.devops.service.DevOpsProjectService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@WebFluxTest(DevOpsProjectController.class)
@Import(TestSecurityConfig.class)
class DevOpsProjectControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private DevOpsProjectService projectService;

    private DevOpsProject testProject;

    @BeforeEach
    void setUp() {
        testProject = new DevOpsProject();
        testProject.setId(1L);
        testProject.setName("测试项目");
        testProject.setDescription("这是一个测试项目");
        testProject.setStatus("ACTIVE");
        testProject.setUserId(1L);
        testProject.setCreatedAt(LocalDateTime.now());
        testProject.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void createProject_ShouldReturnCreatedProject() throws Exception {
        // Given
        DevOpsProject newProject = new DevOpsProject();
        newProject.setName("新项目");
        newProject.setDescription("新项目描述");
        newProject.setStatus("ACTIVE");

        when(projectService.createProject(any(DevOpsProject.class), anyLong()))
                .thenReturn(Mono.just(testProject));

        // When & Then
        webTestClient.post()
                .uri("/api/devops/projects")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(newProject)
                .exchange()
                .expectStatus().isCreated()
                .expectBody(DevOpsProject.class)
                .value(project -> {
                    assert project.getId().equals(1L);
                    assert project.getName().equals("测试项目");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getAllProjects_ShouldReturnProjectList() {
        // Given
        when(projectService.getAllProjects(anyLong()))
                .thenReturn(Flux.just(testProject));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/projects")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsProject.class)
                .hasSize(1)
                .value(projects -> {
                    assert projects.get(0).getName().equals("测试项目");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getProjectById_ShouldReturnProject() {
        // Given
        when(projectService.getProjectById(eq(1L), anyLong()))
                .thenReturn(Mono.just(testProject));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/projects/1")
                .exchange()
                .expectStatus().isOk()
                .expectBody(DevOpsProject.class)
                .value(project -> {
                    assert project.getId().equals(1L);
                    assert project.getName().equals("测试项目");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getProjectById_WhenNotFound_ShouldReturn404() {
        // Given
        when(projectService.getProjectById(eq(999L), anyLong()))
                .thenReturn(Mono.error(new IllegalArgumentException("Project not found")));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/projects/999")
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void updateProject_ShouldReturnUpdatedProject() throws Exception {
        // Given
        DevOpsProject updatedProject = new DevOpsProject();
        updatedProject.setName("更新的项目");
        updatedProject.setDescription("更新的描述");
        updatedProject.setStatus("INACTIVE");

        DevOpsProject returnProject = new DevOpsProject();
        returnProject.setId(1L);
        returnProject.setName("更新的项目");
        returnProject.setDescription("更新的描述");
        returnProject.setStatus("INACTIVE");
        returnProject.setUserId(1L);

        when(projectService.updateProject(eq(1L), any(DevOpsProject.class), anyLong()))
                .thenReturn(Mono.just(returnProject));

        // When & Then
        webTestClient.put()
                .uri("/api/devops/projects/1")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(updatedProject)
                .exchange()
                .expectStatus().isOk()
                .expectBody(DevOpsProject.class)
                .value(project -> {
                    assert project.getName().equals("更新的项目");
                    assert project.getStatus().equals("INACTIVE");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void updateProject_WhenNotFound_ShouldReturn400() throws Exception {
        // Given
        DevOpsProject updatedProject = new DevOpsProject();
        updatedProject.setName("更新的项目");

        when(projectService.updateProject(eq(999L), any(DevOpsProject.class), anyLong()))
                .thenReturn(Mono.error(new IllegalArgumentException("Project not found")));

        // When & Then
        webTestClient.put()
                .uri("/api/devops/projects/999")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(updatedProject)
                .exchange()
                .expectStatus().isBadRequest();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void deleteProject_ShouldReturnNoContent() {
        // Given
        when(projectService.deleteProject(eq(1L), anyLong()))
                .thenReturn(Mono.just(true));

        // When & Then
        webTestClient.delete()
                .uri("/api/devops/projects/1")
                .exchange()
                .expectStatus().isNoContent();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void deleteProject_WhenNotFound_ShouldReturn404() {
        // Given
        when(projectService.deleteProject(eq(999L), anyLong()))
                .thenReturn(Mono.error(new IllegalArgumentException("Project not found")));

        // When & Then
        webTestClient.delete()
                .uri("/api/devops/projects/999")
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void searchProjects_ShouldReturnMatchingProjects() {
        // Given
        when(projectService.searchProjects(anyLong(), eq("测试")))
                .thenReturn(Flux.just(testProject));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/projects/search?keyword=测试")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsProject.class)
                .hasSize(1)
                .value(projects -> {
                    assert projects.get(0).getName().equals("测试项目");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void countProjects_ShouldReturnCount() {
        // Given
        when(projectService.countProjects(anyLong()))
                .thenReturn(Mono.just(5L));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/projects/count")
                .exchange()
                .expectStatus().isOk()
                .expectBody(Long.class)
                .value(count -> {
                    assert count.equals(5L);
                });
    }


}
