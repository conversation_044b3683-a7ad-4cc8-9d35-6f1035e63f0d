package com.example.springvueapp.devops.controller;

import com.example.springvueapp.common.config.TestSecurityConfig;
import com.example.springvueapp.devops.model.DevOpsResource;
import com.example.springvueapp.devops.service.DevOpsResourceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@WebFluxTest(DevOpsResourceController.class)
@Import(TestSecurityConfig.class)
class DevOpsResourceControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private DevOpsResourceService resourceService;

    private DevOpsResource testResource;

    @BeforeEach
    void setUp() {
        testResource = new DevOpsResource();
        testResource.setId(1L);
        testResource.setName("测试资源");
        testResource.setDescription("这是一个测试资源");
        testResource.setComponentId(1L);
        testResource.setType("DATABASE");
        testResource.setPath("********************************");
        testResource.setStatus("ACTIVE");
        testResource.setUserId(1L);
        testResource.setCreatedAt(LocalDateTime.now());
        testResource.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void createResource_ShouldReturnCreatedResource() throws Exception {
        // Given
        DevOpsResource newResource = new DevOpsResource();
        newResource.setName("新资源");
        newResource.setDescription("新资源描述");
        newResource.setType("CACHE");
        newResource.setPath("redis://localhost:6379");
        newResource.setStatus("ACTIVE");

        when(resourceService.createResource(any(DevOpsResource.class), eq(1L), anyLong()))
                .thenReturn(Mono.just(testResource));

        // When & Then
        webTestClient.post()
                .uri("/api/devops/resources/component/1")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(newResource)
                .exchange()
                .expectStatus().isCreated()
                .expectBody(DevOpsResource.class)
                .value(resource -> {
                    assert resource.getId().equals(1L);
                    assert resource.getName().equals("测试资源");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getAllResources_ShouldReturnResourceList() {
        // Given
        when(resourceService.getAllResources(anyLong()))
                .thenReturn(Flux.just(testResource));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/resources")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsResource.class)
                .hasSize(1)
                .value(resources -> {
                    assert resources.get(0).getName().equals("测试资源");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getResourcesByComponent_ShouldReturnResourceList() {
        // Given
        when(resourceService.getResourcesByComponent(eq(1L), anyLong()))
                .thenReturn(Flux.just(testResource));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/resources/component/1")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsResource.class)
                .hasSize(1)
                .value(resources -> {
                    assert resources.get(0).getComponentId().equals(1L);
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getResourceById_ShouldReturnResource() {
        // Given
        when(resourceService.getResourceById(eq(1L), anyLong()))
                .thenReturn(Mono.just(testResource));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/resources/1")
                .exchange()
                .expectStatus().isOk()
                .expectBody(DevOpsResource.class)
                .value(resource -> {
                    assert resource.getId().equals(1L);
                    assert resource.getName().equals("测试资源");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getResourceById_WhenNotFound_ShouldReturn404() {
        // Given
        when(resourceService.getResourceById(eq(999L), anyLong()))
                .thenReturn(Mono.error(new IllegalArgumentException("Resource not found")));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/resources/999")
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void updateResource_ShouldReturnUpdatedResource() throws Exception {
        // Given
        DevOpsResource updatedResource = new DevOpsResource();
        updatedResource.setName("更新的资源");
        updatedResource.setDescription("更新的描述");
        updatedResource.setStatus("INACTIVE");

        DevOpsResource returnResource = new DevOpsResource();
        returnResource.setId(1L);
        returnResource.setName("更新的资源");
        returnResource.setDescription("更新的描述");
        returnResource.setStatus("INACTIVE");
        returnResource.setUserId(1L);

        when(resourceService.updateResource(eq(1L), any(DevOpsResource.class), anyLong()))
                .thenReturn(Mono.just(returnResource));

        // When & Then
        webTestClient.put()
                .uri("/api/devops/resources/1")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(updatedResource)
                .exchange()
                .expectStatus().isOk()
                .expectBody(DevOpsResource.class)
                .value(resource -> {
                    assert resource.getName().equals("更新的资源");
                    assert resource.getStatus().equals("INACTIVE");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void deleteResource_ShouldReturnNoContent() {
        // Given
        when(resourceService.deleteResource(eq(1L), anyLong()))
                .thenReturn(Mono.just(true));

        // When & Then
        webTestClient.delete()
                .uri("/api/devops/resources/1")
                .exchange()
                .expectStatus().isNoContent();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void deleteResource_WhenNotFound_ShouldReturn404() {
        // Given
        when(resourceService.deleteResource(eq(999L), anyLong()))
                .thenReturn(Mono.error(new IllegalArgumentException("Resource not found")));

        // When & Then
        webTestClient.delete()
                .uri("/api/devops/resources/999")
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void getResourcesByType_ShouldReturnResourceList() {
        // Given
        when(resourceService.getResourcesByType(anyLong(), eq("DATABASE")))
                .thenReturn(Flux.just(testResource));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/resources/type/DATABASE")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsResource.class)
                .hasSize(1)
                .value(resources -> {
                    assert resources.get(0).getType().equals("DATABASE");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void searchResources_ShouldReturnMatchingResources() {
        // Given
        when(resourceService.searchResources(anyLong(), eq("测试")))
                .thenReturn(Flux.just(testResource));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/resources/search?keyword=测试")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsResource.class)
                .hasSize(1)
                .value(resources -> {
                    assert resources.get(0).getName().equals("测试资源");
                });
    }

    @Test
    @WithMockUser(username = "testuser", authorities = {"USER"})
    void searchResourcesInComponent_ShouldReturnMatchingResources() {
        // Given
        when(resourceService.searchResourcesInComponent(eq(1L), anyLong(), eq("测试")))
                .thenReturn(Flux.just(testResource));

        // When & Then
        webTestClient.get()
                .uri("/api/devops/resources/component/1/search?keyword=测试")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(DevOpsResource.class)
                .hasSize(1)
                .value(resources -> {
                    assert resources.get(0).getName().equals("测试资源");
                });
    }




}
