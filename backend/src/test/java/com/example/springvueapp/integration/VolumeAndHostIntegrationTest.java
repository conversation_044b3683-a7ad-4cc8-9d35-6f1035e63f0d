package com.example.springvueapp.integration;

import com.example.springvueapp.sandbox.model.HostEntry;
import com.example.springvueapp.sandbox.model.VolumeMount;
import com.example.springvueapp.sandbox.SandboxConfig;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Volume挂载和Host域名解析功能的集成测试
 * 验证完整的功能流程
 */
class VolumeAndHostIntegrationTest {

    @Test
    void testCompleteVolumeAndHostConfiguration() {
        // 创建完整的沙箱配置，包含Volume挂载和Host域名解析
        VolumeMount dataMount = new VolumeMount();
        dataMount.setHostPath("/host/data");
        dataMount.setContainerPath("/container/data");
        dataMount.setReadOnly(false);

        VolumeMount logsMount = new VolumeMount();
        logsMount.setHostPath("/host/logs");
        logsMount.setContainerPath("/container/logs");
        logsMount.setReadOnly(true);

        HostEntry dbEntry = new HostEntry();
        dbEntry.setHostname("database.local");
        dbEntry.setIpAddress("********");

        HostEntry apiEntry = new HostEntry();
        apiEntry.setHostname("api.service.local");
        apiEntry.setIpAddress("********");

        List<VolumeMount> volumeMounts = Arrays.asList(dataMount, logsMount);
        List<HostEntry> hostEntries = Arrays.asList(dbEntry, apiEntry);

        // 使用Builder创建完整配置
        SandboxConfig config = SandboxConfig.builder()
                .id("integration-test-sandbox")
                .name("Integration Test Sandbox")
                .description("用于集成测试的完整沙箱配置")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .volumeMounts(volumeMounts)
                .hostEntries(hostEntries)
                .timeoutSeconds(300)
                .autoRestart(false)
                .build();

        // 验证配置完整性
        assertNotNull(config);
        assertEquals("integration-test-sandbox", config.getId());
        assertEquals("Integration Test Sandbox", config.getName());
        assertEquals("用于集成测试的完整沙箱配置", config.getDescription());
        assertEquals("ubuntu:20.04", config.getDockerImage());
        assertEquals("/bin/bash", config.getCommand());
        assertEquals(300, config.getTimeoutSeconds());
        assertFalse(config.getAutoRestart());

        // 验证Volume挂载配置
        assertNotNull(config.getVolumeMounts());
        assertEquals(2, config.getVolumeMounts().size());

        VolumeMount actualDataMount = config.getVolumeMounts().get(0);
        assertEquals("/host/data", actualDataMount.getHostPath());
        assertEquals("/container/data", actualDataMount.getContainerPath());
        assertFalse(actualDataMount.getReadOnly());

        VolumeMount actualLogsMount = config.getVolumeMounts().get(1);
        assertEquals("/host/logs", actualLogsMount.getHostPath());
        assertEquals("/container/logs", actualLogsMount.getContainerPath());
        assertTrue(actualLogsMount.getReadOnly());

        // 验证Host域名解析配置
        assertNotNull(config.getHostEntries());
        assertEquals(2, config.getHostEntries().size());

        HostEntry actualDbEntry = config.getHostEntries().get(0);
        assertEquals("database.local", actualDbEntry.getHostname());
        assertEquals("********", actualDbEntry.getIpAddress());

        HostEntry actualApiEntry = config.getHostEntries().get(1);
        assertEquals("api.service.local", actualApiEntry.getHostname());
        assertEquals("********", actualApiEntry.getIpAddress());
    }

    @Test
    void testVolumePathValidation() {
        // 测试Volume路径的有效性验证
        VolumeMount validMount = new VolumeMount();
        validMount.setHostPath("/valid/host/path");
        validMount.setContainerPath("/valid/container/path");
        validMount.setReadOnly(true);

        // 验证有效路径
        assertNotNull(validMount.getHostPath());
        assertNotNull(validMount.getContainerPath());
        assertTrue(validMount.getHostPath().startsWith("/"));
        assertTrue(validMount.getContainerPath().startsWith("/"));
        assertTrue(validMount.getReadOnly());

        // 测试相对路径（在实际应用中可能需要额外验证）
        VolumeMount relativeMount = new VolumeMount();
        relativeMount.setHostPath("./relative/path");
        relativeMount.setContainerPath("relative/container/path");
        relativeMount.setReadOnly(false);

        assertNotNull(relativeMount.getHostPath());
        assertNotNull(relativeMount.getContainerPath());
        assertFalse(relativeMount.getReadOnly());
    }

    @Test
    void testHostEntryValidation() {
        // 测试Host条目的有效性验证
        HostEntry validEntry = new HostEntry();
        validEntry.setHostname("valid.example.com");
        validEntry.setIpAddress("*************");

        // 验证有效的Host条目
        assertNotNull(validEntry.getHostname());
        assertNotNull(validEntry.getIpAddress());
        assertTrue(validEntry.getHostname().contains("."));

        // 验证IP地址格式（简单检查）
        String[] ipParts = validEntry.getIpAddress().split("\\.");
        assertEquals(4, ipParts.length);

        // 测试localhost条目
        HostEntry localhostEntry = new HostEntry();
        localhostEntry.setHostname("localhost");
        localhostEntry.setIpAddress("127.0.0.1");

        assertNotNull(localhostEntry.getHostname());
        assertNotNull(localhostEntry.getIpAddress());
        assertEquals("localhost", localhostEntry.getHostname());
        assertEquals("127.0.0.1", localhostEntry.getIpAddress());

        // 测试内网IP地址
        HostEntry internalEntry = new HostEntry();
        internalEntry.setHostname("internal.service");
        internalEntry.setIpAddress("********");

        assertNotNull(internalEntry.getHostname());
        assertNotNull(internalEntry.getIpAddress());
        assertTrue(internalEntry.getIpAddress().startsWith("10."));
    }

    @Test
    void testConfigurationSerialization() {
        // 测试配置的序列化和反序列化兼容性
        VolumeMount mount = new VolumeMount();
        mount.setHostPath("/test/host");
        mount.setContainerPath("/test/container");
        mount.setReadOnly(true);

        HostEntry entry = new HostEntry();
        entry.setHostname("test.local");
        entry.setIpAddress("127.0.0.1");

        SandboxConfig originalConfig = SandboxConfig.builder()
                .name("serialization-test")
                .dockerImage("test:latest")
                .command("test")
                .volumeMounts(Arrays.asList(mount))
                .hostEntries(Arrays.asList(entry))
                .build();

        // 验证原始配置
        assertNotNull(originalConfig);
        assertNotNull(originalConfig.getVolumeMounts());
        assertNotNull(originalConfig.getHostEntries());
        assertEquals(1, originalConfig.getVolumeMounts().size());
        assertEquals(1, originalConfig.getHostEntries().size());

        // 验证Volume挂载数据
        VolumeMount actualMount = originalConfig.getVolumeMounts().get(0);
        assertEquals("/test/host", actualMount.getHostPath());
        assertEquals("/test/container", actualMount.getContainerPath());
        assertTrue(actualMount.getReadOnly());

        // 验证Host条目数据
        HostEntry actualEntry = originalConfig.getHostEntries().get(0);
        assertEquals("test.local", actualEntry.getHostname());
        assertEquals("127.0.0.1", actualEntry.getIpAddress());
    }

    @Test
    void testEmptyAndNullConfigurations() {
        // 测试空配置和null配置的处理
        SandboxConfig emptyConfig = SandboxConfig.builder()
                .name("empty-config")
                .dockerImage("empty:latest")
                .command("empty")
                .volumeMounts(Arrays.asList())
                .hostEntries(Arrays.asList())
                .build();

        assertNotNull(emptyConfig);
        assertNotNull(emptyConfig.getVolumeMounts());
        assertNotNull(emptyConfig.getHostEntries());
        assertEquals(0, emptyConfig.getVolumeMounts().size());
        assertEquals(0, emptyConfig.getHostEntries().size());

        SandboxConfig nullConfig = SandboxConfig.builder()
                .name("null-config")
                .dockerImage("null:latest")
                .command("null")
                .volumeMounts(null)
                .hostEntries(null)
                .build();

        assertNotNull(nullConfig);
        assertNull(nullConfig.getVolumeMounts());
        assertNull(nullConfig.getHostEntries());
    }

    @Test
    void testBuilderPatternConsistency() {
        // 测试Builder模式的一致性
        SandboxConfig config1 = SandboxConfig.builder()
                .name("test1")
                .dockerImage("image1")
                .volumeMounts(Arrays.asList(new VolumeMount()))
                .hostEntries(Arrays.asList(new HostEntry()))
                .build();

        SandboxConfig config2 = SandboxConfig.builder()
                .name("test2")
                .dockerImage("image2")
                .volumeMounts(Arrays.asList(new VolumeMount()))
                .hostEntries(Arrays.asList(new HostEntry()))
                .build();

        // 验证两个配置是独立的
        assertNotEquals(config1.getName(), config2.getName());
        assertNotEquals(config1.getDockerImage(), config2.getDockerImage());
        assertNotSame(config1.getVolumeMounts(), config2.getVolumeMounts());
        assertNotSame(config1.getHostEntries(), config2.getHostEntries());

        // 但结构应该相同
        assertEquals(config1.getVolumeMounts().size(), config2.getVolumeMounts().size());
        assertEquals(config1.getHostEntries().size(), config2.getHostEntries().size());
    }
}
