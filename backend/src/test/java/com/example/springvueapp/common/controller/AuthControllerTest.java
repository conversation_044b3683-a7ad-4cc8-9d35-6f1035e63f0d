package com.example.springvueapp.common.controller;

import com.example.springvueapp.common.model.User;
import com.example.springvueapp.common.service.AuthService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * AuthController 的单元测试
 * 使用 Mockito 模拟 AuthService
 */
@ExtendWith(MockitoExtension.class)
public class AuthControllerTest {

    @Mock
    private AuthService authService;

    @InjectMocks
    private AuthController authController;

    @Test
    public void testAuthenticateUser_Success() {
        // 准备测试数据
        Map<String, String> loginRequest = new HashMap<>();
        loginRequest.put("username", "testuser");
        loginRequest.put("password", "password");

        // 模拟 AuthService 行为
        when(authService.authenticateUser("testuser", "password"))
                .thenReturn(Mono.just("jwt.token.here"));

        // 调用被测试方法
        Mono<ResponseEntity<Map<String, String>>> responseMono = 
                authController.authenticateUser(loginRequest);

        // 验证结果
        StepVerifier.create(responseMono)
                .expectNextMatches(response -> 
                    response.getStatusCode() == HttpStatus.OK &&
                    response.getBody().containsKey("token") &&
                    response.getBody().get("token").equals("jwt.token.here")
                )
                .verifyComplete();
    }

    @Test
    public void testAuthenticateUser_Failure() {
        // 准备测试数据
        Map<String, String> loginRequest = new HashMap<>();
        loginRequest.put("username", "testuser");
        loginRequest.put("password", "wrongpassword");

        // 模拟 AuthService 行为
        when(authService.authenticateUser("testuser", "wrongpassword"))
                .thenReturn(Mono.error(new IllegalArgumentException("用户名或密码错误")));

        // 调用被测试方法
        Mono<ResponseEntity<Map<String, String>>> responseMono = 
                authController.authenticateUser(loginRequest);

        // 验证结果
        StepVerifier.create(responseMono)
                .expectNextMatches(response -> 
                    response.getStatusCode() == HttpStatus.BAD_REQUEST &&
                    response.getBody().containsKey("error") &&
                    response.getBody().get("error").equals("用户名或密码错误")
                )
                .verifyComplete();
    }

    @Test
    public void testRegisterUser_Success() {
        // 准备测试数据
        User user = User.builder()
                .username("newuser")
                .password("password")
                .email("<EMAIL>")
                .fullName("New User")
                .build();

        // 模拟 AuthService 行为
        when(authService.registerUser(any(User.class)))
                .thenReturn(Mono.just(true));

        // 调用被测试方法
        Mono<ResponseEntity<?>> responseMono = authController.registerUser(user);

        // 验证结果
        StepVerifier.create(responseMono)
                .expectNextMatches(response -> 
                    response.getStatusCode() == HttpStatus.OK
                )
                .verifyComplete();
    }

    @Test
    public void testRegisterUser_Failure() {
        // 准备测试数据
        User user = User.builder()
                .username("existinguser")
                .password("password")
                .email("<EMAIL>")
                .fullName("Existing User")
                .build();

        // 模拟 AuthService 行为
        when(authService.registerUser(any(User.class)))
                .thenReturn(Mono.just(false));

        // 调用被测试方法
        Mono<ResponseEntity<?>> responseMono = authController.registerUser(user);

        // 验证结果
        StepVerifier.create(responseMono)
                .expectNextMatches(response -> 
                    response.getStatusCode() == HttpStatus.BAD_REQUEST &&
                    response.getBody() instanceof Map &&
                    ((Map<?, ?>) response.getBody()).containsKey("error")
                )
                .verifyComplete();
    }
}
