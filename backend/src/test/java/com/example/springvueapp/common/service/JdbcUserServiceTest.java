package com.example.springvueapp.common.service;

import com.example.springvueapp.common.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * JdbcUserService 的单元测试
 * 使用 Mockito 模拟 JdbcTemplate
 */
@ExtendWith(MockitoExtension.class)
public class JdbcUserServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private JdbcUserService jdbcUserService;

    private User testUser;

    @BeforeEach
    public void setUp() {
        // 创建测试用户
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .password("password")
                .email("<EMAIL>")
                .fullName("Test User")
                .enabled(true)
                .build();
    }

    @Test
    public void testFindByUsername_UserExists() {
        // 模拟 JdbcTemplate 行为
        when(jdbcTemplate.query(anyString(), any(RowMapper.class), eq("testuser")))
                .thenReturn(Arrays.asList(testUser));

        // 调用被测试方法
        Optional<User> result = jdbcUserService.findByUsername("testuser");

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals("testuser", result.get().getUsername());
        assertEquals("<EMAIL>", result.get().getEmail());
    }

    @Test
    public void testFindByUsername_UserNotExists() {
        // 模拟 JdbcTemplate 行为
        when(jdbcTemplate.query(anyString(), any(RowMapper.class), eq("nonexistentuser")))
                .thenReturn(Collections.emptyList());

        // 调用被测试方法
        Optional<User> result = jdbcUserService.findByUsername("nonexistentuser");

        // 验证结果
        assertFalse(result.isPresent());
    }

    @Test
    public void testFindByUsernameReactive_UserExists() {
        // 模拟 JdbcTemplate 行为
        when(jdbcTemplate.query(anyString(), any(RowMapper.class), eq("testuser")))
                .thenReturn(Arrays.asList(testUser));

        // 调用被测试方法
        Mono<User> userMono = jdbcUserService.findByUsernameReactive("testuser");

        // 验证结果
        StepVerifier.create(userMono)
                .expectNextMatches(user -> 
                    user.getUsername().equals("testuser") &&
                    user.getEmail().equals("<EMAIL>")
                )
                .verifyComplete();
    }

    @Test
    public void testExistsByUsername_True() {
        // 模拟 JdbcTemplate 行为
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq("testuser")))
                .thenReturn(1);

        // 调用被测试方法
        boolean result = jdbcUserService.existsByUsername("testuser");

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void testExistsByUsername_False() {
        // 模拟 JdbcTemplate 行为
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq("nonexistentuser")))
                .thenReturn(0);

        // 调用被测试方法
        boolean result = jdbcUserService.existsByUsername("nonexistentuser");

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testSave_NewUser() {
        // 创建新用户
        User newUser = User.builder()
                .username("newuser")
                .password("password")
                .email("<EMAIL>")
                .fullName("New User")
                .enabled(true)
                .build();

        // 模拟 JdbcTemplate 行为
        when(jdbcTemplate.update(
                anyString(), 
                eq(newUser.getUsername()), 
                eq(newUser.getPassword()), 
                eq(newUser.getEmail()), 
                eq(newUser.getFullName()), 
                eq(newUser.isEnabled())
        )).thenReturn(1);
        
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), eq(newUser.getUsername())))
                .thenReturn(2L);

        // 调用被测试方法
        User savedUser = jdbcUserService.save(newUser);

        // 验证结果
        assertNotNull(savedUser);
        assertEquals(2L, savedUser.getId());
        assertEquals("newuser", savedUser.getUsername());
    }
}
