package com.example.springvueapp.common.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JwtService 的单元测试
 */
public class JwtServiceTest {

    private JwtService jwtService;

    @BeforeEach
    public void setUp() {
        jwtService = new JwtService();
        // 使用反射设置私有字段的值
        ReflectionTestUtils.setField(jwtService, "jwtSecret", "testSecretKeyThatIsLongEnoughForHS256Algorithm");
        ReflectionTestUtils.setField(jwtService, "jwtExpirationMs", 3600000); // 1小时
    }

    @Test
    public void testGenerateToken() {
        // 生成令牌
        String token = jwtService.generateToken("testuser");

        // 验证令牌不为空
        assertNotNull(token);
        // 验证令牌格式 (JWT 格式: header.payload.signature)
        String[] parts = token.split("\\.");
        assertEquals(3, parts.length);
    }

    @Test
    public void testGenerateTokenMono() {
        // 生成响应式令牌
        Mono<String> tokenMono = jwtService.generateTokenMono("testuser");

        // 验证响应式令牌
        StepVerifier.create(tokenMono)
                .expectNextMatches(token -> {
                    // 验证令牌不为空
                    assertNotNull(token);
                    // 验证令牌格式
                    String[] parts = token.split("\\.");
                    return parts.length == 3;
                })
                .verifyComplete();
    }
}
