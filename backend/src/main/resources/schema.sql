CREATE TABLE IF NOT EXISTS users (
  id IDENTITY PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(100) NOT NULL,
  email VARCHAR(100) NOT NULL UNIQUE,
  full_name VARCHAR(100),
  enabled BOOLEAN DEFAULT TRUE
);

-- MCP Server Configurations table
CREATE TABLE IF NOT EXISTS mcp_server_configurations (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    command VARCHAR(500) NOT NULL,
    arguments CLOB, -- JSON string of List<String>
    environment CLOB, -- JSO<PERSON> string of Map<String, String>
    working_directory VARCHAR(500),
    docker_image VARCHAR(200) NOT NULL,
    resource_limits CLOB, -- JSON string of ResourceLimits
    network_config CLOB, -- JSON string of NetworkConfig
    volume_mounts CLOB, -- JSON string of List<VolumeMount>
    host_entries CLOB, -- J<PERSON><PERSON> string of List<HostEntry>
    timeout_seconds INTEGER DEFAULT 300,
    auto_restart BOOLEAN DEFAULT FALSE,
    enabled BOOLEAN DEFAULT TRUE,
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- MCP Server Instances table
CREATE TABLE IF NOT EXISTS mcp_server_instances (
    id IDENTITY PRIMARY KEY,
    configuration_id BIGINT NOT NULL,
    sandbox_id VARCHAR(100) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL,
    sandbox_type VARCHAR(20) NOT NULL,
    started_at TIMESTAMP NULL,
    stopped_at TIMESTAMP NULL,
    error_message CLOB,
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (configuration_id) REFERENCES mcp_server_configurations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- DevOps产品管理核心表结构

-- 项目表 (Project)
CREATE TABLE IF NOT EXISTS devops_projects (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(name, user_id)
);

-- 应用表 (Application)
CREATE TABLE IF NOT EXISTS devops_applications (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    project_id BIGINT NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES devops_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(name, project_id)
);

-- 组件表 (Component) - 对应Maven Parent模块概念
CREATE TABLE IF NOT EXISTS devops_components (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    application_id BIGINT NOT NULL,
    repository_url VARCHAR(500),
    repository_type VARCHAR(20) DEFAULT 'GIT',
    status VARCHAR(20) DEFAULT 'ACTIVE',
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES devops_applications(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(name, application_id)
);

-- 资源表 (Resource) - 对应Maven子模块概念
CREATE TABLE IF NOT EXISTS devops_resources (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    type VARCHAR(50) NOT NULL,
    component_id BIGINT NOT NULL,
    path VARCHAR(500),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (component_id) REFERENCES devops_components(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(name, component_id)
);

-- CI/CD相关表结构

-- CI任务表 (CiTask) - 基于组件层的持续集成任务
CREATE TABLE IF NOT EXISTS devops_ci_tasks (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    component_id BIGINT NOT NULL,
    version VARCHAR(50),
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'INACTIVE',
    configuration CLOB, -- JSON配置信息
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (component_id) REFERENCES devops_components(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(name, component_id)
);

-- CI任务实例表 (CiTaskInstance) - CI任务的执行实例
CREATE TABLE IF NOT EXISTS devops_ci_task_instances (
    id IDENTITY PRIMARY KEY,
    ci_task_id BIGINT NOT NULL,
    instance_id VARCHAR(100) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL,
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    logs CLOB,
    result_data CLOB, -- JSON格式的执行结果数据
    error_message CLOB,
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ci_task_id) REFERENCES devops_ci_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- CD任务表 (CdTask) - 基于应用层的持续交付任务
CREATE TABLE IF NOT EXISTS devops_cd_tasks (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    application_id BIGINT NOT NULL,
    component_versions CLOB, -- JSON格式的组件版本信息
    status VARCHAR(20) DEFAULT 'INACTIVE',
    configuration CLOB, -- JSON配置信息
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES devops_applications(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(name, application_id)
);

-- 部署模板表 (DeploymentTemplate) - 部署命令模板
CREATE TABLE IF NOT EXISTS devops_deployment_templates (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    template_type VARCHAR(50) NOT NULL,
    template_content CLOB NOT NULL, -- 模板内容
    variables CLOB, -- JSON格式的变量定义
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(name, user_id)
);

-- CD任务实例表 (CdTaskInstance) - CD任务的执行实例
CREATE TABLE IF NOT EXISTS devops_cd_task_instances (
    id IDENTITY PRIMARY KEY,
    cd_task_id BIGINT NOT NULL,
    instance_id VARCHAR(100) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL,
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    deployment_info CLOB, -- JSON格式的部署信息
    logs CLOB,
    error_message CLOB,
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cd_task_id) REFERENCES devops_cd_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tekton集群表 (TektonCluster) - Tekton集群配置管理
CREATE TABLE IF NOT EXISTS devops_tekton_clusters (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    api_server VARCHAR(500) NOT NULL,
    namespace VARCHAR(100) DEFAULT 'default',
    credentials CLOB, -- JSON格式的认证信息
    status VARCHAR(20) DEFAULT 'INACTIVE',
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(name, user_id)
);
