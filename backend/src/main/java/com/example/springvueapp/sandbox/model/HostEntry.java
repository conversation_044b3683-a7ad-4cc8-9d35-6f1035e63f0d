package com.example.springvueapp.sandbox.model;

/**
 * Host域名解析条目配置
 * 与前端TypeScript接口保持一致，用于在容器中添加自定义的hosts文件条目
 */
public class HostEntry {

    private String hostname;
    private String ipAddress;

    public HostEntry() {
    }

    public HostEntry(String hostname, String ipAddress) {
        this.hostname = hostname;
        this.ipAddress = ipAddress;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}
