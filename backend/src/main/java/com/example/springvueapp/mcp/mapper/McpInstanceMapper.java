package com.example.springvueapp.mcp.mapper;

import com.example.springvueapp.mcp.entity.McpServerInstanceEntity;
import com.example.springvueapp.mcp.model.McpServerInstance;
import org.springframework.stereotype.Component;

/**
 * MCP实例实体和DTO之间的转换器
 */
@Component
public class McpInstanceMapper {

    /**
     * 将Entity转换为DTO
     */
    public McpServerInstance toDto(McpServerInstanceEntity entity) {
        if (entity == null) {
            return null;
        }

        return McpServerInstance.builder()
                .id(entity.getId())
                .configurationId(entity.getConfigurationId())
                .sandboxId(entity.getSandboxId())
                .status(entity.getStatus())
                .sandboxType(entity.getSandboxType())
                .startedAt(entity.getStartedAt())
                .stoppedAt(entity.getStoppedAt())
                .errorMessage(entity.getErrorMessage())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为Entity
     */
    public McpServerInstanceEntity toEntity(McpServerInstance dto) {
        if (dto == null) {
            return null;
        }

        return McpServerInstanceEntity.builder()
                .id(dto.getId())
                .configurationId(dto.getConfigurationId())
                .sandboxId(dto.getSandboxId())
                .status(dto.getStatus())
                .sandboxType(dto.getSandboxType())
                .startedAt(dto.getStartedAt())
                .stoppedAt(dto.getStoppedAt())
                .errorMessage(dto.getErrorMessage())
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }
}
