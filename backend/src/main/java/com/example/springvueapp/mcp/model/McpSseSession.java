package com.example.springvueapp.mcp.model;

import com.example.springvueapp.mcp.client.McpClient;
import com.example.springvueapp.sandbox.SandboxInstance;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Sinks;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicReference;

/**
 * MCP SSE会话模型
 * 管理单个SSE连接的状态和相关资源
 */
public class McpSseSession {
    
    /**
     * 会话状态枚举
     */
    public enum SessionStatus {
        INITIALIZING,    // 初始化中
        ACTIVE,         // 活跃状态
        CLOSING,        // 关闭中
        CLOSED          // 已关闭
    }
    
    private final String sessionId;
    private final String mcpServerName;
    private final Long userId;
    private final LocalDateTime createdAt;
    private final AtomicReference<SessionStatus> status;
    
    // SSE事件发送器
    private final Sinks.Many<ServerSentEvent<String>> eventSink;
    
    // 关联的沙箱实例
    private volatile SandboxInstance sandboxInstance;
    
    // 关联的MCP客户端
    private volatile McpClient mcpClient;
    
    // 最后活跃时间
    private volatile LocalDateTime lastActiveAt;
    
    public McpSseSession(String sessionId, String mcpServerName, Long userId) {
        this.sessionId = sessionId;
        this.mcpServerName = mcpServerName;
        this.userId = userId;
        this.createdAt = LocalDateTime.now();
        this.lastActiveAt = LocalDateTime.now();
        this.status = new AtomicReference<>(SessionStatus.INITIALIZING);
        this.eventSink = Sinks.many().multicast().onBackpressureBuffer();
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public String getMcpServerName() {
        return mcpServerName;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public SessionStatus getStatus() {
        return status.get();
    }
    
    public boolean updateStatus(SessionStatus expectedStatus, SessionStatus newStatus) {
        return status.compareAndSet(expectedStatus, newStatus);
    }
    
    public void setStatus(SessionStatus newStatus) {
        status.set(newStatus);
    }
    
    public Sinks.Many<ServerSentEvent<String>> getEventSink() {
        return eventSink;
    }
    
    public SandboxInstance getSandboxInstance() {
        return sandboxInstance;
    }
    
    public void setSandboxInstance(SandboxInstance sandboxInstance) {
        this.sandboxInstance = sandboxInstance;
    }
    
    public McpClient getMcpClient() {
        return mcpClient;
    }
    
    public void setMcpClient(McpClient mcpClient) {
        this.mcpClient = mcpClient;
    }
    
    public LocalDateTime getLastActiveAt() {
        return lastActiveAt;
    }
    
    public void updateLastActiveAt() {
        this.lastActiveAt = LocalDateTime.now();
    }
    
    /**
     * 检查会话是否活跃
     */
    public boolean isActive() {
        return status.get() == SessionStatus.ACTIVE;
    }
    
    /**
     * 检查会话是否已关闭
     */
    public boolean isClosed() {
        SessionStatus currentStatus = status.get();
        return currentStatus == SessionStatus.CLOSED || currentStatus == SessionStatus.CLOSING;
    }
    
    /**
     * 发送SSE事件
     */
    public void sendEvent(SseEventType eventType, String data) {
        if (!isClosed()) {
            ServerSentEvent<String> event = ServerSentEvent.<String>builder()
                    .event(eventType.getEventName())
                    .data(data)
                    .build();
            eventSink.tryEmitNext(event);
            updateLastActiveAt();
        }
    }
    
    /**
     * 关闭会话
     */
    public void close() {
        if (updateStatus(SessionStatus.ACTIVE, SessionStatus.CLOSING) || 
            updateStatus(SessionStatus.INITIALIZING, SessionStatus.CLOSING)) {
            
            // 关闭MCP客户端
            if (mcpClient != null) {
                mcpClient.close().subscribe();
            }
            
            // 关闭沙箱实例
            if (sandboxInstance != null) {
                sandboxInstance.destroy().subscribe();
            }
            
            // 完成事件流
            eventSink.tryEmitComplete();
            
            // 更新状态为已关闭
            setStatus(SessionStatus.CLOSED);
        }
    }
}
