package com.example.springvueapp.mcp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.service.McpSseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * MCP SSE控制器
 * 提供SSE端点来与MCP服务器通信
 * <p>
 * 端点说明：
 * - GET /api/mcp/sse/{mcpServerName} - 建立SSE会话，创建沙箱，返回endpoint信息和sessionId
 * - POST /api/mcp/sse/message - 发送MCP消息到指定会话
 */
@RestController
@RequestMapping("/api/mcp/sse")
public class McpSseController {

    private static final Logger log = LoggerFactory.getLogger(McpSseController.class);

    private final McpSseService sseService;

    public McpSseController(McpSseService sseService) {
        this.sseService = sseService;
    }

    /**
     * 建立SSE会话
     * <p>
     * 此端点会：
     * 1. 创建新的SSE会话
     * 2. 启动对应的沙箱实例
     * 3. 初始化MCP客户端
     * 4. 返回SSE流，首先发送ENDPOINT_EVENT_TYPE事件包含sessionId和messageEndpoint
     * 5. 后续通过SSE流传输MCP消息和通知
     *
     * @param mcpServerName  MCP服务器名称
     * @param authentication 用户认证信息
     * @return SSE事件流
     */
    @GetMapping(value = "/{mcpServerName}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Mono<ResponseEntity<Flux<ServerSentEvent<String>>>> establishSseSession(
            @PathVariable String mcpServerName,
            Authentication authentication) {

        Long userId = getUserId(authentication);

        log.info("Establishing SSE session for MCP server: {} by user: {}", mcpServerName, userId);

        return sseService.createSession(mcpServerName, userId)
                .map(eventStream -> {
                    // 配置SSE流的属性
                    Flux<ServerSentEvent<String>> configuredStream = eventStream
                            .doOnSubscribe(subscription ->
                                    log.info("SSE client connected for MCP server: {}", mcpServerName))
                            .doOnCancel(() ->
                                    log.info("SSE client disconnected for MCP server: {}", mcpServerName))
                            .doOnError(error ->
                                    log.error("SSE stream error for MCP server {}: {}", mcpServerName, error.getMessage()))
                            .onBackpressureBuffer(1000) // 缓冲区大小
                            .timeout(Duration.ofMinutes(30)); // 超时设置

                    return ResponseEntity.ok()
                            .header("Cache-Control", "no-cache")
                            .header("Connection", "keep-alive")
                            .header("Access-Control-Allow-Origin", "*")
                            .header("Access-Control-Allow-Headers", "Cache-Control")
                            .body(configuredStream);
                })
                .onErrorResume(error -> {
                    log.error("Failed to establish SSE session for MCP server {}: {}", mcpServerName, error.getMessage());
                    return Mono.just(ResponseEntity.internalServerError().build());
                });
    }

    /**
     * 发送消息到MCP会话
     * <p>
     * 客户端通过此端点发送MCP协议消息到指定的会话。
     * 消息将被路由到对应的沙箱中的MCP服务器，响应通过SSE流返回。
     *
     * @param requestBody    JsonRpc请求体
     * @param authentication 用户认证信息
     * @return 处理结果
     */
    @PostMapping("/message")
    public Mono<ResponseEntity<Map<String, Object>>> sendMessage(
            @RequestParam(name = "sessionId") String sessionId,
            @RequestBody JsonRpcRequest requestBody,
            Authentication authentication) {

        Long userId = getUserId(authentication);

        log.debug("Sending message to session: {} by user: {}", sessionId, userId);

        // 验证请求参数
        if (sessionId == null || sessionId.trim().isEmpty()) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(Map.of("error", "sessionId is required")));
        }

        if (requestBody == null) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(Map.of("error", "message is required")));
        }

        return sseService.sendMessage(sessionId, requestBody)
                .then(Mono.just(ResponseEntity.ok(Map.<String, Object>of("status", "sent"))))
                .onErrorResume(error -> {
                    log.error("Failed to send message to session {}: {}",
                            sessionId, error.getMessage());

                    return Mono.just(ResponseEntity.badRequest()
                            .body(Map.<String, Object>of("error", error.getMessage())));
                });
    }

    /**
     * 关闭SSE会话
     *
     * @param sessionId      会话ID
     * @param authentication 用户认证信息
     * @return 处理结果
     */
    @DeleteMapping("/session/{sessionId}")
    public Mono<ResponseEntity<Map<String, Object>>> closeSession(
            @PathVariable String sessionId,
            Authentication authentication) {

        Long userId = getUserId(authentication);

        log.info("Closing SSE session: {} by user: {}", sessionId, userId);

        return sseService.closeSession(sessionId)
                .then(Mono.just(ResponseEntity.ok(Map.<String, Object>of("status", "closed"))))
                .onErrorResume(error -> {
                    log.error("Failed to close session {}: {}", sessionId, error.getMessage());
                    return Mono.just(ResponseEntity.badRequest()
                            .body(Map.<String, Object>of("error", error.getMessage())));
                });
    }

    /**
     * 获取SSE服务状态
     *
     * @param authentication 用户认证信息
     * @return 服务状态信息
     */
    @GetMapping("/status")
    public Mono<ResponseEntity<Map<String, Object>>> getStatus(Authentication authentication) {
        Long userId = getUserId(authentication);

        Map<String, Object> status = Map.of(
                "activeSessionCount", sseService.getActiveSessionCount(),
                "userSessions", sseService.getUserSessions(userId).size()
        );

        return Mono.just(ResponseEntity.ok(status));
    }

    /**
     * 从认证信息中提取用户ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication == null || authentication.getName() == null) {
            throw new RuntimeException("User not authenticated");
        }

        // 这里假设用户名就是用户ID，实际项目中可能需要从用户服务查询
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            // 如果用户名不是数字，可能需要通过用户服务查询用户ID
            // 这里简化处理，返回一个默认值或抛出异常
            throw new RuntimeException("Invalid user ID format: " + authentication.getName());
        }
    }
}
