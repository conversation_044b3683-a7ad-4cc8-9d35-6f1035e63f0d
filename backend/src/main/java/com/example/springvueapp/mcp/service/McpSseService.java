package com.example.springvueapp.mcp.service;

import com.example.springvueapp.mcp.client.McpClient;
import com.example.springvueapp.mcp.model.McpSseSession;
import com.example.springvueapp.mcp.model.McpToolCallRequest;
import com.example.springvueapp.mcp.model.SseEventType;
import com.example.springvueapp.mcp.protocol.JsonRpcMessage;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.mcp.model.McpServerConfiguration;
import com.example.springvueapp.sandbox.SandboxEnvironment;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * MCP SSE服务
 * 管理SSE会话生命周期、沙箱创建、消息路由等核心业务逻辑
 */
@Service
public class McpSseService {

    private static final Logger log = LoggerFactory.getLogger(McpSseService.class);

    // 会话超时时间（分钟）
    private static final int SESSION_TIMEOUT_MINUTES = 30;

    private final McpConfigurationService configurationService;
    private final McpProxyService proxyService;
    private final SandboxEnvironment sandboxEnvironment;
    private final ObjectMapper objectMapper;

    // 活跃会话映射
    private final Map<String, McpSseSession> activeSessions = new ConcurrentHashMap<>();

    // 定时清理器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    public McpSseService(McpConfigurationService configurationService,
                         McpProxyService proxyService,
                         SandboxEnvironment sandboxEnvironment,
                         ObjectMapper objectMapper) {
        this.configurationService = configurationService;
        this.proxyService = proxyService;
        this.sandboxEnvironment = sandboxEnvironment;
        this.objectMapper = objectMapper;

        // 启动会话清理任务
        startSessionCleanupTask();
    }

    /**
     * 创建新的SSE会话
     */
    public Mono<Flux<ServerSentEvent<String>>> createSession(String mcpServerName, Long userId) {
        String sessionId = generateSessionId();
        McpSseSession session = new McpSseSession(sessionId, mcpServerName, userId);
        // 注册会话
        activeSessions.put(sessionId, session);
        log.info("Created SSE session: {} for MCP server: {}", sessionId, mcpServerName);
        return configurationService.getConfigurationByName(mcpServerName, userId)
                .switchIfEmpty(Mono.error(new RuntimeException("MCP server configuration not found: " + mcpServerName)))
                .flatMap(config -> {
                    // 异步初始化沙箱和MCP客户端
                    initializeSession(session, config)
                            .subscribe(
                                    success -> log.info("Session {} initialized successfully", sessionId),
                                    error -> {
                                        log.error("Failed to initialize session {}: {}", sessionId, error.getMessage());
                                        session.sendEvent(SseEventType.ERROR_EVENT_TYPE,
                                                "Failed to initialize session: " + error.getMessage());
                                        closeSession(sessionId);
                                    }
                            );
                    return Mono.just(session.getEventSink().asFlux());
                })
                .doFinally(s -> {
                    // 客户端关闭流，doOnCancel和doFinally都会执行，清理会话
                    closeSession(sessionId);
                    log.info("Close SSE session: {} for MCP server: {}", sessionId, mcpServerName);
                });
    }

    /**
     * 发送消息到指定会话
     */
    public Mono<Void> sendMessage(String sessionId, Object message) {
        McpSseSession session = activeSessions.get(sessionId);
        if (session == null) {
            return Mono.error(new RuntimeException("Session not found: " + sessionId));
        }

        if (!session.isActive()) {
            return Mono.error(new RuntimeException("Session is not active: " + sessionId));
        }

        McpClient mcpClient = session.getMcpClient();
        if (mcpClient == null) {
            return Mono.error(new RuntimeException("MCP client not available for session: " + sessionId));
        }

        // 更新会话活跃时间
        session.updateLastActiveAt();

        return processMessage(session, message)
                .doOnError(error -> {
                    log.error("Error processing message for session {}: {}", sessionId, error.getMessage());
                    session.sendEvent(SseEventType.ERROR_EVENT_TYPE,
                            "Error processing message: " + error.getMessage());
                });
    }

    /**
     * 关闭会话
     */
    public Mono<Void> closeSession(String sessionId) {
        return Mono.fromRunnable(() -> {
            McpSseSession session = activeSessions.remove(sessionId);
            if (session != null) {
                log.info("Closing SSE session: {}", sessionId);
                session.close();
            }
        });
    }

    /**
     * 获取活跃会话数量
     */
    public int getActiveSessionCount() {
        return activeSessions.size();
    }

    /**
     * 获取用户的活跃会话
     */
    public Map<String, McpSseSession> getUserSessions(Long userId) {
        return activeSessions.entrySet().stream()
                .filter(entry -> userId.equals(entry.getValue().getUserId()))
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));
    }

    private String generateSessionId() {
        return UUID.randomUUID().toString();
    }

    private Mono<Void> initializeSession(McpSseSession session, McpServerConfiguration config) {
        return proxyService.startServer(config, session.getUserId())
                .flatMap(mcpInstance -> {
                    // 通过sandboxEnvironment获取沙箱实例
                    return sandboxEnvironment.getSandbox(mcpInstance.getSandboxId())
                            .flatMap(sandboxInstance -> {
                                session.setSandboxInstance(sandboxInstance);

                                // 创建MCP客户端
                                McpClient mcpClient = new McpClient(sandboxInstance);
                                session.setMcpClient(mcpClient);

                                // 初始化MCP会话
                                return mcpClient.initialize()
                                        .doOnNext(response -> {
                                            // 更新会话状态为活跃
                                            session.setStatus(McpSseSession.SessionStatus.ACTIVE);

                                            // 发送端点信息事件
                                            sendEndpointInfo(session);

                                            // 订阅MCP通知
                                            subscribeToMcpNotifications(session, mcpClient);
                                        })
                                        .then();
                            });
                })
                .onErrorResume(error -> {
                    log.error("Failed to initialize session {}: {}", session.getSessionId(), error.getMessage());
                    return Mono.error(error);
                });
    }

    private void sendEndpointInfo(McpSseSession session) {
        session.sendEvent(SseEventType.ENDPOINT_EVENT_TYPE, "/api/mcp/sse/message?sessionId=" + session.getSessionId());
    }

    private void subscribeToMcpNotifications(McpSseSession session, McpClient mcpClient) {
        mcpClient.getNotifications()
                .subscribe(
                        notification -> {
                            try {
                                String data = objectMapper.writeValueAsString(notification);
                                session.sendEvent(SseEventType.MESSAGE_EVENT_TYPE, data);
                            } catch (JsonProcessingException e) {
                                log.error("Failed to serialize MCP notification: {}", e.getMessage());
                            }
                        },
                        error -> {
                            log.error("Error in MCP notification stream for session {}: {}",
                                    session.getSessionId(), error.getMessage());
                            session.sendEvent(SseEventType.ERROR_EVENT_TYPE,
                                    "MCP notification error: " + error.getMessage());
                        }
                );
    }

    private Mono<Void> processMessage(McpSseSession session, Object message) {
        try {
            // 将消息转换为JSON-RPC格式
            JsonRpcMessage rpcMessage;
            if (message instanceof JsonRpcMessage) {
                rpcMessage = (JsonRpcMessage) message;
            } else {
                // 尝试从Map或其他格式转换
                rpcMessage = objectMapper.convertValue(message, JsonRpcRequest.class);
            }

            // 根据消息类型处理
            if (rpcMessage instanceof JsonRpcRequest) {
                return handleJsonRpcRequest(session, (JsonRpcRequest) rpcMessage);
            } else {
                return Mono.error(new RuntimeException("Unsupported message type"));
            }

        } catch (Exception e) {
            return Mono.error(new RuntimeException("Failed to process message: " + e.getMessage()));
        }
    }

    private Mono<Void> handleJsonRpcRequest(McpSseSession session, JsonRpcRequest request) {
        McpClient mcpClient = session.getMcpClient();

        // 根据方法名路由到相应的处理器
        String method = request.getMethod();

        return switch (method) {
            case "tools/list" -> mcpClient.listTools()
                    .flatMap(tools -> sendJsonRpcResponse(session, request.getId(), tools));
            case "tools/call" -> {
                McpToolCallRequest toolCallRequest = objectMapper.convertValue(request.getParams(), McpToolCallRequest.class);
                yield mcpClient.callTool(toolCallRequest.getName(), toolCallRequest.getArguments())
                        .flatMap(response -> sendJsonRpcResponse(session, request.getId(), response));
            }
            default -> sendJsonRpcError(session, request.getId(), -32601, "Method not found: " + method);
        };
    }

    private Mono<Void> sendJsonRpcResponse(McpSseSession session, Object requestId, Object result) {
        try {
            JsonRpcResponse response = new JsonRpcResponse(requestId, result);
            String data = objectMapper.writeValueAsString(response);
            session.sendEvent(SseEventType.MESSAGE_EVENT_TYPE, data);
            return Mono.empty();
        } catch (JsonProcessingException e) {
            return Mono.error(new RuntimeException("Failed to serialize response: " + e.getMessage()));
        }
    }

    private Mono<Void> sendJsonRpcError(McpSseSession session, Object requestId, int code, String message) {
        try {
            JsonRpcResponse response = new JsonRpcResponse(requestId, null);
            response.setError(new com.example.springvueapp.mcp.protocol.JsonRpcError(code, message, null));
            String data = objectMapper.writeValueAsString(response);
            session.sendEvent(SseEventType.ERROR_EVENT_TYPE, data);
            return Mono.empty();
        } catch (JsonProcessingException e) {
            return Mono.error(new RuntimeException("Failed to serialize error response: " + e.getMessage()));
        }
    }

    private void startSessionCleanupTask() {
        scheduler.scheduleAtFixedRate(() -> {
            LocalDateTime cutoff = LocalDateTime.now().minusMinutes(SESSION_TIMEOUT_MINUTES);

            activeSessions.entrySet().removeIf(entry -> {
                McpSseSession session = entry.getValue();
                if (session.getLastActiveAt().isBefore(cutoff) || session.isClosed()) {
                    log.info("Cleaning up inactive session: {}", entry.getKey());
                    session.close();
                    return true;
                }
                return false;
            });

        }, SESSION_TIMEOUT_MINUTES, SESSION_TIMEOUT_MINUTES, TimeUnit.MINUTES);
    }
}
