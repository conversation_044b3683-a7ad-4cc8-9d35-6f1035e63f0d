package com.example.springvueapp.mcp.repository;

import com.example.springvueapp.mcp.entity.McpServerInstanceEntity;
import com.example.springvueapp.sandbox.SandboxStatus;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Repository for MCP server instances
 */
@Repository
public interface McpServerInstanceRepository extends R2dbcRepository<McpServerInstanceEntity, Long> {
    
    /**
     * Find instances by user ID
     */
    Flux<McpServerInstanceEntity> findByUserId(Long userId);

    /**
     * Find instances by configuration ID
     */
    Mono<McpServerInstanceEntity> findByConfigurationId(Long configurationId);

    /**
     * Find instances by status
     */
    Flux<McpServerInstanceEntity> findByStatus(SandboxStatus status);

    /**
     * Find instance by sandbox ID
     */
    Mono<McpServerInstanceEntity> findBySandboxId(String sandboxId);

    /**
     * Find running instances by user ID
     */
    Flux<McpServerInstanceEntity> findByUserIdAndStatus(Long userId, SandboxStatus status);
}
