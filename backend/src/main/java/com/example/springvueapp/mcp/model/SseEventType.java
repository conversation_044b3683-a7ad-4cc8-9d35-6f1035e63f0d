package com.example.springvueapp.mcp.model;

/**
 * SSE事件类型枚举
 * 定义MCP SSE通信中使用的事件类型
 */
public enum SseEventType {
    /**
     * 端点事件类型 - 用于端点发现，返回message endpoint和sessionId
     */
    ENDPOINT_EVENT_TYPE("endpoint"),
    
    /**
     * 消息事件类型 - 用于MCP消息传输
     */
    MESSAGE_EVENT_TYPE("message"),
    
    /**
     * 错误事件类型 - 用于错误信息传输
     */
    ERROR_EVENT_TYPE("error"),
    
    /**
     * 连接关闭事件类型
     */
    CLOSE_EVENT_TYPE("close");
    
    private final String eventName;
    
    SseEventType(String eventName) {
        this.eventName = eventName;
    }
    
    public String getEventName() {
        return eventName;
    }
    
    @Override
    public String toString() {
        return eventName;
    }
}
