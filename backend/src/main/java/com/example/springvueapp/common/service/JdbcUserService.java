package com.example.springvueapp.common.service;

import com.example.springvueapp.common.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.sql.ResultSet;
import java.util.List;
import java.util.Optional;

/**
 * 使用传统 JDBC 实现的用户服务
 * 适用于不支持 R2DBC 的数据库或需要特定 JDBC 功能的场景
 */
@Service
public class JdbcUserService {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public JdbcUserService(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 用户 RowMapper
     */
    private static final RowMapper<User> USER_ROW_MAPPER = (ResultSet rs, int rowNum) -> {
        User user = new User();
        user.setId(rs.getLong("id"));
        user.setUsername(rs.getString("username"));
        user.setPassword(rs.getString("password"));
        user.setEmail(rs.getString("email"));
        user.setFullName(rs.getString("full_name"));
        user.setEnabled(rs.getBoolean("enabled"));
        return user;
    };

    /**
     * 通过用户名查找用户 - 阻塞式 JDBC 操作
     */
    public Optional<User> findByUsername(String username) {
        String sql = "SELECT * FROM users WHERE username = ?";
        List<User> users = jdbcTemplate.query(sql, USER_ROW_MAPPER, username);
        return users.isEmpty() ? Optional.empty() : Optional.of(users.get(0));
    }

    /**
     * 通过用户名查找用户 - 包装为响应式 Mono
     * 这种方式可以在响应式代码中调用阻塞式 JDBC 操作
     */
    public Mono<User> findByUsernameReactive(String username) {
        return Mono.fromCallable(() -> findByUsername(username))
                .flatMap(optionalUser -> optionalUser.map(Mono::just).orElseGet(Mono::empty))
                .subscribeOn(Schedulers.boundedElastic()); // 在单独的线程上执行阻塞操作
    }

    /**
     * 保存用户 - 阻塞式 JDBC 操作
     */
    public User save(User user) {
        if (user.getId() == null) {
            // 插入新用户
            String sql = "INSERT INTO users (username, password, email, full_name, enabled) VALUES (?, ?, ?, ?, ?)";
            jdbcTemplate.update(sql, 
                user.getUsername(), 
                user.getPassword(), 
                user.getEmail(), 
                user.getFullName(), 
                user.isEnabled()
            );
            
            // 获取新插入用户的ID
            String idSql = "SELECT id FROM users WHERE username = ?";
            Long id = jdbcTemplate.queryForObject(idSql, Long.class, user.getUsername());
            user.setId(id);
        } else {
            // 更新现有用户
            String sql = "UPDATE users SET username = ?, password = ?, email = ?, full_name = ?, enabled = ? WHERE id = ?";
            jdbcTemplate.update(sql, 
                user.getUsername(), 
                user.getPassword(), 
                user.getEmail(), 
                user.getFullName(), 
                user.isEnabled(), 
                user.getId()
            );
        }
        return user;
    }

    /**
     * 保存用户 - 包装为响应式 Mono
     */
    public Mono<User> saveReactive(User user) {
        return Mono.fromCallable(() -> save(user))
                .subscribeOn(Schedulers.boundedElastic()); // 在单独的线程上执行阻塞操作
    }

    /**
     * 检查用户名是否存在 - 阻塞式 JDBC 操作
     */
    public boolean existsByUsername(String username) {
        String sql = "SELECT COUNT(*) FROM users WHERE username = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, username);
        return count != null && count > 0;
    }

    /**
     * 检查用户名是否存在 - 包装为响应式 Mono
     */
    public Mono<Boolean> existsByUsernameReactive(String username) {
        return Mono.fromCallable(() -> existsByUsername(username))
                .subscribeOn(Schedulers.boundedElastic()); // 在单独的线程上执行阻塞操作
    }
}
