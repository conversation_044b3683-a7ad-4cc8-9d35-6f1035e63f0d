package com.example.springvueapp.common.controller;

import com.example.springvueapp.common.model.User;
import com.example.springvueapp.common.service.HybridUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 用户控制器 - 展示如何使用混合服务
 */
@RestController
@RequestMapping("/users")
public class UserController {

    private final HybridUserService userService;

    @Autowired
    public UserController(HybridUserService userService) {
        this.userService = userService;
    }

    /**
     * 根据用户名查找用户
     */
    @GetMapping("/{username}")
    public Mono<ResponseEntity<User>> getUserByUsername(@PathVariable String username) {
        return userService.findUserByUsername(username)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    /**
     * 复杂查询示例
     */
    @GetMapping("/search")
    public Flux<User> searchUsers(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "false") boolean activeOnly) {
        return userService.findUsersByComplexCriteria(keyword, activeOnly);
    }

    /**
     * 创建或更新用户
     */
    @PostMapping
    public Mono<ResponseEntity<User>> createOrUpdateUser(@RequestBody User user) {
        return userService.saveUser(user)
                .map(ResponseEntity::ok);
    }

    /**
     * 使用事务创建用户
     */
    @PostMapping("/with-transaction")
    public Mono<ResponseEntity<User>> createUserWithTransaction(@RequestBody User user) {
        return userService.createUserWithTransaction(user)
                .map(ResponseEntity::ok);
    }
}
