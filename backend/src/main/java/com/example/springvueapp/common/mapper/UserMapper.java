package com.example.springvueapp.common.mapper;

import com.example.springvueapp.common.entity.UserEntity;
import com.example.springvueapp.common.model.User;
import org.springframework.stereotype.Component;

/**
 * 用户实体和DTO之间的转换器
 */
@Component
public class UserMapper {

    /**
     * 将Entity转换为DTO
     */
    public User toDto(UserEntity entity) {
        if (entity == null) {
            return null;
        }

        return User.builder()
                .id(entity.getId())
                .username(entity.getUsername())
                .password(entity.getPassword()) // 注意：在实际应用中可能不需要返回密码
                .email(entity.getEmail())
                .fullName(entity.getFullName())
                .enabled(entity.isEnabled())
                .build();
    }

    /**
     * 将DTO转换为Entity
     */
    public UserEntity toEntity(User dto) {
        if (dto == null) {
            return null;
        }

        return UserEntity.builder()
                .id(dto.getId())
                .username(dto.getUsername())
                .password(dto.getPassword())
                .email(dto.getEmail())
                .fullName(dto.getFullName())
                .enabled(dto.isEnabled())
                .build();
    }

    /**
     * 将Entity转换为DTO（不包含密码）
     */
    public User toDtoWithoutPassword(UserEntity entity) {
        if (entity == null) {
            return null;
        }

        return User.builder()
                .id(entity.getId())
                .username(entity.getUsername())
                .email(entity.getEmail())
                .fullName(entity.getFullName())
                .enabled(entity.isEnabled())
                .build();
    }
}
