package com.example.springvueapp.devops.mapper;

import com.example.springvueapp.devops.entity.DevOpsResourceEntity;
import com.example.springvueapp.devops.model.DevOpsResource;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * DevOps资源的实体与DTO转换器
 */
@Component
public class DevOpsResourceMapper {

    /**
     * 将实体转换为DTO
     * @param entity 资源实体
     * @return 资源DTO
     */
    public DevOpsResource toDto(DevOpsResourceEntity entity) {
        if (entity == null) {
            return null;
        }

        return DevOpsResource.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .type(entity.getType())
                .componentId(entity.getComponentId())
                .path(entity.getPath())
                .status(entity.getStatus())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     * @param dto 资源DTO
     * @return 资源实体
     */
    public DevOpsResourceEntity toEntity(DevOpsResource dto) {
        if (dto == null) {
            return null;
        }

        return DevOpsResourceEntity.builder()
                .id(dto.getId())
                .name(dto.getName())
                .description(dto.getDescription())
                .type(dto.getType())
                .componentId(dto.getComponentId())
                .path(dto.getPath())
                .status(dto.getStatus())
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为新建实体（不包含ID和时间戳）
     * @param dto 资源DTO
     * @param componentId 组件ID
     * @param userId 用户ID
     * @return 资源实体
     */
    public DevOpsResourceEntity toNewEntity(DevOpsResource dto, Long componentId, Long userId) {
        if (dto == null) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        return DevOpsResourceEntity.builder()
                .name(dto.getName())
                .description(dto.getDescription())
                .type(dto.getType())
                .componentId(componentId)
                .path(dto.getPath())
                .status(dto.getStatus() != null ? dto.getStatus() : "ACTIVE")
                .userId(userId)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }

    /**
     * 将DTO转换为更新实体（保留ID，更新时间戳）
     * @param dto 资源DTO
     * @param existingEntity 现有实体
     * @return 更新后的资源实体
     */
    public DevOpsResourceEntity toUpdateEntity(DevOpsResource dto, DevOpsResourceEntity existingEntity) {
        if (dto == null || existingEntity == null) {
            return null;
        }

        return DevOpsResourceEntity.builder()
                .id(existingEntity.getId())
                .name(dto.getName() != null ? dto.getName() : existingEntity.getName())
                .description(dto.getDescription() != null ? dto.getDescription() : existingEntity.getDescription())
                .type(dto.getType() != null ? dto.getType() : existingEntity.getType())
                .componentId(existingEntity.getComponentId())
                .path(dto.getPath() != null ? dto.getPath() : existingEntity.getPath())
                .status(dto.getStatus() != null ? dto.getStatus() : existingEntity.getStatus())
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }
}
