package com.example.springvueapp.devops.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * DevOps项目的数据库实体类
 * 对应devops_projects表
 */
@Table("devops_projects")
public class DevOpsProjectEntity {

    @Id
    private Long id;

    private String name;

    private String description;

    private String status;

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsProjectEntity() {
    }

    public DevOpsProjectEntity(Long id, String name, String description, String status, 
                              Long userId, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.status = status;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsProjectEntity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", status='" + status + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsProjectEntity 的构建器类
     */
    public static class Builder {
        private DevOpsProjectEntity project = new DevOpsProjectEntity();

        public Builder id(Long id) {
            project.setId(id);
            return this;
        }

        public Builder name(String name) {
            project.setName(name);
            return this;
        }

        public Builder description(String description) {
            project.setDescription(description);
            return this;
        }

        public Builder status(String status) {
            project.setStatus(status);
            return this;
        }

        public Builder userId(Long userId) {
            project.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            project.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            project.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsProjectEntity build() {
            return project;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
