package com.example.springvueapp.devops.repository;

import com.example.springvueapp.devops.entity.DevOpsResourceEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps资源的响应式Repository接口
 */
@Repository
public interface DevOpsResourceRepository extends ReactiveCrudRepository<DevOpsResourceEntity, Long> {

    /**
     * 根据用户ID查找所有资源
     * @param userId 用户ID
     * @return 资源列表
     */
    Flux<DevOpsResourceEntity> findByUserId(Long userId);

    /**
     * 根据组件ID查找所有资源
     * @param componentId 组件ID
     * @return 资源列表
     */
    Flux<DevOpsResourceEntity> findByComponentId(Long componentId);

    /**
     * 根据用户ID和组件ID查找资源
     * @param userId 用户ID
     * @param componentId 组件ID
     * @return 资源列表
     */
    Flux<DevOpsResourceEntity> findByUserIdAndComponentId(Long userId, Long componentId);

    /**
     * 根据用户ID和资源名称查找资源
     * @param userId 用户ID
     * @param name 资源名称
     * @return 资源实体
     */
    Mono<DevOpsResourceEntity> findByUserIdAndName(Long userId, String name);

    /**
     * 根据组件ID和资源名称查找资源
     * @param componentId 组件ID
     * @param name 资源名称
     * @return 资源实体
     */
    Mono<DevOpsResourceEntity> findByComponentIdAndName(Long componentId, String name);

    /**
     * 根据用户ID和状态查找资源
     * @param userId 用户ID
     * @param status 资源状态
     * @return 资源列表
     */
    Flux<DevOpsResourceEntity> findByUserIdAndStatus(Long userId, String status);

    /**
     * 根据用户ID和资源类型查找资源
     * @param userId 用户ID
     * @param type 资源类型
     * @return 资源列表
     */
    Flux<DevOpsResourceEntity> findByUserIdAndType(Long userId, String type);

    /**
     * 根据组件ID和资源类型查找资源
     * @param componentId 组件ID
     * @param type 资源类型
     * @return 资源列表
     */
    Flux<DevOpsResourceEntity> findByComponentIdAndType(Long componentId, String type);

    /**
     * 检查资源名称在组件范围内是否存在
     * @param componentId 组件ID
     * @param name 资源名称
     * @return 是否存在
     */
    Mono<Boolean> existsByComponentIdAndName(Long componentId, String name);

    /**
     * 根据用户ID和资源ID查找资源
     * @param userId 用户ID
     * @param id 资源ID
     * @return 资源实体
     */
    Mono<DevOpsResourceEntity> findByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID删除资源
     * @param userId 用户ID
     * @param id 资源ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_resources WHERE user_id = :userId AND id = :id")
    Mono<Integer> deleteByUserIdAndId(Long userId, Long id);

    /**
     * 统计组件的资源数量
     * @param componentId 组件ID
     * @return 资源数量
     */
    Mono<Long> countByComponentId(Long componentId);

    /**
     * 统计用户的资源数量
     * @param userId 用户ID
     * @return 资源数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 统计指定类型的资源数量
     * @param userId 用户ID
     * @param type 资源类型
     * @return 资源数量
     */
    Mono<Long> countByUserIdAndType(Long userId, String type);

    /**
     * 根据名称模糊查询用户的资源
     * @param userId 用户ID
     * @param namePattern 名称模式
     * @return 资源列表
     */
    @Query("SELECT * FROM devops_resources WHERE user_id = :userId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsResourceEntity> findByUserIdAndNameContaining(Long userId, String namePattern);

    /**
     * 根据组件ID和名称模糊查询资源
     * @param componentId 组件ID
     * @param namePattern 名称模式
     * @return 资源列表
     */
    @Query("SELECT * FROM devops_resources WHERE component_id = :componentId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsResourceEntity> findByComponentIdAndNameContaining(Long componentId, String namePattern);

    /**
     * 根据路径查找资源
     * @param userId 用户ID
     * @param path 资源路径
     * @return 资源实体
     */
    Mono<DevOpsResourceEntity> findByUserIdAndPath(Long userId, String path);

    /**
     * 检查路径是否已被使用
     * @param componentId 组件ID
     * @param path 资源路径
     * @return 是否存在
     */
    Mono<Boolean> existsByComponentIdAndPath(Long componentId, String path);

    /**
     * 获取所有资源类型
     * @param userId 用户ID
     * @return 资源类型列表
     */
    @Query("SELECT DISTINCT type FROM devops_resources WHERE user_id = :userId ORDER BY type")
    Flux<String> findDistinctTypesByUserId(Long userId);
}
