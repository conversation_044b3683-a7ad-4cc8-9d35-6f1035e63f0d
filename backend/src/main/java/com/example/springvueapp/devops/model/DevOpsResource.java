package com.example.springvueapp.devops.model;

import java.time.LocalDateTime;

/**
 * DevOps资源的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 * 对应Maven子模块概念
 */
public class DevOpsResource {

    private Long id;

    private String name;

    private String description;

    private String type;

    private Long componentId;

    private String path;

    private String status;

    private Long userId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public DevOpsResource() {
    }

    public DevOpsResource(Long id, String name, String description, String type,
                         Long componentId, String path, String status,
                         Long userId, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.type = type;
        this.componentId = componentId;
        this.path = path;
        this.status = status;
        this.userId = userId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getComponentId() {
        return componentId;
    }

    public void setComponentId(Long componentId) {
        this.componentId = componentId;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "DevOpsResource{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", type='" + type + '\'' +
                ", componentId=" + componentId +
                ", path='" + path + '\'' +
                ", status='" + status + '\'' +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * DevOpsResource 的构建器类
     */
    public static class Builder {
        private DevOpsResource resource = new DevOpsResource();

        public Builder id(Long id) {
            resource.setId(id);
            return this;
        }

        public Builder name(String name) {
            resource.setName(name);
            return this;
        }

        public Builder description(String description) {
            resource.setDescription(description);
            return this;
        }

        public Builder type(String type) {
            resource.setType(type);
            return this;
        }

        public Builder componentId(Long componentId) {
            resource.setComponentId(componentId);
            return this;
        }

        public Builder path(String path) {
            resource.setPath(path);
            return this;
        }

        public Builder status(String status) {
            resource.setStatus(status);
            return this;
        }

        public Builder userId(Long userId) {
            resource.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            resource.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            resource.setUpdatedAt(updatedAt);
            return this;
        }

        public DevOpsResource build() {
            return resource;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
