package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.model.DevOpsCiTask;
import com.example.springvueapp.devops.model.DevOpsCiTaskInstance;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * CI任务服务抽象接口
 * 定义标准CI操作方法，支持多种CI平台实现
 */
public interface CiTaskService {

    /**
     * 创建CI任务
     * @param ciTask CI任务信息
     * @param componentId 组件ID
     * @param userId 用户ID
     * @return 创建的CI任务
     */
    Mono<DevOpsCiTask> createCiTask(DevOpsCiTask ciTask, Long componentId, Long userId);

    /**
     * 更新CI任务
     * @param taskId 任务ID
     * @param ciTask 更新的CI任务信息
     * @param userId 用户ID
     * @return 更新后的CI任务
     */
    Mono<DevOpsCiTask> updateCiTask(Long taskId, DevOpsCiTask ciTask, Long userId);

    /**
     * 删除CI任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 删除结果
     */
    Mono<Boolean> deleteCiTask(Long taskId, Long userId);

    /**
     * 根据ID获取CI任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return CI任务信息
     */
    Mono<DevOpsCiTask> getCiTaskById(Long taskId, Long userId);

    /**
     * 获取组件的所有CI任务
     * @param componentId 组件ID
     * @param userId 用户ID
     * @return CI任务列表
     */
    Flux<DevOpsCiTask> getCiTasksByComponent(Long componentId, Long userId);

    /**
     * 获取用户的所有CI任务
     * @param userId 用户ID
     * @return CI任务列表
     */
    Flux<DevOpsCiTask> getAllCiTasks(Long userId);

    /**
     * 启动CI任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param parameters 启动参数
     * @return 任务实例
     */
    Mono<DevOpsCiTaskInstance> startCiTask(Long taskId, Long userId, Map<String, Object> parameters);

    /**
     * 停止CI任务实例
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 停止结果
     */
    Mono<Boolean> stopCiTaskInstance(String instanceId, Long userId);

    /**
     * 取消CI任务实例
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 取消结果
     */
    Mono<Boolean> cancelCiTaskInstance(String instanceId, Long userId);

    /**
     * 获取CI任务实例状态
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 任务实例信息
     */
    Mono<DevOpsCiTaskInstance> getCiTaskInstanceStatus(String instanceId, Long userId);

    /**
     * 获取CI任务的所有实例
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 任务实例列表
     */
    Flux<DevOpsCiTaskInstance> getCiTaskInstances(Long taskId, Long userId);

    /**
     * 获取CI任务实例日志
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 日志内容
     */
    Mono<String> getCiTaskInstanceLogs(String instanceId, Long userId);

    /**
     * 清理已完成的CI任务实例
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param keepCount 保留的实例数量
     * @return 清理的实例数量
     */
    Mono<Integer> cleanupCompletedInstances(Long taskId, Long userId, int keepCount);

    /**
     * 验证CI任务配置
     * @param configuration 配置信息
     * @return 验证结果
     */
    Mono<Boolean> validateConfiguration(Map<String, Object> configuration);

    /**
     * 获取CI任务模板
     * @param taskType 任务类型
     * @return 模板配置
     */
    Mono<Map<String, Object>> getTaskTemplate(String taskType);

    /**
     * 获取支持的任务类型
     * @return 任务类型列表
     */
    Flux<String> getSupportedTaskTypes();

    /**
     * 检查CI平台连接状态
     * @return 连接状态
     */
    Mono<Boolean> checkConnection();

    /**
     * 获取CI平台信息
     * @return 平台信息
     */
    Mono<Map<String, Object>> getPlatformInfo();
}
