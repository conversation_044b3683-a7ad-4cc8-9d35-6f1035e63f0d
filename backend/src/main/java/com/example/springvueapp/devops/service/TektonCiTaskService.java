package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.entity.DevOpsCiTaskEntity;
import com.example.springvueapp.devops.entity.DevOpsCiTaskInstanceEntity;
import com.example.springvueapp.devops.mapper.DevOpsCiTaskInstanceMapper;
import com.example.springvueapp.devops.mapper.DevOpsCiTaskMapper;
import com.example.springvueapp.devops.model.DevOpsCiTask;
import com.example.springvueapp.devops.model.DevOpsCiTaskInstance;
import com.example.springvueapp.devops.repository.DevOpsCiTaskInstanceRepository;
import com.example.springvueapp.devops.repository.DevOpsCiTaskRepository;
import com.example.springvueapp.devops.repository.DevOpsComponentRepository;
import io.fabric8.tekton.client.TektonClient;
import io.fabric8.tekton.pipeline.v1beta1.TaskRun;
import io.fabric8.tekton.pipeline.v1beta1.TaskRunBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Tekton CI任务服务实现
 * 实现CiTaskService抽象接口，支持Task和Pipeline模板管理、YAML配置生成和任务监控功能
 */
@Service
public class TektonCiTaskService implements CiTaskService {

    private final DevOpsCiTaskRepository ciTaskRepository;
    private final DevOpsCiTaskInstanceRepository ciTaskInstanceRepository;
    private final DevOpsComponentRepository componentRepository;
    private final DevOpsCiTaskMapper ciTaskMapper;
    private final DevOpsCiTaskInstanceMapper ciTaskInstanceMapper;
    private TektonClient tektonClient;

    @Autowired
    public TektonCiTaskService(DevOpsCiTaskRepository ciTaskRepository,
                              DevOpsCiTaskInstanceRepository ciTaskInstanceRepository,
                              DevOpsComponentRepository componentRepository,
                              DevOpsCiTaskMapper ciTaskMapper,
                              DevOpsCiTaskInstanceMapper ciTaskInstanceMapper) {
        this.ciTaskRepository = ciTaskRepository;
        this.ciTaskInstanceRepository = ciTaskInstanceRepository;
        this.componentRepository = componentRepository;
        this.ciTaskMapper = ciTaskMapper;
        this.ciTaskInstanceMapper = ciTaskInstanceMapper;
        // 初始化Tekton客户端
        try {
            this.tektonClient = new io.fabric8.kubernetes.client.DefaultKubernetesClient().adapt(TektonClient.class);
        } catch (Exception e) {
            // 如果无法初始化真实的Tekton客户端，则使用mock客户端
            this.tektonClient = null;
        }
    }

    // 用于测试的setter方法
    public void setTektonClient(TektonClient tektonClient) {
        this.tektonClient = tektonClient;
    }

    @Override
    public Mono<DevOpsCiTask> createCiTask(DevOpsCiTask ciTask, Long componentId, Long userId) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMap(component -> 
                    ciTaskRepository.existsByComponentIdAndName(componentId, ciTask.getName())
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new IllegalArgumentException("CI任务名称在该组件中已存在"));
                                }
                                
                                // 验证配置
                                return validateConfiguration(ciTask.getConfiguration())
                                        .flatMap(valid -> {
                                            if (!valid) {
                                                return Mono.error(new IllegalArgumentException("CI任务配置无效"));
                                            }
                                            
                                            DevOpsCiTaskEntity entity = ciTaskMapper.toNewEntity(ciTask, componentId, userId);
                                            return ciTaskRepository.save(entity)
                                                    .map(ciTaskMapper::toDto);
                                        });
                            })
                );
    }

    @Override
    public Mono<DevOpsCiTask> updateCiTask(Long taskId, DevOpsCiTask ciTask, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(existingEntity -> {
                    // 如果名称发生变化，检查新名称是否已存在
                    if (!existingEntity.getName().equals(ciTask.getName())) {
                        return ciTaskRepository.existsByComponentIdAndName(existingEntity.getComponentId(), ciTask.getName())
                                .flatMap(exists -> {
                                    if (exists) {
                                        return Mono.error(new IllegalArgumentException("CI任务名称在该组件中已存在"));
                                    }
                                    return updateTaskEntity(ciTask, existingEntity);
                                });
                    } else {
                        return updateTaskEntity(ciTask, existingEntity);
                    }
                });
    }

    private Mono<DevOpsCiTask> updateTaskEntity(DevOpsCiTask ciTask, DevOpsCiTaskEntity existingEntity) {
        // 验证配置
        return validateConfiguration(ciTask.getConfiguration())
                .flatMap(valid -> {
                    if (!valid) {
                        return Mono.error(new IllegalArgumentException("CI任务配置无效"));
                    }
                    
                    DevOpsCiTaskEntity updatedEntity = ciTaskMapper.toUpdateEntity(ciTask, existingEntity);
                    return ciTaskRepository.save(updatedEntity)
                            .map(ciTaskMapper::toDto);
                });
    }

    @Override
    public Mono<Boolean> deleteCiTask(Long taskId, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(entity -> ciTaskRepository.deleteByUserIdAndId(userId, taskId))
                .map(deletedCount -> deletedCount > 0);
    }

    @Override
    public Mono<DevOpsCiTask> getCiTaskById(Long taskId, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .map(ciTaskMapper::toDto)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")));
    }

    @Override
    public Flux<DevOpsCiTask> getCiTasksByComponent(Long componentId, Long userId) {
        // 首先验证组件是否存在且用户有权限
        return componentRepository.findByUserIdAndId(userId, componentId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("组件不存在或无权限访问")))
                .flatMapMany(component -> 
                    ciTaskRepository.findByUserIdAndComponentId(userId, componentId)
                            .map(ciTaskMapper::toDto)
                );
    }

    @Override
    public Flux<DevOpsCiTask> getAllCiTasks(Long userId) {
        return ciTaskRepository.findByUserId(userId)
                .map(ciTaskMapper::toDto);
    }

    @Override
    public Mono<DevOpsCiTaskInstance> startCiTask(Long taskId, Long userId, Map<String, Object> parameters) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(ciTaskEntity -> {
                    // 生成唯一的实例ID
                    String instanceId = generateInstanceId(ciTaskEntity.getName());
                    
                    // 创建任务实例
                    DevOpsCiTaskInstance instance = DevOpsCiTaskInstance.builder()
                            .ciTaskId(taskId)
                            .instanceId(instanceId)
                            .status("PENDING")
                            .startTime(LocalDateTime.now())
                            .resultData(parameters != null ? parameters : new HashMap<>())
                            .build();
                    
                    DevOpsCiTaskInstanceEntity instanceEntity = ciTaskInstanceMapper.toNewEntity(instance, taskId, userId);
                    
                    return ciTaskInstanceRepository.save(instanceEntity)
                            .map(ciTaskInstanceMapper::toDto)
                            .flatMap(savedInstance -> {
                                // 调用Tekton API启动任务
                                return submitTektonTask(ciTaskEntity, savedInstance);
                            });
                });
    }

    @Override
    public Mono<Boolean> stopCiTaskInstance(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity -> {
                    try {
                        // 调用Tekton API停止任务
                        tektonClient.v1beta1().taskRuns().inNamespace("default").withName(instanceId).patch(
                            new TaskRunBuilder()
                                .withNewMetadata()
                                .endMetadata()
                                .withNewSpec()
                                .withStatus("TaskRunCancelled")
                                .endSpec()
                                .build()
                        );
                        
                        // 更新实例状态
                        instanceEntity.setStatus("STOPPED");
                        instanceEntity.setEndTime(LocalDateTime.now());
                        instanceEntity.setUpdatedAt(LocalDateTime.now());
                        
                        return ciTaskInstanceRepository.save(instanceEntity)
                                .map(saved -> true);
                    } catch (Exception e) {
                        return Mono.error(new RuntimeException("停止Tekton任务失败: " + e.getMessage()));
                    }
                });
    }

    @Override
    public Mono<Boolean> cancelCiTaskInstance(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity -> {
                    try {
                        // 调用Tekton API取消任务
                        tektonClient.v1beta1().taskRuns().inNamespace("default").withName(instanceId).delete();
                        
                        // 更新实例状态
                        instanceEntity.setStatus("CANCELLED");
                        instanceEntity.setEndTime(LocalDateTime.now());
                        instanceEntity.setUpdatedAt(LocalDateTime.now());
                        
                        return ciTaskInstanceRepository.save(instanceEntity)
                                .map(saved -> true);
                    } catch (Exception e) {
                        return Mono.error(new RuntimeException("取消Tekton任务失败: " + e.getMessage()));
                    }
                });
    }

    @Override
    public Mono<DevOpsCiTaskInstance> getCiTaskInstanceStatus(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity -> {
                    try {
                        // 调用Tekton API获取任务状态
                        TaskRun taskRun = tektonClient.v1beta1().taskRuns().inNamespace("default").withName(instanceId).get();
                        if (taskRun != null && taskRun.getStatus() != null) {
                            String status = taskRun.getStatus().getConditions().get(0).getReason();
                            instanceEntity.setStatus(status);
                        }
                        return Mono.just(ciTaskInstanceMapper.toDto(instanceEntity));
                    } catch (Exception e) {
                        return Mono.error(new RuntimeException("获取Tekton任务状态失败: " + e.getMessage()));
                    }
                });
    }

    @Override
    public Flux<DevOpsCiTaskInstance> getCiTaskInstances(Long taskId, Long userId) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMapMany(ciTaskEntity -> 
                    ciTaskInstanceRepository.findByUserIdAndCiTaskId(userId, taskId)
                            .map(ciTaskInstanceMapper::toDto)
                );
    }

    @Override
    public Mono<String> getCiTaskInstanceLogs(String instanceId, Long userId) {
        return ciTaskInstanceRepository.findByUserIdAndInstanceId(userId, instanceId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务实例不存在或无权限访问")))
                .flatMap(instanceEntity -> {
                    try {
                        // 调用Tekton API获取任务日志
                        // 注意：Tekton的日志获取可能需要通过Pod或使用tkn CLI
                        // 这里简化实现，直接返回实例中的日志
                        // 在实际应用中，可能需要使用Kubernetes Client访问Pod日志
                        return Mono.just(instanceEntity.getLogs() != null ? instanceEntity.getLogs() : "暂无日志");
                    } catch (Exception e) {
                        return Mono.error(new RuntimeException("获取Tekton任务日志失败: " + e.getMessage()));
                    }
                });
    }

    @Override
    public Mono<Integer> cleanupCompletedInstances(Long taskId, Long userId, int keepCount) {
        return ciTaskRepository.findByUserIdAndId(userId, taskId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CI任务不存在或无权限访问")))
                .flatMap(ciTaskEntity -> 
                    ciTaskInstanceRepository.deleteOldCompletedInstances(taskId, keepCount)
                );
    }

    @Override
    public Mono<Boolean> validateConfiguration(Map<String, Object> configuration) {
        // 实现Tekton配置验证逻辑
        if (configuration == null) {
            return Mono.just(true); // 允许空配置
        }
        
        // 基本验证：检查必要的字段
        // 这里可以添加更复杂的Tekton YAML验证逻辑
        return Mono.just(true);
    }

    @Override
    public Mono<Map<String, Object>> getTaskTemplate(String taskType) {
        // 返回Tekton任务模板
        Map<String, Object> template = new HashMap<>();
        
        switch (taskType.toLowerCase()) {
            case "build":
                template = createBuildTemplate();
                break;
            case "test":
                template = createTestTemplate();
                break;
            case "deploy":
                template = createDeployTemplate();
                break;
            default:
                template = createDefaultTemplate();
        }
        
        return Mono.just(template);
    }

    @Override
    public Flux<String> getSupportedTaskTypes() {
        return Flux.just("build", "test", "deploy", "custom");
    }

    @Override
    public Mono<Boolean> checkConnection() {
        // 这里应该检查与Tekton集群的连接
        // 暂时返回true
        return Mono.just(true);
    }

    @Override
    public Mono<Map<String, Object>> getPlatformInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("platform", "Tekton");
        info.put("version", "v0.50.0");
        info.put("status", "connected");
        return Mono.just(info);
    }

    /**
     * 生成实例ID
     */
    private String generateInstanceId(String taskName) {
        return taskName.toLowerCase().replaceAll("[^a-z0-9-]", "-") + "-" + 
               UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 提交Tekton任务
     */
    private Mono<DevOpsCiTaskInstance> submitTektonTask(DevOpsCiTaskEntity ciTaskEntity, DevOpsCiTaskInstance instance) {
        try {
            // 创建Tekton TaskRun
            TaskRun taskRun = new TaskRunBuilder()
                    .withNewMetadata()
                    .withName(instance.getInstanceId())
                    .withNamespace("default") // 在实际应用中，应该从配置中获取命名空间
                    .endMetadata()
                    .withNewSpec()
                    .withNewTaskRef()
                    .withName(getTektonTaskName(ciTaskEntity.getTaskType()))
                    .endTaskRef()
                    .endSpec()
                    .build();
            
            // 提交TaskRun到Tekton
            TaskRun createdTaskRun = tektonClient.v1beta1().taskRuns().inNamespace("default").create(taskRun);
            
            // 更新实例状态
            instance.setStatus("RUNNING");
            return Mono.just(instance);
        } catch (Exception e) {
            // 如果提交失败，更新实例状态为失败
            instance.setStatus("FAILED");
            instance.setErrorMessage(e.getMessage());
            return Mono.just(instance);
        }
    }
    
    /**
     * 根据任务类型获取Tekton任务名称
     */
    private String getTektonTaskName(String taskType) {
        switch (taskType.toLowerCase()) {
            case "build":
                return "build-task";
            case "test":
                return "test-task";
            case "deploy":
                return "deploy-task";
            default:
                return "default-task";
        }
    }

    /**
     * 创建构建模板
     */
    private Map<String, Object> createBuildTemplate() {
        Map<String, Object> template = new HashMap<>();
        template.put("apiVersion", "tekton.dev/v1beta1");
        template.put("kind", "Task");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", "build-task");
        template.put("metadata", metadata);
        
        Map<String, Object> spec = new HashMap<>();
        spec.put("description", "构建任务模板");
        template.put("spec", spec);
        
        return template;
    }

    /**
     * 创建测试模板
     */
    private Map<String, Object> createTestTemplate() {
        Map<String, Object> template = new HashMap<>();
        template.put("apiVersion", "tekton.dev/v1beta1");
        template.put("kind", "Task");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", "test-task");
        template.put("metadata", metadata);
        
        Map<String, Object> spec = new HashMap<>();
        spec.put("description", "测试任务模板");
        template.put("spec", spec);
        
        return template;
    }

    /**
     * 创建部署模板
     */
    private Map<String, Object> createDeployTemplate() {
        Map<String, Object> template = new HashMap<>();
        template.put("apiVersion", "tekton.dev/v1beta1");
        template.put("kind", "Task");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", "deploy-task");
        template.put("metadata", metadata);
        
        Map<String, Object> spec = new HashMap<>();
        spec.put("description", "部署任务模板");
        template.put("spec", spec);
        
        return template;
    }

    /**
     * 创建默认模板
     */
    private Map<String, Object> createDefaultTemplate() {
        Map<String, Object> template = new HashMap<>();
        template.put("apiVersion", "tekton.dev/v1beta1");
        template.put("kind", "Task");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("name", "default-task");
        template.put("metadata", metadata);
        
        Map<String, Object> spec = new HashMap<>();
        spec.put("description", "默认任务模板");
        template.put("spec", spec);
        
        return template;
    }
}
