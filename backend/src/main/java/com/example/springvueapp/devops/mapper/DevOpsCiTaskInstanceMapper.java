package com.example.springvueapp.devops.mapper;

import com.example.springvueapp.devops.entity.DevOpsCiTaskInstanceEntity;
import com.example.springvueapp.devops.model.DevOpsCiTaskInstance;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * DevOps CI任务实例的实体与DTO转换器
 */
@Component
public class DevOpsCiTaskInstanceMapper {

    private final ObjectMapper objectMapper;

    @Autowired
    public DevOpsCiTaskInstanceMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 将实体转换为DTO
     * @param entity CI任务实例实体
     * @return CI任务实例DTO
     */
    public DevOpsCiTaskInstance toDto(DevOpsCiTaskInstanceEntity entity) {
        if (entity == null) {
            return null;
        }

        Map<String, Object> resultData = parseResultData(entity.getResultData());

        return DevOpsCiTaskInstance.builder()
                .id(entity.getId())
                .ciTaskId(entity.getCiTaskId())
                .instanceId(entity.getInstanceId())
                .status(entity.getStatus())
                .startTime(entity.getStartTime())
                .endTime(entity.getEndTime())
                .logs(entity.getLogs())
                .resultData(resultData)
                .errorMessage(entity.getErrorMessage())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     * @param dto CI任务实例DTO
     * @return CI任务实例实体
     */
    public DevOpsCiTaskInstanceEntity toEntity(DevOpsCiTaskInstance dto) {
        if (dto == null) {
            return null;
        }

        String resultDataJson = serializeResultData(dto.getResultData());

        return DevOpsCiTaskInstanceEntity.builder()
                .id(dto.getId())
                .ciTaskId(dto.getCiTaskId())
                .instanceId(dto.getInstanceId())
                .status(dto.getStatus())
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime())
                .logs(dto.getLogs())
                .resultData(resultDataJson)
                .errorMessage(dto.getErrorMessage())
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为新建实体（不包含ID和时间戳）
     * @param dto CI任务实例DTO
     * @param ciTaskId CI任务ID
     * @param userId 用户ID
     * @return CI任务实例实体
     */
    public DevOpsCiTaskInstanceEntity toNewEntity(DevOpsCiTaskInstance dto, Long ciTaskId, Long userId) {
        if (dto == null) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        String resultDataJson = serializeResultData(dto.getResultData());

        return DevOpsCiTaskInstanceEntity.builder()
                .ciTaskId(ciTaskId)
                .instanceId(dto.getInstanceId())
                .status(dto.getStatus() != null ? dto.getStatus() : "PENDING")
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime())
                .logs(dto.getLogs())
                .resultData(resultDataJson)
                .errorMessage(dto.getErrorMessage())
                .userId(userId)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }

    /**
     * 将DTO转换为更新实体（保留ID，更新时间戳）
     * @param dto CI任务实例DTO
     * @param existingEntity 现有实体
     * @return 更新后的CI任务实例实体
     */
    public DevOpsCiTaskInstanceEntity toUpdateEntity(DevOpsCiTaskInstance dto, DevOpsCiTaskInstanceEntity existingEntity) {
        if (dto == null || existingEntity == null) {
            return null;
        }

        String resultDataJson = dto.getResultData() != null ? 
                serializeResultData(dto.getResultData()) : existingEntity.getResultData();

        return DevOpsCiTaskInstanceEntity.builder()
                .id(existingEntity.getId())
                .ciTaskId(existingEntity.getCiTaskId())
                .instanceId(existingEntity.getInstanceId())
                .status(dto.getStatus() != null ? dto.getStatus() : existingEntity.getStatus())
                .startTime(dto.getStartTime() != null ? dto.getStartTime() : existingEntity.getStartTime())
                .endTime(dto.getEndTime() != null ? dto.getEndTime() : existingEntity.getEndTime())
                .logs(dto.getLogs() != null ? dto.getLogs() : existingEntity.getLogs())
                .resultData(resultDataJson)
                .errorMessage(dto.getErrorMessage() != null ? dto.getErrorMessage() : existingEntity.getErrorMessage())
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 解析结果数据JSON字符串为Map
     * @param resultDataJson JSON字符串
     * @return 结果数据Map
     */
    private Map<String, Object> parseResultData(String resultDataJson) {
        if (resultDataJson == null || resultDataJson.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(resultDataJson, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            // 如果解析失败，返回空Map并记录错误
            return new HashMap<>();
        }
    }

    /**
     * 序列化结果数据Map为JSON字符串
     * @param resultData 结果数据Map
     * @return JSON字符串
     */
    private String serializeResultData(Map<String, Object> resultData) {
        if (resultData == null || resultData.isEmpty()) {
            return "{}";
        }

        try {
            return objectMapper.writeValueAsString(resultData);
        } catch (JsonProcessingException e) {
            // 如果序列化失败，返回空JSON对象
            return "{}";
        }
    }
}
