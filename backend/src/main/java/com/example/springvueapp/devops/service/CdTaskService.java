package com.example.springvueapp.devops.service;

import com.example.springvueapp.devops.model.DevOpsCdTask;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * CD任务服务抽象接口
 * 定义标准CD操作方法，支持多种CD平台实现
 */
public interface CdTaskService {

    /**
     * 创建CD任务
     * @param cdTask CD任务信息
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return 创建的CD任务
     */
    Mono<DevOpsCdTask> createCdTask(DevOpsCdTask cdTask, Long applicationId, Long userId);

    /**
     * 更新CD任务
     * @param taskId 任务ID
     * @param cdTask 更新的CD任务信息
     * @param userId 用户ID
     * @return 更新后的CD任务
     */
    Mono<DevOpsCdTask> updateCdTask(Long taskId, DevOpsCdTask cdTask, Long userId);

    /**
     * 删除CD任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 删除结果
     */
    Mono<Boolean> deleteCdTask(Long taskId, Long userId);

    /**
     * 根据ID获取CD任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return CD任务信息
     */
    Mono<DevOpsCdTask> getCdTaskById(Long taskId, Long userId);

    /**
     * 获取应用的所有CD任务
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return CD任务列表
     */
    Flux<DevOpsCdTask> getCdTasksByApplication(Long applicationId, Long userId);

    /**
     * 获取用户的所有CD任务
     * @param userId 用户ID
     * @return CD任务列表
     */
    Flux<DevOpsCdTask> getAllCdTasks(Long userId);

    /**
     * 启动CD任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param parameters 启动参数
     * @return 部署结果
     */
    Mono<Map<String, Object>> startCdTask(Long taskId, Long userId, Map<String, Object> parameters);

    /**
     * 停止CD任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 停止结果
     */
    Mono<Boolean> stopCdTask(Long taskId, Long userId);

    /**
     * 获取CD任务状态
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 任务状态信息
     */
    Mono<Map<String, Object>> getCdTaskStatus(Long taskId, Long userId);

    /**
     * 获取CD任务部署历史
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 部署历史列表
     */
    Flux<Map<String, Object>> getCdTaskDeploymentHistory(Long taskId, Long userId);

    /**
     * 回滚CD任务到指定版本
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param targetVersion 目标版本
     * @return 回滚结果
     */
    Mono<Map<String, Object>> rollbackCdTask(Long taskId, Long userId, String targetVersion);

    /**
     * 获取资源包
     * @param componentVersions 组件版本信息
     * @return 资源包信息
     */
    Mono<Map<String, Object>> getResourcePackages(Map<String, String> componentVersions);

    /**
     * 生成部署命令
     * @param cdTask CD任务
     * @param parameters 部署参数
     * @return 部署命令
     */
    Mono<String> generateDeploymentCommand(DevOpsCdTask cdTask, Map<String, Object> parameters);

    /**
     * 验证CD任务配置
     * @param configuration 配置信息
     * @return 验证结果
     */
    Mono<Boolean> validateConfiguration(Map<String, Object> configuration);

    /**
     * 获取部署模板
     * @param templateType 模板类型
     * @return 模板内容
     */
    Mono<Map<String, Object>> getDeploymentTemplate(String templateType);

    /**
     * 获取支持的部署类型
     * @return 部署类型列表
     */
    Flux<String> getSupportedDeploymentTypes();

    /**
     * 检查CD平台连接状态
     * @return 连接状态
     */
    Mono<Boolean> checkConnection();

    /**
     * 获取CD平台信息
     * @return 平台信息
     */
    Mono<Map<String, Object>> getPlatformInfo();
}
