package com.example.springvueapp.devops.repository;

import com.example.springvueapp.devops.entity.DevOpsDeploymentTemplateEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps部署模板的响应式Repository接口
 */
@Repository
public interface DevOpsDeploymentTemplateRepository extends ReactiveCrudRepository<DevOpsDeploymentTemplateEntity, Long> {

    /**
     * 根据用户ID查找所有部署模板
     * @param userId 用户ID
     * @return 部署模板列表
     */
    Flux<DevOpsDeploymentTemplateEntity> findByUserId(Long userId);

    /**
     * 根据用户ID和模板名称查找部署模板
     * @param userId 用户ID
     * @param name 模板名称
     * @return 部署模板实体
     */
    Mono<DevOpsDeploymentTemplateEntity> findByUserIdAndName(Long userId, String name);

    /**
     * 根据用户ID和模板类型查找部署模板
     * @param userId 用户ID
     * @param templateType 模板类型
     * @return 部署模板列表
     */
    Flux<DevOpsDeploymentTemplateEntity> findByUserIdAndTemplateType(Long userId, String templateType);

    /**
     * 检查模板名称在用户范围内是否存在
     * @param userId 用户ID
     * @param name 模板名称
     * @return 是否存在
     */
    Mono<Boolean> existsByUserIdAndName(Long userId, String name);

    /**
     * 根据用户ID和模板ID查找部署模板
     * @param userId 用户ID
     * @param id 模板ID
     * @return 部署模板实体
     */
    Mono<DevOpsDeploymentTemplateEntity> findByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID删除部署模板
     * @param userId 用户ID
     * @param id 模板ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_deployment_templates WHERE user_id = :userId AND id = :id")
    Mono<Integer> deleteByUserIdAndId(Long userId, Long id);

    /**
     * 统计用户的部署模板数量
     * @param userId 用户ID
     * @return 部署模板数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 统计指定类型的部署模板数量
     * @param userId 用户ID
     * @param templateType 模板类型
     * @return 部署模板数量
     */
    Mono<Long> countByUserIdAndTemplateType(Long userId, String templateType);

    /**
     * 根据名称模糊查询用户的部署模板
     * @param userId 用户ID
     * @param namePattern 名称模式
     * @return 部署模板列表
     */
    @Query("SELECT * FROM devops_deployment_templates WHERE user_id = :userId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsDeploymentTemplateEntity> findByUserIdAndNameContaining(Long userId, String namePattern);

    /**
     * 获取所有模板类型
     * @param userId 用户ID
     * @return 模板类型列表
     */
    @Query("SELECT DISTINCT template_type FROM devops_deployment_templates WHERE user_id = :userId ORDER BY template_type")
    Flux<String> findDistinctTemplateTypesByUserId(Long userId);

    /**
     * 根据模板类型查找默认模板
     * @param templateType 模板类型
     * @return 默认模板
     */
    @Query("SELECT * FROM devops_deployment_templates WHERE template_type = :templateType AND name LIKE '%default%' LIMIT 1")
    Mono<DevOpsDeploymentTemplateEntity> findDefaultTemplateByType(String templateType);

    /**
     * 获取最近使用的模板
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近使用的模板列表
     */
    @Query("SELECT * FROM devops_deployment_templates WHERE user_id = :userId ORDER BY updated_at DESC LIMIT :limit")
    Flux<DevOpsDeploymentTemplateEntity> findRecentlyUsedTemplates(Long userId, int limit);
}
