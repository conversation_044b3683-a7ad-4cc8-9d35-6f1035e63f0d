package com.example.springvueapp.devops.repository;

import com.example.springvueapp.devops.entity.DevOpsCiTaskInstanceEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps CI任务实例的响应式Repository接口
 */
@Repository
public interface DevOpsCiTaskInstanceRepository extends ReactiveCrudRepository<DevOpsCiTaskInstanceEntity, Long> {

    /**
     * 根据用户ID查找所有CI任务实例
     * @param userId 用户ID
     * @return CI任务实例列表
     */
    Flux<DevOpsCiTaskInstanceEntity> findByUserId(Long userId);

    /**
     * 根据CI任务ID查找所有实例
     * @param ciTaskId CI任务ID
     * @return CI任务实例列表
     */
    Flux<DevOpsCiTaskInstanceEntity> findByCiTaskId(Long ciTaskId);

    /**
     * 根据用户ID和CI任务ID查找实例
     * @param userId 用户ID
     * @param ciTaskId CI任务ID
     * @return CI任务实例列表
     */
    Flux<DevOpsCiTaskInstanceEntity> findByUserIdAndCiTaskId(Long userId, Long ciTaskId);

    /**
     * 根据实例ID查找CI任务实例
     * @param instanceId 实例ID
     * @return CI任务实例
     */
    Mono<DevOpsCiTaskInstanceEntity> findByInstanceId(String instanceId);

    /**
     * 根据用户ID和实例ID查找CI任务实例
     * @param userId 用户ID
     * @param instanceId 实例ID
     * @return CI任务实例
     */
    Mono<DevOpsCiTaskInstanceEntity> findByUserIdAndInstanceId(Long userId, String instanceId);

    /**
     * 根据用户ID和状态查找CI任务实例
     * @param userId 用户ID
     * @param status 实例状态
     * @return CI任务实例列表
     */
    Flux<DevOpsCiTaskInstanceEntity> findByUserIdAndStatus(Long userId, String status);

    /**
     * 根据CI任务ID和状态查找实例
     * @param ciTaskId CI任务ID
     * @param status 实例状态
     * @return CI任务实例列表
     */
    Flux<DevOpsCiTaskInstanceEntity> findByCiTaskIdAndStatus(Long ciTaskId, String status);

    /**
     * 根据用户ID和实例ID查找CI任务实例
     * @param userId 用户ID
     * @param id 实例ID
     * @return CI任务实例
     */
    Mono<DevOpsCiTaskInstanceEntity> findByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID删除CI任务实例
     * @param userId 用户ID
     * @param id 实例ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_ci_task_instances WHERE user_id = :userId AND id = :id")
    Mono<Integer> deleteByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID和实例ID删除CI任务实例
     * @param userId 用户ID
     * @param instanceId 实例ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_ci_task_instances WHERE user_id = :userId AND instance_id = :instanceId")
    Mono<Integer> deleteByUserIdAndInstanceId(Long userId, String instanceId);

    /**
     * 统计CI任务的实例数量
     * @param ciTaskId CI任务ID
     * @return 实例数量
     */
    Mono<Long> countByCiTaskId(Long ciTaskId);

    /**
     * 统计用户的CI任务实例数量
     * @param userId 用户ID
     * @return 实例数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 统计指定状态的实例数量
     * @param userId 用户ID
     * @param status 实例状态
     * @return 实例数量
     */
    Mono<Long> countByUserIdAndStatus(Long userId, String status);

    /**
     * 获取CI任务的最新实例
     * @param ciTaskId CI任务ID
     * @param limit 限制数量
     * @return CI任务实例列表
     */
    @Query("SELECT * FROM devops_ci_task_instances WHERE ci_task_id = :ciTaskId ORDER BY created_at DESC LIMIT :limit")
    Flux<DevOpsCiTaskInstanceEntity> findLatestByCiTaskId(Long ciTaskId, int limit);

    /**
     * 获取用户的最新实例
     * @param userId 用户ID
     * @param limit 限制数量
     * @return CI任务实例列表
     */
    @Query("SELECT * FROM devops_ci_task_instances WHERE user_id = :userId ORDER BY created_at DESC LIMIT :limit")
    Flux<DevOpsCiTaskInstanceEntity> findLatestByUserId(Long userId, int limit);

    /**
     * 删除已完成的旧实例，保留指定数量
     * @param ciTaskId CI任务ID
     * @param keepCount 保留数量
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_ci_task_instances WHERE ci_task_id = :ciTaskId AND status IN ('COMPLETED', 'FAILED', 'CANCELLED') AND id NOT IN (SELECT id FROM devops_ci_task_instances WHERE ci_task_id = :ciTaskId AND status IN ('COMPLETED', 'FAILED', 'CANCELLED') ORDER BY created_at DESC LIMIT :keepCount)")
    Mono<Integer> deleteOldCompletedInstances(Long ciTaskId, int keepCount);

    /**
     * 获取正在运行的实例
     * @param userId 用户ID
     * @return 正在运行的实例列表
     */
    @Query("SELECT * FROM devops_ci_task_instances WHERE user_id = :userId AND status IN ('RUNNING', 'PENDING', 'STARTING') ORDER BY created_at DESC")
    Flux<DevOpsCiTaskInstanceEntity> findRunningInstances(Long userId);

    /**
     * 检查实例ID是否存在
     * @param instanceId 实例ID
     * @return 是否存在
     */
    Mono<Boolean> existsByInstanceId(String instanceId);
}
