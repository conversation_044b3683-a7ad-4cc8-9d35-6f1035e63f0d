package com.example.springvueapp.devops.mapper;

import com.example.springvueapp.devops.entity.DevOpsCdTaskEntity;
import com.example.springvueapp.devops.model.DevOpsCdTask;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * DevOps CD任务的实体与DTO转换器
 */
@Component
public class DevOpsCdTaskMapper {

    private final ObjectMapper objectMapper;

    @Autowired
    public DevOpsCdTaskMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 将实体转换为DTO（别名方法，与CI保持一致）
     * @param entity CD任务实体
     * @return CD任务DTO
     */
    public DevOpsCdTask toDto(DevOpsCdTaskEntity entity) {
        return toModel(entity);
    }

    /**
     * 将实体转换为模型
     * @param entity CD任务实体
     * @return CD任务DTO
     */
    public DevOpsCdTask toModel(DevOpsCdTaskEntity entity) {
        if (entity == null) {
            return null;
        }

        Map<String, String> componentVersions = parseComponentVersions(entity.getComponentVersions());
        Map<String, Object> configuration = parseConfiguration(entity.getConfiguration());

        return DevOpsCdTask.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .applicationId(entity.getApplicationId())
                .componentVersions(componentVersions)
                .status(entity.getStatus())
                .configuration(configuration)
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     * @param dto CD任务DTO
     * @return CD任务实体
     */
    public DevOpsCdTaskEntity toEntity(DevOpsCdTask dto) {
        if (dto == null) {
            return null;
        }

        String componentVersionsJson = serializeComponentVersions(dto.getComponentVersions());
        String configurationJson = serializeConfiguration(dto.getConfiguration());

        return DevOpsCdTaskEntity.builder()
                .id(dto.getId())
                .name(dto.getName())
                .description(dto.getDescription())
                .applicationId(dto.getApplicationId())
                .componentVersions(componentVersionsJson)
                .status(dto.getStatus())
                .configuration(configurationJson)
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为新建实体（不包含ID和时间戳）
     * @param dto CD任务DTO
     * @param applicationId 应用ID
     * @param userId 用户ID
     * @return CD任务实体
     */
    public DevOpsCdTaskEntity toNewEntity(DevOpsCdTask dto, Long applicationId, Long userId) {
        if (dto == null) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        String componentVersionsJson = serializeComponentVersions(dto.getComponentVersions());
        String configurationJson = serializeConfiguration(dto.getConfiguration());

        return DevOpsCdTaskEntity.builder()
                .name(dto.getName())
                .description(dto.getDescription())
                .applicationId(applicationId)
                .componentVersions(componentVersionsJson)
                .status(dto.getStatus() != null ? dto.getStatus() : "INACTIVE")
                .configuration(configurationJson)
                .userId(userId)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }

    /**
     * 将DTO转换为更新实体（保留ID，更新时间戳）
     * @param dto CD任务DTO
     * @param existingEntity 现有实体
     * @return 更新后的CD任务实体
     */
    public DevOpsCdTaskEntity toUpdateEntity(DevOpsCdTask dto, DevOpsCdTaskEntity existingEntity) {
        if (dto == null || existingEntity == null) {
            return null;
        }

        String componentVersionsJson = dto.getComponentVersions() != null ? 
                serializeComponentVersions(dto.getComponentVersions()) : existingEntity.getComponentVersions();
        String configurationJson = dto.getConfiguration() != null ? 
                serializeConfiguration(dto.getConfiguration()) : existingEntity.getConfiguration();

        return DevOpsCdTaskEntity.builder()
                .id(existingEntity.getId())
                .name(dto.getName() != null ? dto.getName() : existingEntity.getName())
                .description(dto.getDescription() != null ? dto.getDescription() : existingEntity.getDescription())
                .applicationId(existingEntity.getApplicationId())
                .componentVersions(componentVersionsJson)
                .status(dto.getStatus() != null ? dto.getStatus() : existingEntity.getStatus())
                .configuration(configurationJson)
                .userId(existingEntity.getUserId())
                .createdAt(existingEntity.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 解析组件版本信息JSON字符串
     * @param componentVersionsJson JSON字符串
     * @return 组件版本Map
     */
    private Map<String, String> parseComponentVersions(String componentVersionsJson) {
        if (componentVersionsJson == null || componentVersionsJson.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(componentVersionsJson, 
                    new TypeReference<Map<String, String>>() {});
        } catch (JsonProcessingException e) {
            // 记录错误日志，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 解析配置信息JSON字符串
     * @param configurationJson JSON字符串
     * @return 配置Map
     */
    private Map<String, Object> parseConfiguration(String configurationJson) {
        if (configurationJson == null || configurationJson.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(configurationJson, 
                    new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            // 记录错误日志，返回空Map
            return new HashMap<>();
        }
    }

    /**
     * 序列化组件版本信息为JSON字符串
     * @param componentVersions 组件版本Map
     * @return JSON字符串
     */
    private String serializeComponentVersions(Map<String, String> componentVersions) {
        if (componentVersions == null || componentVersions.isEmpty()) {
            return "{}";
        }

        try {
            return objectMapper.writeValueAsString(componentVersions);
        } catch (JsonProcessingException e) {
            // 记录错误日志，返回空JSON对象
            return "{}";
        }
    }

    /**
     * 序列化配置信息为JSON字符串
     * @param configuration 配置Map
     * @return JSON字符串
     */
    private String serializeConfiguration(Map<String, Object> configuration) {
        if (configuration == null || configuration.isEmpty()) {
            return "{}";
        }

        try {
            return objectMapper.writeValueAsString(configuration);
        } catch (JsonProcessingException e) {
            // 记录错误日志，返回空JSON对象
            return "{}";
        }
    }
}
