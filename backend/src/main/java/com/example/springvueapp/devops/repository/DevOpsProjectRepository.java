package com.example.springvueapp.devops.repository;

import com.example.springvueapp.devops.entity.DevOpsProjectEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * DevOps项目的响应式Repository接口
 */
@Repository
public interface DevOpsProjectRepository extends ReactiveCrudRepository<DevOpsProjectEntity, Long> {

    /**
     * 根据用户ID查找所有项目
     * @param userId 用户ID
     * @return 项目列表
     */
    Flux<DevOpsProjectEntity> findByUserId(Long userId);

    /**
     * 根据用户ID和项目名称查找项目
     * @param userId 用户ID
     * @param name 项目名称
     * @return 项目实体
     */
    Mono<DevOpsProjectEntity> findByUserIdAndName(Long userId, String name);

    /**
     * 根据用户ID和状态查找项目
     * @param userId 用户ID
     * @param status 项目状态
     * @return 项目列表
     */
    Flux<DevOpsProjectEntity> findByUserIdAndStatus(Long userId, String status);

    /**
     * 检查项目名称在用户范围内是否存在
     * @param userId 用户ID
     * @param name 项目名称
     * @return 是否存在
     */
    Mono<Boolean> existsByUserIdAndName(Long userId, String name);

    /**
     * 根据用户ID和项目ID查找项目
     * @param userId 用户ID
     * @param id 项目ID
     * @return 项目实体
     */
    Mono<DevOpsProjectEntity> findByUserIdAndId(Long userId, Long id);

    /**
     * 根据用户ID删除项目
     * @param userId 用户ID
     * @param id 项目ID
     * @return 删除的行数
     */
    @Query("DELETE FROM devops_projects WHERE user_id = :userId AND id = :id")
    Mono<Integer> deleteByUserIdAndId(Long userId, Long id);

    /**
     * 统计用户的项目数量
     * @param userId 用户ID
     * @return 项目数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 根据名称模糊查询用户的项目
     * @param userId 用户ID
     * @param namePattern 名称模式
     * @return 项目列表
     */
    @Query("SELECT * FROM devops_projects WHERE user_id = :userId AND name LIKE :namePattern ORDER BY created_at DESC")
    Flux<DevOpsProjectEntity> findByUserIdAndNameContaining(Long userId, String namePattern);
}
