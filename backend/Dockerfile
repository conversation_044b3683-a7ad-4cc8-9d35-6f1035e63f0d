# Multi-stage build for Spring Boot application with GraalVM Native Image support
# Stage 1: Build with GraalVM
FROM ghcr.io/graalvm/graalvm-community:17-ol8 AS graalvm-builder

# Install native-image
RUN gu install native-image

# Install Maven
RUN microdnf install -y wget tar gzip && \
    wget https://archive.apache.org/dist/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.tar.gz && \
    tar -xzf apache-maven-3.9.6-bin.tar.gz -C /opt && \
    ln -s /opt/apache-maven-3.9.6 /opt/maven && \
    rm apache-maven-3.9.6-bin.tar.gz

ENV MAVEN_HOME=/opt/maven
ENV PATH=$MAVEN_HOME/bin:$PATH

WORKDIR /app

# Copy pom.xml and download dependencies
COPY pom.xml .
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build native image
RUN mvn clean -Pnative package -DskipTests

# Stage 2: Traditional JAR build (fallback)
FROM maven:3.9-openjdk-17-slim AS jar-builder

WORKDIR /app

# Copy pom.xml and download dependencies
COPY pom.xml .
RUN mvn dependency:go-offline -B

# Copy source code and build
COPY src ./src
RUN mvn clean package -DskipTests

# Stage 3: Native runtime
FROM oraclelinux:8-slim AS native-runtime

# Install Docker CLI and other dependencies for sandbox management
RUN microdnf update -y && \
    microdnf install -y \
    curl \
    ca-certificates \
    glibc-devel \
    zlib-devel && \
    curl -fsSL https://download.docker.com/linux/centos/docker-ce.repo -o /etc/yum.repos.d/docker-ce.repo && \
    microdnf install -y docker-ce-cli && \
    microdnf clean all

# Create non-root user
RUN groupadd -r mcpproxy && useradd -r -g mcpproxy mcpproxy

WORKDIR /app

# Copy native executable
COPY --from=graalvm-builder /app/target/spring-vue-app /app/spring-vue-app

# Create data directory
RUN mkdir -p /app/data && chown -R mcpproxy:mcpproxy /app

# Switch to non-root user
USER mcpproxy

EXPOSE 8080

ENTRYPOINT ["./spring-vue-app"]

# Stage 4: Traditional JVM runtime (fallback)
FROM openjdk:17-jre-slim AS jvm-runtime

WORKDIR /app

# Install Docker CLI for sandbox management
RUN apt-get update && \
    apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release && \
    mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y docker-ce-cli && \
    rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r mcpproxy && useradd -r -g mcpproxy mcpproxy

# Copy application jar
COPY --from=jar-builder /app/target/*.jar app.jar

# Create data directory
RUN mkdir -p /app/data && chown -R mcpproxy:mcpproxy /app

# Switch to non-root user
USER mcpproxy

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
