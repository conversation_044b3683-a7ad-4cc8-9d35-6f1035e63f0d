# GraalVM Native Image 构建指南 (Spring Boot 3.x)

本文档介绍如何使用Spring Boot 3.x内置的GraalVM Native Image支持将应用编译为原生镜像。

## 前提条件

### 1. 安装GraalVM

```bash
# 下载并安装GraalVM Community Edition 17
# 方法1: 使用SDKMAN
sdk install java 17.0.9-graalce
sdk use java 17.0.9-graalce

# 方法2: 手动下载
# 从 https://github.com/graalvm/graalvm-ce-builds/releases 下载
```

### 2. 安装Native Image

```bash
# 安装native-image组件
gu install native-image

# 验证安装
native-image --version
```

### 3. 系统依赖

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install build-essential zlib1g-dev
```

#### Linux (CentOS/RHEL)
```bash
sudo yum groupinstall "Development Tools"
sudo yum install zlib-devel
```

#### macOS
```bash
xcode-select --install
```

## 构建方法

### 方法1: 使用Maven Profile

```bash
# 构建原生镜像
mvn clean -Pnative package -DskipTests

# 运行原生镜像
./target/spring-vue-app
```

### 方法2: 使用构建脚本

```bash
# 使用提供的构建脚本
./build-native.sh
```

### 方法3: 使用Docker

```bash
# 构建原生镜像Docker容器
docker build -f Dockerfile.native -t spring-vue-app:native .

# 运行原生镜像容器
docker run -p 8080:8080 spring-vue-app:native
```

### 方法4: 使用Docker Compose

```bash
# 启动原生镜像版本
docker-compose --profile native up backend-native

# 或者同时启动所有服务（包括原生后端）
docker-compose --profile native up
```

## 配置说明

### Maven配置

项目已配置了`native` profile，利用Spring Boot 3.x内置支持：

- **native-maven-plugin**: GraalVM官方Maven插件
- **主类**: `com.example.springvueapp.SpringVueAppApplication`
- **简化构建参数**: 只保留项目特定的Docker客户端配置
- **Spring Boot自动配置**: 自动处理大部分反射、资源和代理配置

### Spring Boot Native Hints

项目使用Spring Boot 3.x的Native Hints机制：

- `NativeHintsConfiguration.java`: 使用`@RegisterReflectionForBinding`注解
- **自动配置**: Spring Boot自动处理框架相关的配置
- **项目特定配置**: 只需要配置MCP协议、Docker客户端等项目特定类型
- **无需手动配置文件**: 不再需要手动维护reflect-config.json等文件

### 简化的构建参数

Spring Boot 3.x自动处理大部分配置，只需要项目特定的参数：

```xml
<buildArgs>
    <!-- Docker Java客户端需要运行时初始化 -->
    <buildArg>--initialize-at-run-time=com.github.dockerjava</buildArg>
    <buildArg>--initialize-at-run-time=org.apache.commons.compress</buildArg>
    <!-- 允许不完整的类路径，用于Docker客户端 -->
    <buildArg>--allow-incomplete-classpath</buildArg>
</buildArgs>
```

**Spring Boot自动处理的配置**：
- HTTP/HTTPS协议支持
- Netty运行时初始化
- 反射和资源配置
- 代理类配置
- 序列化配置

## 性能对比

### 启动时间
- **JVM版本**: ~3-5秒
- **Native版本**: ~0.1-0.3秒

### 内存使用
- **JVM版本**: ~200-300MB
- **Native版本**: ~50-100MB

### 文件大小
- **JAR文件**: ~50-80MB
- **Native可执行文件**: ~80-120MB

## 故障排除

### 常见问题

#### 1. 构建失败 - 缺少反射配置

**错误信息**:
```
ClassNotFoundException during image generation
```

**解决方案**:
- 检查`NativeHintsConfiguration.java`是否包含所需的类
- 添加`@RegisterReflectionForBinding`注解到相关类
- Spring Boot 3.x会自动处理大部分框架类

#### 2. 运行时错误 - 资源文件未找到

**错误信息**:
```
Resource not found: application.properties
```

**解决方案**:
- Spring Boot 3.x自动包含标准资源文件
- 对于自定义资源，使用`@ImportRuntimeHints`注解
- 检查资源文件是否在正确的classpath位置

#### 3. Docker构建失败

**错误信息**:
```
native-image command not found
```

**解决方案**:
- 确保Dockerfile使用正确的GraalVM基础镜像
- 检查`gu install native-image`是否成功执行

### 调试技巧

#### 1. 启用详细输出
```bash
mvn -Pnative package -DskipTests -Dverbose=true
```

#### 2. 生成构建报告
```bash
mvn -Pnative package -DskipTests -Dargs="--emit build-report"
```

#### 3. 使用Agent收集配置
```bash
# 运行测试并收集配置
mvn -Pnative -Dagent=true test

# 复制生成的配置
mvn -Pnative -Dagent=true test native:metadata-copy
```

## 部署建议

### 生产环境

1. **使用原生镜像**用于：
   - 容器化部署
   - 无服务器函数
   - 资源受限环境

2. **使用JVM版本**用于：
   - 开发和调试
   - 需要动态类加载的场景
   - 复杂的反射使用

### 监控和观察

原生镜像支持以下监控功能：
- Spring Boot Actuator
- Micrometer metrics
- 自定义健康检查

## 参考资源

- [GraalVM官方文档](https://www.graalvm.org/latest/reference-manual/native-image/)
- [Spring Boot Native文档](https://docs.spring.io/spring-boot/docs/current/reference/html/native-image.html)
- [GraalVM Native Build Tools](https://graalvm.github.io/native-build-tools/latest/index.html)
