# Spring Boot 3.x Native Image Dockerfile
FROM ghcr.io/graalvm/graalvm-community:17-ol8 AS builder

# Install native-image
RUN gu install native-image

# Install Maven
RUN microdnf install -y wget tar gzip && \
    wget https://archive.apache.org/dist/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.tar.gz && \
    tar -xzf apache-maven-3.9.6-bin.tar.gz -C /opt && \
    ln -s /opt/apache-maven-3.9.6 /opt/maven && \
    rm apache-maven-3.9.6-bin.tar.gz

ENV MAVEN_HOME=/opt/maven
ENV PATH=$MAVEN_HOME/bin:$PATH

WORKDIR /app

# Copy pom.xml and download dependencies
COPY pom.xml .
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build native image using Spring Boot 3.x native support
RUN mvn clean -Pnative native:compile -DskipTests

# Runtime stage - optimized for Spring Boot Native
FROM oraclelinux:8-slim

# Install minimal runtime dependencies
RUN microdnf update -y && \
    microdnf install -y \
    curl \
    ca-certificates && \
    curl -fsSL https://download.docker.com/linux/centos/docker-ce.repo -o /etc/yum.repos.d/docker-ce.repo && \
    microdnf install -y docker-ce-cli && \
    microdnf clean all

# Create non-root user
RUN groupadd -r mcpproxy && useradd -r -g mcpproxy mcpproxy

WORKDIR /app

# Copy native executable (Spring Boot 3.x generates this automatically)
COPY --from=builder /app/target/spring-vue-app /app/spring-vue-app

# Create data directory
RUN mkdir -p /app/data && chown -R mcpproxy:mcpproxy /app

# Switch to non-root user
USER mcpproxy

EXPOSE 8080

# Health check optimized for native startup
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

ENTRYPOINT ["./spring-vue-app"]
