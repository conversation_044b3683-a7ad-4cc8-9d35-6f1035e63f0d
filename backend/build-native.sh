#!/bin/bash

# Spring Boot 3.x GraalVM Native Image 构建脚本
# 使用Spring Boot内置的Native Image支持

set -e

echo "开始构建Spring Boot 3.x GraalVM原生镜像..."

# 检查是否安装了GraalVM
if ! command -v native-image &> /dev/null; then
    echo "错误: 未找到native-image命令。请确保已安装GraalVM并配置了native-image。"
    echo "安装命令: gu install native-image"
    exit 1
fi

# 检查Java版本
echo "Java版本信息:"
java -version

echo "GraalVM版本信息:"
native-image --version

# 清理之前的构建
echo "清理之前的构建..."
mvn clean

# 构建原生镜像 (使用Spring Boot 3.x native支持)
echo "开始构建原生镜像..."
mvn -Pnative native:compile -DskipTests

# 检查构建结果
if [ -f "target/spring-vue-app" ]; then
    echo "✅ 原生镜像构建成功!"
    echo "可执行文件位置: target/spring-vue-app"
    
    # 显示文件大小
    echo "文件大小:"
    ls -lh target/spring-vue-app
    
    echo ""
    echo "运行原生镜像:"
    echo "./target/spring-vue-app"
    
    echo ""
    echo "构建Docker镜像:"
    echo "docker build -f Dockerfile.native -t spring-vue-app:native ."
    
else
    echo "❌ 原生镜像构建失败!"
    exit 1
fi
