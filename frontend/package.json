{"name": "spring-vue-app-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "vue-tsc --noEmit && vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@monaco-editor/loader": "^1.5.0", "@types/js-yaml": "^4.0.9", "axios": "^1.6.2", "js-yaml": "^4.1.0", "monaco-editor": "^0.52.2", "pinia": "^3.0.0", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-v8": "^2.1.8", "@vue/test-utils": "^2.4.1", "autoprefixer": "^10.4.16", "happy-dom": "^15.11.7", "postcss": "^8.4.31", "sass": "^1.69.5", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^6.0.0", "vitest": "^2.1.8", "vue-tsc": "^2.1.10"}, "packageManager": "pnpm@8.10.0"}