// Monaco Editor mock for testing
export const editor = {
  create: () => ({
    getValue: () => '',
    setValue: () => {},
    onDidChangeModelContent: () => ({ dispose: () => {} }),
    dispose: () => {},
    layout: () => {},
    getPosition: () => ({ lineNumber: 1, column: 1 }),
    executeEdits: () => {}
  }),
  defineTheme: () => {},
  setTheme: () => {}
}

export const languages = {
  setLanguageConfiguration: () => {},
  registerCompletionItemProvider: () => ({ dispose: () => {} })
}

export const Range = class {
  constructor(
    public startLineNumber: number,
    public startColumn: number,
    public endLineNumber: number,
    public endColumn: number
  ) {}
}

export const KeyCode = {
  F1: 112,
  F8: 119,
  Escape: 27
}

export const KeyMod = {
  CtrlCmd: 2048,
  Shift: 1024,
  Alt: 512
}

export default {
  editor,
  languages,
  Range,
  KeyCode,
  KeyMod
}
