import { createRouter, createWebHistory, RouteRecordRaw, NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import LoginView from '../modules/common/views/LoginView.vue'
import HomeView from '../modules/common/views/HomeView.vue'
import McpDashboard from '../modules/mcp/views/McpDashboard.vue'
import DevOpsDashboard from '../modules/devops/views/DevOpsDashboard.vue'
import ProjectList from '../modules/devops/views/ProjectList.vue'
import ApplicationList from '../modules/devops/views/ApplicationList.vue'
import ComponentList from '../modules/devops/views/ComponentList.vue'
import ResourceList from '../modules/devops/views/ResourceList.vue'
import CIManagement from '../modules/devops/views/CIManagement.vue'
import CDManagement from '../modules/devops/views/CDManagement.vue'
import MonitoringCenter from '../modules/devops/views/MonitoringCenter.vue'
import { useAuthStore } from '../modules/common/stores/auth'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/mcp'
  },
  {
    path: '/home',
    name: 'home',
    component: HomeView,
    meta: { requiresAuth: true }
  },
  {
    path: '/mcp',
    name: 'mcp-dashboard',
    component: McpDashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/devops',
    name: 'devops-dashboard',
    component: DevOpsDashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/projects',
    name: 'devops-projects',
    component: ProjectList,
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/applications',
    name: 'devops-applications',
    component: ApplicationList,
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/components',
    name: 'devops-components',
    component: ComponentList,
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/resources',
    name: 'devops-resources',
    component: ResourceList,
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/ci',
    name: 'devops-ci',
    component: CIManagement,
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/cd',
    name: 'devops-cd',
    component: CDManagement,
    meta: { requiresAuth: true }
  },
  {
    path: '/devops/monitoring',
    name: 'devops-monitoring',
    component: MonitoringCenter,
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

router.beforeEach((
  to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const authStore = useAuthStore()

  if (to.meta && to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})

export default router
