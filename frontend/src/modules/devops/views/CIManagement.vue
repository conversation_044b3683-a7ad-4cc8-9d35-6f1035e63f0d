<template>
  <div class="ci-management">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>CI 管理</h1>
          <p>配置和管理持续集成流水线，自动化构建、测试和代码质量检查流程</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            创建CI任务
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card ci-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ ciStore.tasks.length }}</div>
          <div class="stat-label">CI 任务总数</div>
          <div class="stat-detail">
            <span class="running">{{ ciStore.runningTasks.length }} 运行中</span>
            <span class="completed">{{ ciStore.completedTasks.length }} 已完成</span>
          </div>
        </div>
      </div>

      <div class="stat-card build-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ buildTasks.length }}</div>
          <div class="stat-label">构建任务</div>
          <div class="stat-detail">
            <span class="active">{{ activeBuildTasks.length }} 活跃</span>
            <span class="failed">{{ failedBuildTasks.length }} 失败</span>
          </div>
        </div>
      </div>

      <div class="stat-card test-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ testTasks.length }}</div>
          <div class="stat-label">测试任务</div>
          <div class="stat-detail">
            <span class="success">{{ successfulTests.length }} 通过</span>
            <span class="failed">{{ failedTests.length }} 失败</span>
          </div>
        </div>
      </div>

      <div class="stat-card success-rate-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ successRate }}%</div>
          <div class="stat-label">成功率</div>
          <div class="stat-detail">
            <span class="success">{{ ciStore.completedTasks.length }} 成功</span>
            <span class="failed">{{ ciStore.failedTasks.length }} 失败</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务类型切换 -->
    <div class="task-type-tabs">
      <button
        v-for="tab in taskTypeTabs"
        :key="tab.key"
        :class="['tab-button', { active: activeTab === tab.key }]"
        @click="activeTab = tab.key"
      >
        <svg class="tab-icon" viewBox="0 0 20 20" fill="currentColor">
          <path :d="tab.icon" />
        </svg>
        {{ tab.label }}
        <span class="tab-count">{{ getTabCount(tab.key) }}</span>
      </button>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section">
      <div class="filter-group">
        <label class="filter-label">按组件筛选：</label>
        <select
          v-model="selectedComponentId"
          @change="handleComponentFilter"
          class="filter-select"
        >
          <option value="">全部组件</option>
          <option
            v-for="component in components"
            :key="component.id"
            :value="component.id"
          >
            {{ component.name }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select
          v-model="selectedStatus"
          @change="handleStatusFilter"
          class="filter-select"
        >
          <option value="">全部状态</option>
          <option value="ACTIVE">活跃</option>
          <option value="RUNNING">运行中</option>
          <option value="COMPLETED">已完成</option>
          <option value="FAILED">失败</option>
          <option value="CANCELLED">已取消</option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">任务类型：</label>
        <select
          v-model="selectedTaskType"
          @change="handleTaskTypeFilter"
          class="filter-select"
        >
          <option value="">全部类型</option>
          <option value="build">构建</option>
          <option value="test">测试</option>
          <option value="lint">代码检查</option>
          <option value="security">安全扫描</option>
        </select>
      </div>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :data="filteredTasks"
      :columns="tableColumns"
      :loading="ciStore.loading"
      title="CI 任务列表"
      searchable
      search-placeholder="搜索CI任务名称或描述..."
      @search="handleSearch"
      @row-click="handleRowClick"
    >
      <template #toolbar-actions>
        <button
          @click="refreshData"
          :disabled="ciStore.loading"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
      </template>

      <template #cell-componentName="{ record }">
        <span class="component-link" @click="viewComponent(record.componentId)">
          {{ getComponentName(record.componentId) }}
        </span>
      </template>

      <template #cell-taskType="{ value }">
        <span class="task-type-badge" :class="`type-${value?.toLowerCase()}`">
          {{ getTaskTypeLabel(value) }}
        </span>
      </template>

      <template #cell-status="{ value }">
        <StatusTag :status="value" />
      </template>

      <template #cell-duration="{ record }">
        <span class="duration-text">
          {{ formatDuration(record.startTime, record.endTime) }}
        </span>
      </template>

      <template #cell-createdAt="{ value }">
        {{ formatDate(value) }}
      </template>

      <template #actions="{ record }">
        <div class="action-buttons">
          <button
            @click="viewTask(record)"
            class="action-btn action-btn-view"
            title="查看详情"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canViewLogs(record)"
            @click="viewLogs(record)"
            class="action-btn action-btn-logs"
            title="查看日志"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
              <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canExecuteTask(record)"
            @click="executeTask(record)"
            class="action-btn action-btn-execute"
            title="执行任务"
            :disabled="executingTask"
          >
            <svg v-if="executingTask" class="animate-spin" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
              <path d="M10 2a8 8 0 018 8" fill="currentColor">
                <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
              </path>
            </svg>
            <svg v-else viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canStopTask(record)"
            @click="stopTask(record)"
            class="action-btn action-btn-stop"
            title="停止任务"
            :disabled="stoppingTask"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click="editTask(record)"
            class="action-btn action-btn-edit"
            title="编辑任务"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            @click="deleteTask(record)"
            class="action-btn action-btn-delete"
            title="删除任务"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </template>
    </DataTable>

    <!-- 创建/编辑任务模态框 -->
    <CITaskModal
      v-if="showCreateModal || showEditModal"
      :visible="showCreateModal || showEditModal"
      :task="editingTask"
      :components="components"
      @close="closeModal"
      @submit="handleTaskSubmit"
    />

    <!-- 日志查看模态框 -->
    <TaskLogsModal
      v-if="showLogsModal"
      :visible="showLogsModal"
      :task="currentTask"
      @close="closeLogsModal"
    />

    <!-- 确认删除对话框 -->
    <ConfirmDialog
      :visible="showDeleteDialog"
      title="确认删除CI任务"
      :message="`确定要删除CI任务 &quot;${deleteTarget?.name}&quot; 吗？此操作不可撤销。`"
      type="danger"
      confirm-text="删除"
      :loading="deleting"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import StatusTag from '@/components/StatusTag.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import CITaskModal from '@/modules/devops/components/CITaskModal.vue'
import TaskLogsModal from '@/modules/devops/components/TaskLogsModal.vue'
import { useCiStore } from '@/modules/devops/stores/ci'
import { useDevOpsStore } from '@/modules/devops/stores/devops'
import type { DevOpsCiTask, BreadcrumbItem, TableColumn } from '@/modules/devops/types/devops'

const router = useRouter()
const ciStore = useCiStore()
const devopsStore = useDevOpsStore()

// 响应式数据
const executingTask = ref(false)
const stoppingTask = ref(false)
const deleting = ref(false)
const searchQuery = ref('')
const selectedComponentId = ref<number | ''>('')
const selectedStatus = ref('')
const selectedTaskType = ref('')
const activeTab = ref('all')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const showLogsModal = ref(false)
const currentTask = ref<DevOpsCiTask | null>(null)
const editingTask = ref<DevOpsCiTask | null>(null)
const deleteTarget = ref<DevOpsCiTask | null>(null)

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: 'CI 管理', active: true, icon: 'ci' }
])

const components = computed(() => devopsStore.components)

const buildTasks = computed(() =>
  ciStore.tasks.filter(task => task.taskType === 'build')
)

const testTasks = computed(() =>
  ciStore.tasks.filter(task => task.taskType === 'test')
)

const activeBuildTasks = computed(() =>
  buildTasks.value.filter(task => task.status === 'ACTIVE')
)

const failedBuildTasks = computed(() =>
  buildTasks.value.filter(task => task.status === 'FAILED')
)

const successfulTests = computed(() =>
  testTasks.value.filter(task => task.status === 'COMPLETED')
)

const failedTests = computed(() =>
  testTasks.value.filter(task => task.status === 'FAILED')
)

const successRate = computed(() => {
  const total = ciStore.tasks.length
  if (total === 0) return 0
  const successful = ciStore.completedTasks.length
  return Math.round((successful / total) * 100)
})

const taskTypeTabs = [
  { key: 'all', label: '全部任务', icon: 'M4 6a2 2 0 012-2h8a2 2 0 012 2v7a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM6 8a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 4a1 1 0 100 2h6a1 1 0 100-2H7z' },
  { key: 'build', label: '构建任务', icon: 'M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z' },
  { key: 'test', label: '测试任务', icon: 'M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' },
  { key: 'lint', label: '代码检查', icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' },
  { key: 'security', label: '安全扫描', icon: 'M9 12a3 3 0 003-3m-3 3a3 3 0 01-3-3m3 3v6m-3-9a3 3 0 013-3m-3 3h6m-6 0a3 3 0 01-3-3m3 3v6' }
]

const tableColumns: TableColumn[] = [
  { key: 'name', title: '任务名称', sortable: true },
  { key: 'componentName', title: '关联组件', width: '150px' },
  { key: 'taskType', title: '任务类型', width: '100px' },
  { key: 'status', title: '状态', width: '120px' },
  { key: 'duration', title: '执行时长', width: '120px' },
  { key: 'createdAt', title: '创建时间', width: '180px', sortable: true }
]

const filteredTasks = computed(() => {
  let result = [...ciStore.tasks]

  // 按任务类型筛选
  if (activeTab.value !== 'all') {
    result = result.filter(task => task.taskType === activeTab.value)
  }

  // 按组件筛选
  if (selectedComponentId.value) {
    result = result.filter(task => task.componentId === selectedComponentId.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    result = result.filter(task => task.status === selectedStatus.value)
  }

  // 按任务类型筛选
  if (selectedTaskType.value) {
    result = result.filter(task => task.taskType === selectedTaskType.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(task =>
      task.name.toLowerCase().includes(query) ||
      (task.description && task.description.toLowerCase().includes(query))
    )
  }

  return result
})

// 方法
const refreshData = async () => {
  await ciStore.fetchTasks()
  // 如果需要组件数据，可以从devopsStore获取
}

const handleSearch = (query: string) => {
  searchQuery.value = query
}

const handleComponentFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStatusFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleTaskTypeFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleRowClick = (task: DevOpsCiTask) => {
  viewTask(task)
}

const getTabCount = (tabKey: string) => {
  if (tabKey === 'all') return ciStore.tasks.length
  return ciStore.tasks.filter(task => task.taskType === tabKey).length
}

const getComponentName = (componentId: number) => {
  const component = components.value.find(c => c.id === componentId)
  return component?.name || '未知组件'
}

const getTaskTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    build: '构建',
    test: '测试',
    lint: '代码检查',
    security: '安全扫描'
  }
  return typeMap[type] || type
}

const formatDuration = (startTime?: string, endTime?: string) => {
  if (!startTime) return '-'

  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : new Date()
  const duration = Math.floor((end.getTime() - start.getTime()) / 1000)

  if (duration < 60) return `${duration}秒`
  if (duration < 3600) return `${Math.floor(duration / 60)}分${duration % 60}秒`
  return `${Math.floor(duration / 3600)}时${Math.floor((duration % 3600) / 60)}分`
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const viewComponent = (componentId: number) => {
  router.push(`/devops/components/${componentId}`)
}

const viewTask = (task: DevOpsCiTask) => {
  router.push(`/devops/ci/${task.id}`)
}

const canViewLogs = (task: DevOpsCiTask) => {
  return task && task.status && ['RUNNING', 'COMPLETED', 'FAILED'].includes(task.status)
}

const canExecuteTask = (task: DevOpsCiTask) => {
  return task && task.status && ['ACTIVE', 'FAILED', 'CANCELLED'].includes(task.status)
}

const canStopTask = (task: DevOpsCiTask) => {
  return task && task.status === 'RUNNING'
}

const viewLogs = (task: DevOpsCiTask) => {
  currentTask.value = task
  showLogsModal.value = true
}

const executeTask = async (task: DevOpsCiTask) => {
  if (!canExecuteTask(task)) return

  executingTask.value = true
  try {
    await ciStore.executeTask(task.id!)
    // 可以显示成功消息
  } catch (error) {
    console.error('执行CI任务失败:', error)
    // 可以显示错误消息
  } finally {
    executingTask.value = false
  }
}

const stopTask = async (task: DevOpsCiTask) => {
  if (!canStopTask(task)) return

  stoppingTask.value = true
  try {
    // 这里需要实现停止任务的逻辑
    // await ciStore.stopTask(task.id!)
    console.log('停止任务:', task.name)
  } catch (error) {
    console.error('停止CI任务失败:', error)
  } finally {
    stoppingTask.value = false
  }
}

const editTask = (task: DevOpsCiTask) => {
  editingTask.value = task
  showEditModal.value = true
}

const deleteTask = (task: DevOpsCiTask) => {
  deleteTarget.value = task
  showDeleteDialog.value = true
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingTask.value = null
}

const closeLogsModal = () => {
  showLogsModal.value = false
  currentTask.value = null
}

const handleTaskSubmit = async (taskData: any) => {
  try {
    if (editingTask.value) {
      await ciStore.updateTask(editingTask.value.id!, taskData)
    } else {
      await ciStore.createTask(taskData)
    }
    closeModal()
  } catch (error) {
    console.error('保存CI任务失败:', error)
  }
}

const confirmDelete = async () => {
  if (!deleteTarget.value) return

  deleting.value = true
  try {
    await ciStore.deleteTask(deleteTarget.value.id!)
    showDeleteDialog.value = false
    deleteTarget.value = null
  } catch (error) {
    console.error('删除CI任务失败:', error)
  } finally {
    deleting.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.ci-management {
  @apply p-6 bg-gray-50 min-h-screen;
}

/* 页面头部 */
.page-header {
  @apply mb-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-2xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-3;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 flex items-center gap-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.stat-icon svg {
  @apply w-6 h-6;
}

.ci-card .stat-icon {
  @apply bg-blue-100 text-blue-600;
}

.build-card .stat-icon {
  @apply bg-green-100 text-green-600;
}

.test-card .stat-icon {
  @apply bg-purple-100 text-purple-600;
}

.success-rate-card .stat-icon {
  @apply bg-emerald-100 text-emerald-600;
}

.stat-content {
  @apply flex-1;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

.stat-detail {
  @apply flex items-center gap-3 mt-2 text-xs;
}

.stat-detail .running {
  @apply text-blue-600;
}

.stat-detail .completed {
  @apply text-green-600;
}

.stat-detail .active {
  @apply text-green-600;
}

.stat-detail .success {
  @apply text-green-600;
}

.stat-detail .failed {
  @apply text-red-600;
}

/* 任务类型切换 */
.task-type-tabs {
  @apply flex items-center gap-2 mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-2;
}

.tab-button {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors;
}

.tab-button:not(.active) {
  @apply text-gray-600 hover:text-gray-900 hover:bg-gray-50;
}

.tab-button.active {
  @apply bg-blue-100 text-blue-700;
}

.tab-icon {
  @apply w-4 h-4;
}

.tab-count {
  @apply ml-1 px-2 py-0.5 text-xs bg-gray-200 text-gray-700 rounded-full;
}

.tab-button.active .tab-count {
  @apply bg-blue-200 text-blue-800;
}

/* 筛选器部分 */
.filters-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6 flex items-center gap-6;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none;
  min-width: 150px;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-icon {
  @apply w-4 h-4;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 组件链接 */
.component-link {
  @apply text-blue-600 hover:text-blue-800 cursor-pointer hover:underline transition-colors;
}

/* 任务类型标签 */
.task-type-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
}

.task-type-badge.type-build {
  @apply bg-green-100 text-green-800;
}

.task-type-badge.type-test {
  @apply bg-purple-100 text-purple-800;
}

.task-type-badge.type-lint {
  @apply bg-yellow-100 text-yellow-800;
}

.task-type-badge.type-security {
  @apply bg-red-100 text-red-800;
}

/* 执行时长 */
.duration-text {
  @apply text-sm text-gray-600 font-mono;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-1;
}

.action-btn {
  @apply p-2 rounded-md transition-colors;
}

.action-btn svg {
  @apply w-4 h-4;
}

.action-btn-view {
  @apply text-blue-600 hover:bg-blue-50;
}

.action-btn-logs {
  @apply text-gray-600 hover:bg-gray-50;
}

.action-btn-execute {
  @apply text-green-600 hover:bg-green-50;
}

.action-btn-stop {
  @apply text-red-600 hover:bg-red-50;
}

.action-btn-edit {
  @apply text-orange-600 hover:bg-orange-50;
}

.action-btn-delete {
  @apply text-red-600 hover:bg-red-50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ci-management {
    @apply p-4;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }

  .stats-grid {
    @apply grid-cols-1;
  }

  .filters-section {
    @apply flex-col items-start gap-4;
  }

  .task-type-tabs {
    @apply flex-wrap;
  }
}
</style>