<template>
  <div class="cicd-dashboard">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>CI/CD 管理</h1>
          <p>配置和管理持续集成和持续部署流水线，自动化构建、测试和部署流程</p>
        </div>
        <div class="header-actions">
          <router-link to="/devops/ci" class="btn btn-primary">
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z" />
            </svg>
            CI 管理
          </router-link>
          <router-link to="/devops/cd" class="btn btn-secondary">
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z" />
            </svg>
            CD 管理
          </router-link>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card ci-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.ci.total }}</div>
          <div class="stat-label">CI 任务</div>
          <div class="stat-detail">
            <span class="running">{{ stats.ci.running }} 运行中</span>
            <span class="completed">{{ stats.ci.completed }} 已完成</span>
            <span class="failed">{{ stats.ci.failed }} 失败</span>
          </div>
        </div>
        <div class="stat-action">
          <router-link to="/devops/ci" class="action-link">
            管理 CI 任务 →
          </router-link>
        </div>
      </div>

      <div class="stat-card cd-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.cd.total }}</div>
          <div class="stat-label">CD 任务</div>
          <div class="stat-detail">
            <span class="running">{{ stats.cd.running }} 运行中</span>
            <span class="completed">{{ stats.cd.completed }} 已完成</span>
            <span class="failed">{{ stats.cd.failed }} 失败</span>
          </div>
        </div>
        <div class="stat-action">
          <router-link to="/devops/cd" class="action-link">
            管理 CD 任务 →
          </router-link>
        </div>
      </div>

      <div class="stat-card pipeline-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.pipelines.total }}</div>
          <div class="stat-label">流水线</div>
          <div class="stat-detail">
            <span class="active">{{ stats.pipelines.active }} 活跃</span>
            <span class="inactive">{{ stats.pipelines.inactive }} 非活跃</span>
          </div>
        </div>
      </div>

      <div class="stat-card success-rate-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.successRate }}%</div>
          <div class="stat-label">成功率</div>
          <div class="stat-detail">
            <span class="success">{{ stats.successful }} 成功</span>
            <span class="failed">{{ stats.failed }} 失败</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities">
      <div class="section-header">
        <h2>最近活动</h2>
        <div class="section-actions">
          <button @click="refreshActivities" :disabled="loading" class="btn btn-secondary">
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            刷新
          </button>
        </div>
      </div>

      <div class="activities-list">
        <div
          v-for="activity in recentActivities"
          :key="activity.id"
          class="activity-item"
          :class="`activity-${activity.type}`"
        >
          <div class="activity-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path :d="getActivityIcon(activity.type)" />
            </svg>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-description">{{ activity.description }}</div>
            <div class="activity-meta">
              <span class="activity-time">{{ formatTime(activity.createdAt) }}</span>
              <span class="activity-user">{{ activity.userName }}</span>
            </div>
          </div>
          <div class="activity-status">
            <StatusTag :status="activity.status" />
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="section-header">
        <h2>快速操作</h2>
      </div>
      <div class="actions-grid">
        <router-link to="/devops/ci/create" class="action-card">
          <div class="action-icon ci-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="action-content">
            <h3>创建 CI 任务</h3>
            <p>创建新的持续集成任务</p>
          </div>
        </router-link>

        <router-link to="/devops/cd/create" class="action-card">
          <div class="action-icon cd-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="action-content">
            <h3>创建 CD 任务</h3>
            <p>创建新的持续部署任务</p>
          </div>
        </router-link>

        <router-link to="/devops/templates" class="action-card">
          <div class="action-icon template-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" />
            </svg>
          </div>
          <div class="action-content">
            <h3>模板管理</h3>
            <p>管理 CI/CD 任务模板</p>
          </div>
        </router-link>

        <router-link to="/devops/settings" class="action-card">
          <div class="action-icon settings-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="action-content">
            <h3>系统设置</h3>
            <p>配置 CI/CD 系统参数</p>
          </div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Breadcrumb from '@/components/Breadcrumb.vue'
import StatusTag from '@/components/StatusTag.vue'
import type { BreadcrumbItem } from '@/modules/devops/types/devops'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const recentActivities = ref<any[]>([])

// 统计数据
const stats = ref({
  ci: { total: 0, running: 0, completed: 0, failed: 0 },
  cd: { total: 0, running: 0, completed: 0, failed: 0 },
  pipelines: { total: 0, active: 0, inactive: 0 },
  successRate: 0,
  successful: 0,
  failed: 0
})

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: 'CI/CD 管理', active: true, icon: 'cicd' }
])

// 方法
const loadStats = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟统计数据
    stats.value = {
      ci: { total: 15, running: 3, completed: 10, failed: 2 },
      cd: { total: 8, running: 1, completed: 6, failed: 1 },
      pipelines: { total: 23, active: 18, inactive: 5 },
      successRate: 85,
      successful: 16,
      failed: 3
    }
  } finally {
    loading.value = false
  }
}

const loadRecentActivities = async () => {
  try {
    // 模拟最近活动数据
    recentActivities.value = [
      {
        id: 1,
        type: 'ci',
        title: 'CI 任务执行完成',
        description: '用户认证服务构建任务执行成功',
        status: 'COMPLETED',
        userName: '张三',
        createdAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        type: 'cd',
        title: 'CD 部署任务启动',
        description: '订单处理引擎部署到生产环境',
        status: 'RUNNING',
        userName: '李四',
        createdAt: '2024-01-15T10:25:00Z'
      },
      {
        id: 3,
        type: 'ci',
        title: 'CI 任务执行失败',
        description: '支付网关测试任务执行失败',
        status: 'FAILED',
        userName: '王五',
        createdAt: '2024-01-15T10:20:00Z'
      }
    ]
  } catch (error) {
    console.error('加载最近活动失败:', error)
  }
}

const refreshActivities = () => {
  loadRecentActivities()
}

const getActivityIcon = (type: string) => {
  const icons = {
    ci: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z',
    cd: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z',
    pipeline: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z'
  }
  return icons[type] || icons.ci
}

const formatTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes} 分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)} 小时前`
  return `${Math.floor(minutes / 1440)} 天前`
}

// 生命周期
onMounted(() => {
  loadStats()
  loadRecentActivities()
})
</script>

<style scoped>
.cicd-dashboard {
  @apply p-6 bg-gray-50 min-h-screen;
}

/* 页面头部 */
.page-header {
  @apply mb-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-2xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex gap-3;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.stat-icon svg {
  @apply w-6 h-6;
}

.ci-card .stat-icon {
  @apply bg-blue-100 text-blue-600;
}

.cd-card .stat-icon {
  @apply bg-green-100 text-green-600;
}

.pipeline-card .stat-icon {
  @apply bg-purple-100 text-purple-600;
}

.success-rate-card .stat-icon {
  @apply bg-yellow-100 text-yellow-600;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900 mb-1;
}

.stat-label {
  @apply text-sm font-medium text-gray-600 mb-2;
}

.stat-detail {
  @apply flex flex-wrap gap-2 text-xs;
}

.stat-detail span {
  @apply px-2 py-1 rounded-full;
}

.stat-detail .running {
  @apply bg-blue-100 text-blue-700;
}

.stat-detail .completed {
  @apply bg-green-100 text-green-700;
}

.stat-detail .failed {
  @apply bg-red-100 text-red-700;
}

.stat-detail .active {
  @apply bg-green-100 text-green-700;
}

.stat-detail .inactive {
  @apply bg-gray-100 text-gray-700;
}

.stat-detail .success {
  @apply bg-green-100 text-green-700;
}

.stat-action {
  @apply mt-4 pt-4 border-t border-gray-100;
}

.action-link {
  @apply text-sm text-blue-600 hover:text-blue-800 font-medium;
}

/* 最近活动 */
.recent-activities {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 mb-8;
}

.section-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.section-header h2 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.section-actions {
  @apply flex gap-2;
}

.activities-list {
  @apply divide-y divide-gray-100;
}

.activity-item {
  @apply flex items-center gap-4 p-6 hover:bg-gray-50 transition-colors;
}

.activity-icon {
  @apply w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0;
}

.activity-icon svg {
  @apply w-5 h-5;
}

.activity-ci .activity-icon {
  @apply bg-blue-100 text-blue-600;
}

.activity-cd .activity-icon {
  @apply bg-green-100 text-green-600;
}

.activity-pipeline .activity-icon {
  @apply bg-purple-100 text-purple-600;
}

.activity-content {
  @apply flex-1 min-w-0;
}

.activity-title {
  @apply font-medium text-gray-900 mb-1;
}

.activity-description {
  @apply text-sm text-gray-600 mb-2;
}

.activity-meta {
  @apply flex items-center gap-4 text-xs text-gray-500;
}

.activity-status {
  @apply flex-shrink-0;
}

/* 快速操作 */
.quick-actions {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.actions-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-6;
}

.action-card {
  @apply block p-6 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all;
}

.action-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.action-icon svg {
  @apply w-6 h-6;
}

.ci-icon {
  @apply bg-blue-100 text-blue-600;
}

.cd-icon {
  @apply bg-green-100 text-green-600;
}

.template-icon {
  @apply bg-purple-100 text-purple-600;
}

.settings-icon {
  @apply bg-gray-100 text-gray-600;
}

.action-content h3 {
  @apply font-semibold text-gray-900 mb-2;
}

.action-content p {
  @apply text-sm text-gray-600;
}

/* 通用按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn-icon {
  @apply w-4 h-4;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}
</style>
