<template>
  <div class="application-list">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>应用管理</h1>
          <p>管理项目中的应用程序，配置应用环境和监控状态</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            创建应用
          </button>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section">
      <div class="filter-group">
        <label class="filter-label">按项目筛选：</label>
        <select
          v-model="selectedProjectId"
          @change="handleProjectFilter"
          class="filter-select"
        >
          <option value="">全部项目</option>
          <option
            v-for="project in projects"
            :key="project.id"
            :value="project.id"
          >
            {{ project.name }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select
          v-model="selectedStatus"
          @change="handleStatusFilter"
          class="filter-select"
        >
          <option value="">全部状态</option>
          <option value="ACTIVE">活跃</option>
          <option value="INACTIVE">非活跃</option>
          <option value="MAINTENANCE">维护中</option>
        </select>
      </div>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :data="filteredApplications"
      :columns="tableColumns"
      :loading="loading"
      title="应用列表"
      searchable
      search-placeholder="搜索应用名称或描述..."
      @search="handleSearch"
      @row-click="handleRowClick"
    >
      <template #toolbar-actions>
        <button
          @click="refreshData"
          :disabled="loading"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
      </template>

      <template #cell-projectName="{ record }">
        <span class="project-link" @click="viewProject(record?.projectId)">
          {{ getProjectName(record?.projectId) }}
        </span>
      </template>

      <template #cell-status="{ value }">
        <StatusTag :status="value" />
      </template>

      <template #cell-createdAt="{ value }">
        {{ formatDate(value) }}
      </template>

      <template #actions="{ record }">
        <div class="action-buttons">
          <button
            @click="viewApplication(record)"
            class="action-btn action-btn-view"
            title="查看详情"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click="viewComponents(record)"
            class="action-btn action-btn-components"
            title="查看组件"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 12a1 1 0 002 0V6.414l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 6.414V12zM3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click="editApplication(record)"
            class="action-btn action-btn-edit"
            title="编辑应用"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            @click="deleteApplication(record)"
            class="action-btn action-btn-delete"
            title="删除应用"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </template>
    </DataTable>

    <!-- 创建/编辑应用模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h3>{{ showCreateModal ? '创建应用' : '编辑应用' }}</h3>
          <button @click="closeModal" class="modal-close">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="submitForm" class="modal-form">
          <FormField
            v-model="formData.name"
            label="应用名称"
            type="text"
            placeholder="请输入应用名称"
            required
            :error-message="formErrors.name"
          />

          <FormField
            v-model="formData.description"
            label="应用描述"
            type="textarea"
            placeholder="请输入应用描述"
            :rows="3"
            :error-message="formErrors.description"
          />

          <FormField
            v-model="formData.projectId"
            label="所属项目"
            type="select"
            :options="projectOptions"
            required
            :error-message="formErrors.projectId"
          />

          <FormField
            v-model="formData.status"
            label="应用状态"
            type="select"
            :options="statusOptions"
            :error-message="formErrors.status"
          />

          <div class="modal-actions">
            <button type="button" @click="closeModal" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" :disabled="submitting" class="btn btn-primary">
              <svg v-if="submitting" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
                <path d="M10 2a8 8 0 018 8" fill="currentColor">
                  <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
                </path>
              </svg>
              {{ submitting ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 确认删除对话框 -->
    <ConfirmDialog
      :visible="showDeleteDialog"
      title="确认删除应用"
      :message="`确定要删除应用 &quot;${deleteTarget?.name}&quot; 吗？此操作将同时删除该应用下的所有组件和资源，且不可撤销。`"
      type="danger"
      confirm-text="删除"
      :loading="deleting"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import StatusTag from '@/components/StatusTag.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import FormField from '@/components/FormField.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { useDevOpsStore } from '@/modules/devops/stores/devops'
import type { DevOpsApplication, DevOpsProject, BreadcrumbItem, TableColumn } from '@/modules/devops/types/devops'

const router = useRouter()
const devopsStore = useDevOpsStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const deleting = ref(false)
const applications = ref<DevOpsApplication[]>([])
const projects = ref<DevOpsProject[]>([])
const searchQuery = ref('')
const selectedProjectId = ref<number | ''>('')
const selectedStatus = ref('')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const deleteTarget = ref<DevOpsApplication | null>(null)

// 表单数据
const formData = ref({
  name: '',
  description: '',
  projectId: null as number | null,
  status: 'ACTIVE'
})

const formErrors = ref({
  name: '',
  description: '',
  projectId: '',
  status: ''
})

const editingApplication = ref<DevOpsApplication | null>(null)

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: '应用管理', active: true, icon: 'application' }
])

const tableColumns: TableColumn[] = [
  { key: 'name', title: '应用名称', sortable: true },
  { key: 'projectName', title: '所属项目', width: '150px' },
  { key: 'description', title: '描述' },
  { key: 'status', title: '状态', width: '120px' },
  { key: 'createdAt', title: '创建时间', width: '180px', sortable: true }
]

const projectOptions = computed(() => 
  projects.value.map(project => ({
    label: project.name,
    value: project.id!
  }))
)

const statusOptions = [
  { label: '活跃', value: 'ACTIVE' },
  { label: '非活跃', value: 'INACTIVE' },
  { label: '维护中', value: 'MAINTENANCE' }
]

const filteredApplications = computed(() => {
  let result = [...applications.value]
  
  // 按项目筛选
  if (selectedProjectId.value) {
    result = result.filter(app => app.projectId === selectedProjectId.value)
  }
  
  // 按状态筛选
  if (selectedStatus.value) {
    result = result.filter(app => app.status === selectedStatus.value)
  }
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(app => 
      app.name.toLowerCase().includes(query) ||
      (app.description && app.description.toLowerCase().includes(query))
    )
  }
  
  return result
})

// 方法
const loadApplications = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    applications.value = [
      {
        id: 1,
        name: '用户服务',
        description: '用户管理和认证服务',
        projectId: 1,
        status: 'ACTIVE',
        createdAt: '2024-01-15T10:30:00Z',
        userId: 1
      },
      {
        id: 2,
        name: '订单服务',
        description: '订单处理和管理服务',
        projectId: 1,
        status: 'ACTIVE',
        createdAt: '2024-01-16T14:20:00Z',
        userId: 1
      },
      {
        id: 3,
        name: '支付服务',
        description: '支付处理和对账服务',
        projectId: 1,
        status: 'MAINTENANCE',
        createdAt: '2024-01-17T09:15:00Z',
        userId: 1
      },
      {
        id: 4,
        name: '移动端API',
        description: '移动应用后端API服务',
        projectId: 2,
        status: 'ACTIVE',
        createdAt: '2024-01-20T16:45:00Z',
        userId: 1
      }
    ]
  } finally {
    loading.value = false
  }
}

const loadProjects = async () => {
  try {
    // 模拟项目数据
    projects.value = [
      { id: 1, name: '电商平台', description: '电商平台项目', status: 'ACTIVE', createdAt: '2024-01-15T10:30:00Z', userId: 1 },
      { id: 2, name: '移动应用', description: '移动应用项目', status: 'ACTIVE', createdAt: '2024-01-20T14:20:00Z', userId: 1 },
      { id: 3, name: '数据分析平台', description: '数据分析项目', status: 'INACTIVE', createdAt: '2024-01-10T09:15:00Z', userId: 1 }
    ]
  } catch (error) {
    console.error('加载项目失败:', error)
  }
}

const refreshData = () => {
  loadApplications()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
}

const handleProjectFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStatusFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleRowClick = (application: DevOpsApplication) => {
  viewApplication(application)
}

const viewProject = (projectId: number) => {
  router.push({ path: `/devops/projects/${projectId}` })
}

const viewApplication = (application: DevOpsApplication) => {
  router.push({ path: `/devops/applications/${application.id}` })
}

const viewComponents = (application: DevOpsApplication) => {
  router.push({ path: `/devops/components`, query: { applicationId: String(application.id) } })
}

const editApplication = (application: DevOpsApplication) => {
  editingApplication.value = application
  formData.value = {
    name: application.name,
    description: application.description || '',
    projectId: application.projectId,
    status: application.status || 'ACTIVE'
  }
  showEditModal.value = true
}

const deleteApplication = (application: DevOpsApplication) => {
  deleteTarget.value = application
  showDeleteDialog.value = true
}

const getProjectName = (projectId: number) => {
  const project = projects.value.find(p => p.id === projectId)
  return project?.name || '未知项目'
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  resetForm()
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    projectId: null,
    status: 'ACTIVE'
  }
  formErrors.value = {
    name: '',
    description: '',
    projectId: '',
    status: ''
  }
  editingApplication.value = null
}

const validateForm = () => {
  const errors = { name: '', description: '', projectId: '', status: '' }
  let isValid = true

  if (!formData.value.name.trim()) {
    errors.name = '应用名称不能为空'
    isValid = false
  }

  if (!formData.value.projectId) {
    errors.projectId = '请选择所属项目'
    isValid = false
  }

  formErrors.value = errors
  return isValid
}

const submitForm = async () => {
  if (!validateForm()) return

  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (showCreateModal.value) {
      // 创建应用
      const newApplication: DevOpsApplication = {
        id: Date.now(),
        ...formData.value,
        projectId: formData.value.projectId!,
        createdAt: new Date().toISOString(),
        userId: 1
      }
      applications.value.unshift(newApplication)
    } else if (showEditModal.value && editingApplication.value) {
      // 更新应用
      const index = applications.value.findIndex(a => a.id === editingApplication.value!.id)
      if (index !== -1) {
        applications.value[index] = {
          ...applications.value[index],
          ...formData.value,
          projectId: formData.value.projectId!,
          updatedAt: new Date().toISOString()
        }
      }
    }
    
    closeModal()
  } finally {
    submitting.value = false
  }
}

const confirmDelete = async () => {
  if (!deleteTarget.value) return

  deleting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const index = applications.value.findIndex(a => a.id === deleteTarget.value!.id)
    if (index !== -1) {
      applications.value.splice(index, 1)
    }
    
    showDeleteDialog.value = false
    deleteTarget.value = null
  } finally {
    deleting.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadProjects()
  loadApplications()
})
</script>

<style scoped>
.application-list {
  @apply p-6 bg-gray-50 min-h-screen;
}

/* 页面头部 */
.page-header {
  @apply mb-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-2xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-3;
}

/* 筛选器部分 */
.filters-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6 flex items-center gap-6;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none;
  min-width: 150px;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-icon {
  @apply w-4 h-4;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 项目链接 */
.project-link {
  @apply text-blue-600 hover:text-blue-800 cursor-pointer hover:underline transition-colors;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-1;
}

.action-btn {
  @apply p-2 rounded-md transition-colors;
}

.action-btn svg {
  @apply w-4 h-4;
}

.action-btn-view {
  @apply text-blue-600 hover:bg-blue-50;
}

.action-btn-components {
  @apply text-purple-600 hover:bg-purple-50;
}

.action-btn-edit {
  @apply text-green-600 hover:bg-green-50;
}

.action-btn-delete {
  @apply text-red-600 hover:bg-red-50;
}

/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl w-full max-w-md;
}

.modal-header {
  @apply flex items-center justify-between p-6 pb-4;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 p-1 rounded;
}

.modal-close svg {
  @apply w-5 h-5;
}

.modal-form {
  @apply px-6 pb-6;
}

.modal-actions {
  @apply flex items-center justify-end gap-3 mt-6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .application-list {
    @apply p-4;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }

  .filters-section {
    @apply flex-col items-start gap-4;
  }

  .filter-group {
    @apply w-full;
  }

  .filter-select {
    @apply w-full;
  }

  .modal-container {
    @apply max-w-full mx-4;
  }

  .action-buttons {
    @apply flex-col gap-2;
  }
}

/* 动画效果 */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 悬停效果 */
.action-btn:hover {
  @apply transform scale-105;
}

.project-link:hover {
  @apply transform translate-x-1;
}
</style>
