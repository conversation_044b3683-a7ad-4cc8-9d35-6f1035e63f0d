/**
 * ApplicationList组件的单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import ApplicationList from '../ApplicationList.vue'

// Mock组件
vi.mock('@/components/DataTable.vue', () => ({
  default: {
    name: 'DataTable',
    template: '<div data-testid="data-table"><slot name="actions"></slot><slot name="cell-status"></slot><slot name="actions" :record="{}"></slot></div>',
    props: ['data', 'columns', 'loading', 'title', 'searchable', 'search-placeholder'],
    emits: ['search', 'row-click']
  }
}))

vi.mock('@/components/StatusTag.vue', () => ({
  default: {
    name: 'StatusTag',
    template: '<span data-testid="status-tag">{{ status }}</span>',
    props: ['status']
  }
}))

vi.mock('@/components/Breadcrumb.vue', () => ({
  default: {
    name: 'Breadcrumb',
    template: '<nav data-testid="breadcrumb"></nav>',
    props: ['items']
  }
}))

vi.mock('@/components/FormField.vue', () => ({
  default: {
    name: 'FormField',
    template: '<div data-testid="form-field"><input :value="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" /></div>',
    props: ['modelValue', 'label', 'type', 'placeholder', 'required', 'error-message', 'options', 'rows'],
    emits: ['update:modelValue']
  }
}))

vi.mock('@/components/ConfirmDialog.vue', () => ({
  default: {
    name: 'ConfirmDialog',
    template: '<div data-testid="confirm-dialog" v-if="visible"></div>',
    props: ['visible', 'title', 'message', 'type', 'confirm-text', 'loading'],
    emits: ['confirm', 'cancel']
  }
}))

// Mock store
vi.mock('@/modules/devops/stores/devops', () => ({
  useDevOpsStore: () => ({
    applications: [],
    projects: [],
    loading: { applications: false },
    loadApplications: vi.fn(),
    loadProjects: vi.fn(),
    createApplication: vi.fn(),
    updateApplication: vi.fn(),
    deleteApplication: vi.fn()
  })
}))

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/devops/applications/:id', component: { template: '<div>Application Detail</div>' } },
    { path: '/devops/projects/:id', component: { template: '<div>Project Detail</div>' } },
    { path: '/devops/components', component: { template: '<div>Components</div>' } }
  ]
})

describe('ApplicationList', () => {
  let wrapper: any
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    wrapper = mount(ApplicationList, {
      global: {
        plugins: [router, pinia]
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('[data-testid="breadcrumb"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="data-table"]').exists()).toBe(true)
  })

  it('displays page header with title and description', () => {
    const header = wrapper.find('.page-header')
    expect(header.exists()).toBe(true)
    expect(header.text()).toContain('应用管理')
    expect(header.text()).toContain('管理项目中的应用程序，配置应用环境和监控状态')
  })

  it('shows create application button', () => {
    const createButton = wrapper.find('.btn-primary')
    expect(createButton.exists()).toBe(true)
    expect(createButton.text()).toContain('创建应用')
  })

  it('displays filter section with project and status filters', () => {
    const filtersSection = wrapper.find('.filters-section')
    expect(filtersSection.exists()).toBe(true)
    
    const filterSelects = wrapper.findAll('.filter-select')
    expect(filterSelects).toHaveLength(2)
  })

  it('opens create modal when create button is clicked', async () => {
    const createButton = wrapper.find('.btn-primary')
    await createButton.trigger('click')
    
    expect(wrapper.vm.showCreateModal).toBe(true)
    expect(wrapper.find('.modal-overlay').exists()).toBe(true)
  })

  it('displays modal with correct title when creating', async () => {
    wrapper.vm.showCreateModal = true
    await wrapper.vm.$nextTick()
    
    const modalHeader = wrapper.find('.modal-header h3')
    expect(modalHeader.text()).toBe('创建应用')
  })

  it('displays modal with correct title when editing', async () => {
    wrapper.vm.showEditModal = true
    await wrapper.vm.$nextTick()
    
    const modalHeader = wrapper.find('.modal-header h3')
    expect(modalHeader.text()).toBe('编辑应用')
  })

  it('closes modal when close button is clicked', async () => {
    wrapper.vm.showCreateModal = true
    await wrapper.vm.$nextTick()
    
    const closeButton = wrapper.find('.modal-close')
    await closeButton.trigger('click')
    
    expect(wrapper.vm.showCreateModal).toBe(false)
  })

  it('closes modal when overlay is clicked', async () => {
    wrapper.vm.showCreateModal = true
    await wrapper.vm.$nextTick()
    
    const overlay = wrapper.find('.modal-overlay')
    await overlay.trigger('click')
    
    expect(wrapper.vm.showCreateModal).toBe(false)
  })

  it('validates form before submission', async () => {
    wrapper.vm.showCreateModal = true
    await wrapper.vm.$nextTick()
    
    // 尝试提交空表单
    const form = wrapper.find('.modal-form')
    await form.trigger('submit')
    
    expect(wrapper.vm.formErrors.name).toBeTruthy()
    expect(wrapper.vm.formErrors.projectId).toBeTruthy()
  })

  it('handles search functionality', async () => {
    const searchQuery = '用户服务'
    wrapper.vm.handleSearch(searchQuery)
    
    expect(wrapper.vm.searchQuery).toBe(searchQuery)
  })

  it('filters applications by project', async () => {
    // 设置测试数据
    wrapper.vm.applications = [
      { id: 1, name: '应用1', projectId: 1, status: 'ACTIVE' },
      { id: 2, name: '应用2', projectId: 2, status: 'ACTIVE' }
    ]
    
    wrapper.vm.selectedProjectId = 1
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredApplications
    expect(filtered).toHaveLength(1)
    expect(filtered[0].projectId).toBe(1)
  })

  it('filters applications by status', async () => {
    // 设置测试数据
    wrapper.vm.applications = [
      { id: 1, name: '应用1', projectId: 1, status: 'ACTIVE' },
      { id: 2, name: '应用2', projectId: 1, status: 'INACTIVE' }
    ]
    
    wrapper.vm.selectedStatus = 'ACTIVE'
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredApplications
    expect(filtered).toHaveLength(1)
    expect(filtered[0].status).toBe('ACTIVE')
  })

  it('formats date correctly', () => {
    const dateString = '2024-01-15T10:30:00Z'
    const formatted = wrapper.vm.formatDate(dateString)
    
    expect(formatted).toContain('2024')
    expect(typeof formatted).toBe('string')
  })

  it('gets project name correctly', () => {
    wrapper.vm.projects = [
      { id: 1, name: '测试项目', status: 'ACTIVE' }
    ]
    
    const projectName = wrapper.vm.getProjectName(1)
    expect(projectName).toBe('测试项目')
    
    const unknownProjectName = wrapper.vm.getProjectName(999)
    expect(unknownProjectName).toBe('未知项目')
  })

  it('resets form when closing modal', async () => {
    // 设置表单数据
    wrapper.vm.formData.name = '测试应用'
    wrapper.vm.formData.description = '测试描述'
    wrapper.vm.formErrors.name = '错误信息'
    
    wrapper.vm.closeModal()
    
    expect(wrapper.vm.formData.name).toBe('')
    expect(wrapper.vm.formData.description).toBe('')
    expect(wrapper.vm.formErrors.name).toBe('')
  })

  it('shows delete confirmation dialog', async () => {
    const testApplication = { id: 1, name: '测试应用', projectId: 1, status: 'ACTIVE' }
    wrapper.vm.deleteApplication(testApplication)
    
    expect(wrapper.vm.showDeleteDialog).toBe(true)
    expect(wrapper.vm.deleteTarget).toEqual(testApplication)
  })

  it('computes project options correctly', () => {
    wrapper.vm.projects = [
      { id: 1, name: '项目1', status: 'ACTIVE' },
      { id: 2, name: '项目2', status: 'ACTIVE' }
    ]
    
    const options = wrapper.vm.projectOptions
    expect(options).toHaveLength(2)
    expect(options[0]).toEqual({ label: '项目1', value: 1 })
    expect(options[1]).toEqual({ label: '项目2', value: 2 })
  })

  it('handles navigation to application detail', () => {
    const pushSpy = vi.spyOn(router, 'push')
    const testApplication = { id: 1, name: '测试应用', projectId: 1, status: 'ACTIVE' }
    
    wrapper.vm.viewApplication(testApplication)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/applications/1')
  })

  it('handles navigation to components', () => {
    const pushSpy = vi.spyOn(router, 'push')
    const testApplication = { id: 1, name: '测试应用', projectId: 1, status: 'ACTIVE' }
    
    wrapper.vm.viewComponents(testApplication)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/components?applicationId=1')
  })

  it('handles navigation to project detail', () => {
    const pushSpy = vi.spyOn(router, 'push')
    
    wrapper.vm.viewProject(1)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/projects/1')
  })
})
