/**
 * CICDManagement组件的单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import CICDManagement from '../CICDManagement.vue'

// Mock组件
vi.mock('@/components/DataTable.vue', () => ({
  default: {
    name: 'DataTable',
    template: '<div data-testid="data-table"><slot name="actions"></slot><slot name="cell-status"></slot><slot name="actions" :record="{}"></slot></div>',
    props: ['data', 'columns', 'loading', 'title', 'searchable', 'search-placeholder'],
    emits: ['search', 'row-click']
  }
}))

vi.mock('@/components/StatusTag.vue', () => ({
  default: {
    name: 'StatusTag',
    template: '<span data-testid="status-tag">{{ status }}</span>',
    props: ['status']
  }
}))

vi.mock('@/components/Breadcrumb.vue', () => ({
  default: {
    name: 'Breadcrumb',
    template: '<nav data-testid="breadcrumb"></nav>',
    props: ['items']
  }
}))

vi.mock('@/components/FormField.vue', () => ({
  default: {
    name: 'FormField',
    template: '<div data-testid="form-field"><input :value="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" /></div>',
    props: ['modelValue', 'label', 'type', 'placeholder', 'required', 'error-message', 'options', 'rows'],
    emits: ['update:modelValue']
  }
}))

vi.mock('@/components/ConfirmDialog.vue', () => ({
  default: {
    name: 'ConfirmDialog',
    template: '<div data-testid="confirm-dialog" v-if="visible"></div>',
    props: ['visible', 'title', 'message', 'type', 'confirm-text', 'loading'],
    emits: ['confirm', 'cancel']
  }
}))

// Mock store
vi.mock('@/modules/devops/stores/devops', () => ({
  useDevOpsStore: () => ({
    ciTasks: [],
    cdTasks: [],
    components: [],
    loading: { ciTasks: false, cdTasks: false },
    loadCiTasks: vi.fn(),
    loadCdTasks: vi.fn(),
    loadComponents: vi.fn(),
    createCiTask: vi.fn(),
    createCdTask: vi.fn(),
    updateCiTask: vi.fn(),
    updateCdTask: vi.fn(),
    deleteCiTask: vi.fn(),
    deleteCdTask: vi.fn()
  })
}))

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/devops/ci-cd/:id', component: { template: '<div>Task Detail</div>' } },
    { path: '/devops/components/:id', component: { template: '<div>Component Detail</div>' } }
  ]
})

describe('CICDManagement', () => {
  let wrapper: any
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    wrapper = mount(CICDManagement, {
      global: {
        plugins: [router, pinia]
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('[data-testid="breadcrumb"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="data-table"]').exists()).toBe(true)
  })

  it('displays page header with title and description', () => {
    const header = wrapper.find('.page-header')
    expect(header.exists()).toBe(true)
    expect(header.text()).toContain('CI/CD 管理')
    expect(header.text()).toContain('配置和管理持续集成和持续部署流水线')
  })

  it('shows create task button', () => {
    const createButton = wrapper.find('.btn-primary')
    expect(createButton.exists()).toBe(true)
    expect(createButton.text()).toContain('创建任务')
  })

  it('displays statistics cards', () => {
    const statsGrid = wrapper.find('.stats-grid')
    expect(statsGrid.exists()).toBe(true)
    
    const statCards = wrapper.findAll('.stat-card')
    expect(statCards).toHaveLength(4)
    
    // 检查统计卡片类型
    expect(wrapper.find('.ci-card').exists()).toBe(true)
    expect(wrapper.find('.cd-card').exists()).toBe(true)
    expect(wrapper.find('.pipeline-card').exists()).toBe(true)
    expect(wrapper.find('.success-rate-card').exists()).toBe(true)
  })

  it('displays task type tabs', () => {
    const taskTypeTabs = wrapper.find('.task-type-tabs')
    expect(taskTypeTabs.exists()).toBe(true)
    
    const tabButtons = wrapper.findAll('.tab-button')
    expect(tabButtons).toHaveLength(3)
    
    // 检查默认选中的标签
    const activeTab = wrapper.find('.tab-button.active')
    expect(activeTab.exists()).toBe(true)
  })

  it('switches between task type tabs', async () => {
    const ciTab = wrapper.findAll('.tab-button')[1] // CI 任务标签
    await ciTab.trigger('click')
    
    expect(wrapper.vm.activeTab).toBe('ci')
    expect(ciTab.classes()).toContain('active')
  })

  it('displays filter section', () => {
    const filtersSection = wrapper.find('.filters-section')
    expect(filtersSection.exists()).toBe(true)
    
    const filterSelects = wrapper.findAll('.filter-select')
    expect(filterSelects).toHaveLength(3)
  })

  it('opens create modal when create button is clicked', async () => {
    const createButton = wrapper.find('.btn-primary')
    await createButton.trigger('click')
    
    expect(wrapper.vm.showCreateModal).toBe(true)
    expect(wrapper.find('.modal-overlay').exists()).toBe(true)
  })

  it('displays modal with correct title when creating', async () => {
    wrapper.vm.showCreateModal = true
    await wrapper.vm.$nextTick()
    
    const modalHeader = wrapper.find('.modal-header h3')
    expect(modalHeader.text()).toBe('创建任务')
  })

  it('displays modal with correct title when editing', async () => {
    wrapper.vm.showEditModal = true
    await wrapper.vm.$nextTick()
    
    const modalHeader = wrapper.find('.modal-header h3')
    expect(modalHeader.text()).toBe('编辑任务')
  })

  it('validates form before submission', async () => {
    wrapper.vm.showCreateModal = true
    // 清空表单数据以测试验证
    wrapper.vm.formData.name = ''
    wrapper.vm.formData.type = ''
    await wrapper.vm.$nextTick()

    // 尝试提交空表单
    const form = wrapper.find('.modal-form')
    await form.trigger('submit')

    expect(wrapper.vm.formErrors.name).toBeTruthy()
    expect(wrapper.vm.formErrors.type).toBeTruthy()
  })

  it('filters tasks by active tab', async () => {
    // 设置测试数据
    wrapper.vm.tasks = [
      { id: 1, name: '任务1', type: 'CI', status: 'COMPLETED' },
      { id: 2, name: '任务2', type: 'CD', status: 'RUNNING' },
      { id: 3, name: '任务3', type: 'CI', status: 'FAILED' }
    ]
    
    // 切换到CI标签
    wrapper.vm.activeTab = 'ci'
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredTasks
    expect(filtered).toHaveLength(2)
    expect(filtered.every((task: any) => task.type === 'CI')).toBe(true)
  })

  it('filters tasks by component', async () => {
    // 设置测试数据
    wrapper.vm.tasks = [
      { id: 1, name: '任务1', type: 'CI', componentId: 1, status: 'COMPLETED' },
      { id: 2, name: '任务2', type: 'CI', componentId: 2, status: 'RUNNING' }
    ]
    
    wrapper.vm.selectedComponentId = 1
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredTasks
    expect(filtered).toHaveLength(1)
    expect(filtered[0].componentId).toBe(1)
  })

  it('filters tasks by status', async () => {
    // 设置测试数据
    wrapper.vm.tasks = [
      { id: 1, name: '任务1', type: 'CI', status: 'COMPLETED' },
      { id: 2, name: '任务2', type: 'CI', status: 'RUNNING' }
    ]
    
    wrapper.vm.selectedStatus = 'COMPLETED'
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredTasks
    expect(filtered).toHaveLength(1)
    expect(filtered[0].status).toBe('COMPLETED')
  })

  it('gets component name correctly', () => {
    wrapper.vm.components = [
      { id: 1, name: '测试组件', applicationId: 1, status: 'ACTIVE' }
    ]
    
    const componentName = wrapper.vm.getComponentName(1)
    expect(componentName).toBe('测试组件')
    
    const unknownComponentName = wrapper.vm.getComponentName(999)
    expect(unknownComponentName).toBe('未知组件')
  })

  it('gets task type label correctly', () => {
    expect(wrapper.vm.getTaskTypeLabel('CI')).toBe('CI')
    expect(wrapper.vm.getTaskTypeLabel('CD')).toBe('CD')
  })

  it('formats duration correctly', () => {
    const startTime = '2024-01-15T10:30:00Z'
    const endTime = '2024-01-15T10:35:00Z'
    
    const duration = wrapper.vm.formatDuration(startTime, endTime)
    expect(duration).toContain('分')
  })

  it('checks if task can be executed', () => {
    const pendingTask = { status: 'PENDING' }
    const runningTask = { status: 'RUNNING' }
    
    expect(wrapper.vm.canExecuteTask(pendingTask)).toBe(true)
    expect(wrapper.vm.canExecuteTask(runningTask)).toBe(false)
  })

  it('checks if task can be stopped', () => {
    const runningTask = { status: 'RUNNING' }
    const completedTask = { status: 'COMPLETED' }
    
    expect(wrapper.vm.canStopTask(runningTask)).toBe(true)
    expect(wrapper.vm.canStopTask(completedTask)).toBe(false)
  })

  it('checks if logs can be viewed', () => {
    const runningTask = { status: 'RUNNING' }
    const pendingTask = { status: 'PENDING' }
    
    expect(wrapper.vm.canViewLogs(runningTask)).toBe(true)
    expect(wrapper.vm.canViewLogs(pendingTask)).toBe(false)
  })

  it('opens logs modal', async () => {
    const testTask = { id: 1, name: '测试任务', status: 'COMPLETED' }
    
    await wrapper.vm.viewLogs(testTask)
    
    expect(wrapper.vm.showLogsModal).toBe(true)
    expect(wrapper.vm.currentTask).toEqual(testTask)
  })

  it('handles task type change in form', () => {
    wrapper.vm.formData.type = 'CI'
    wrapper.vm.handleTaskTypeChange()
    
    expect(wrapper.vm.formData.configuration.script).toContain('CI 构建脚本')
    
    wrapper.vm.formData.type = 'CD'
    wrapper.vm.handleTaskTypeChange()
    
    expect(wrapper.vm.formData.configuration.script).toContain('CD 部署脚本')
  })

  it('updates statistics correctly', () => {
    wrapper.vm.tasks = [
      { id: 1, type: 'CI', status: 'COMPLETED' },
      { id: 2, type: 'CI', status: 'RUNNING' },
      { id: 3, type: 'CD', status: 'COMPLETED' },
      { id: 4, type: 'CD', status: 'FAILED' }
    ]
    
    wrapper.vm.updateStats()
    
    expect(wrapper.vm.stats.ci.total).toBe(2)
    expect(wrapper.vm.stats.ci.completed).toBe(1)
    expect(wrapper.vm.stats.cd.total).toBe(2)
    expect(wrapper.vm.stats.cd.failed).toBe(1)
    expect(wrapper.vm.stats.successRate).toBe(50)
  })

  it('gets table title correctly', () => {
    wrapper.vm.activeTab = 'all'
    wrapper.vm.tasks = [{ id: 1 }, { id: 2 }]
    
    const title = wrapper.vm.getTableTitle()
    expect(title).toContain('全部任务')
    expect(title).toContain('(2)')
  })

  it('gets tab count correctly', () => {
    wrapper.vm.tasks = [
      { id: 1, type: 'CI' },
      { id: 2, type: 'CI' },
      { id: 3, type: 'CD' }
    ]
    
    expect(wrapper.vm.getTabCount('all')).toBe(3)
    expect(wrapper.vm.getTabCount('ci')).toBe(2)
    expect(wrapper.vm.getTabCount('cd')).toBe(1)
  })

  it('handles navigation to task detail', () => {
    const pushSpy = vi.spyOn(router, 'push')
    const testTask = { id: 1, name: '测试任务', status: 'COMPLETED' }
    
    wrapper.vm.viewTask(testTask)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/ci-cd/1')
  })

  it('handles navigation to component detail', () => {
    const pushSpy = vi.spyOn(router, 'push')
    
    wrapper.vm.viewComponent(1)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/components/1')
  })

  it('resets form when closing modal', () => {
    // 设置表单数据
    wrapper.vm.formData.name = '测试任务'
    wrapper.vm.formData.description = '测试描述'
    wrapper.vm.formErrors.name = '错误信息'
    
    wrapper.vm.closeModal()
    
    expect(wrapper.vm.formData.name).toBe('')
    expect(wrapper.vm.formData.description).toBe('')
    expect(wrapper.vm.formErrors.name).toBe('')
  })

  it('shows delete confirmation dialog', () => {
    const testTask = { id: 1, name: '测试任务', status: 'COMPLETED' }
    wrapper.vm.deleteTask(testTask)
    
    expect(wrapper.vm.showDeleteDialog).toBe(true)
    expect(wrapper.vm.deleteTarget).toEqual(testTask)
  })

  it('handles search functionality', () => {
    const searchQuery = '构建任务'
    wrapper.vm.handleSearch(searchQuery)
    
    expect(wrapper.vm.searchQuery).toBe(searchQuery)
  })

  it('formats date correctly', () => {
    const dateString = '2024-01-15T10:30:00Z'
    const formatted = wrapper.vm.formatDate(dateString)
    
    expect(formatted).toContain('2024')
    expect(typeof formatted).toBe('string')
  })

  it('formats log time correctly', () => {
    const timestamp = '2024-01-15T10:30:00Z'
    const formatted = wrapper.vm.formatLogTime(timestamp)
    
    expect(typeof formatted).toBe('string')
    expect(formatted).toMatch(/\d{1,2}:\d{2}:\d{2}/)
  })
})
