<template>
  <div class="ci-task-list">
    <DataTable
      :data="tasks"
      :columns="tableColumns"
      :loading="loading"
      title="CI 任务列表"
      searchable
      search-placeholder="搜索任务名称或描述..."
      @search="handleSearch"
      @row-click="handleRowClick"
    >
      <template #toolbar-actions>
        <button
          @click="$emit('refresh')"
          :disabled="loading"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
        <button
          @click="$emit('create')"
          class="btn btn-primary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          创建任务
        </button>
      </template>

      <template #cell-componentName="{ record }">
        <span class="component-link" @click="viewComponent(record.componentId)">
          {{ getComponentName(record.componentId) }}
        </span>
      </template>

      <template #cell-taskType="{ value }">
        <span class="task-type-badge" :class="`type-${value?.toLowerCase()}`">
          {{ getTaskTypeLabel(value) }}
        </span>
      </template>

      <template #cell-status="{ value }">
        <StatusTag :status="value" />
      </template>

      <template #cell-lastExecution="{ record }">
        <div class="execution-info">
          <div class="execution-time">
            {{ formatExecutionTime(record.lastExecutionTime) }}
          </div>
          <div class="execution-status" :class="`status-${record.lastExecutionStatus?.toLowerCase()}`">
            {{ getExecutionStatusLabel(record.lastExecutionStatus) }}
          </div>
        </div>
      </template>

      <template #cell-actions="{ record }">
        <div class="action-buttons">
          <button
            @click.stop="$emit('execute', record)"
            :disabled="record.status === 'RUNNING'"
            class="btn btn-sm btn-primary"
            title="执行任务"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click.stop="$emit('view-logs', record)"
            class="btn btn-sm btn-secondary"
            title="查看日志"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z" />
            </svg>
          </button>
          <button
            @click.stop="$emit('edit', record)"
            class="btn btn-sm btn-secondary"
            title="编辑任务"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            @click.stop="$emit('delete', record)"
            class="btn btn-sm btn-danger"
            title="删除任务"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import StatusTag from '@/components/StatusTag.vue'
import type { DevOpsCiTask, DevOpsComponent, TableColumn } from '@/modules/devops/types/devops'

// Props
interface Props {
  tasks: DevOpsCiTask[]
  loading: boolean
  components: DevOpsComponent[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  create: []
  edit: [task: DevOpsCiTask]
  delete: [task: DevOpsCiTask]
  execute: [task: DevOpsCiTask]
  'view-logs': [task: DevOpsCiTask]
  refresh: []
}>()

// 响应式数据
const searchQuery = ref('')

// 计算属性
const tableColumns = computed<TableColumn[]>(() => [
  {
    key: 'name',
    title: '任务名称',
    sortable: true,
    width: '200px'
  },
  {
    key: 'componentName',
    title: '关联组件',
    width: '150px'
  },
  {
    key: 'taskType',
    title: '任务类型',
    width: '120px'
  },
  {
    key: 'status',
    title: '状态',
    width: '100px'
  },
  {
    key: 'lastExecution',
    title: '最近执行',
    width: '180px'
  },
  {
    key: 'createdAt',
    title: '创建时间',
    sortable: true,
    width: '150px'
  },
  {
    key: 'actions',
    title: '操作',
    width: '200px',
    fixed: 'right'
  }
])

const filteredTasks = computed(() => {
  if (!searchQuery.value) return props.tasks
  
  const query = searchQuery.value.toLowerCase()
  return props.tasks.filter(task =>
    task.name.toLowerCase().includes(query) ||
    (task.description && task.description.toLowerCase().includes(query))
  )
})

// 方法
const handleSearch = (query: string) => {
  searchQuery.value = query
}

const handleRowClick = (task: DevOpsCiTask) => {
  emit('edit', task)
}

const getComponentName = (componentId: number) => {
  const component = props.components.find(c => c.id === componentId)
  return component?.name || '未知组件'
}

const getTaskTypeLabel = (taskType: string) => {
  const labels = {
    build: '构建',
    test: '测试',
    lint: '代码检查',
    security_scan: '安全扫描',
    custom: '自定义'
  }
  return labels[taskType] || taskType
}

const getExecutionStatusLabel = (status: string) => {
  const labels = {
    COMPLETED: '成功',
    FAILED: '失败',
    RUNNING: '运行中',
    PENDING: '等待中',
    CANCELLED: '已取消'
  }
  return labels[status] || status
}

const formatExecutionTime = (time: string) => {
  if (!time) return '从未执行'
  
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes} 分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)} 小时前`
  return `${Math.floor(minutes / 1440)} 天前`
}

const viewComponent = (componentId: number) => {
  // 跳转到组件详情页面
  console.log('查看组件:', componentId)
}
</script>

<style scoped>
.ci-task-list {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

/* 组件链接 */
.component-link {
  @apply text-blue-600 hover:text-blue-800 cursor-pointer font-medium;
}

/* 任务类型标识 */
.task-type-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.type-build {
  @apply bg-blue-100 text-blue-800;
}

.type-test {
  @apply bg-green-100 text-green-800;
}

.type-lint {
  @apply bg-yellow-100 text-yellow-800;
}

.type-security_scan {
  @apply bg-red-100 text-red-800;
}

.type-custom {
  @apply bg-purple-100 text-purple-800;
}

/* 执行信息 */
.execution-info {
  @apply text-sm;
}

.execution-time {
  @apply text-gray-600 mb-1;
}

.execution-status {
  @apply text-xs font-medium;
}

.status-completed {
  @apply text-green-600;
}

.status-failed {
  @apply text-red-600;
}

.status-running {
  @apply text-blue-600;
}

.status-pending {
  @apply text-yellow-600;
}

.status-cancelled {
  @apply text-gray-600;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-2;
}

.btn {
  @apply inline-flex items-center gap-1 px-3 py-1.5 rounded-md font-medium transition-colors;
}

.btn-sm {
  @apply px-2 py-1 text-sm;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn-danger {
  @apply bg-red-100 text-red-700 hover:bg-red-200;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-icon {
  @apply w-4 h-4;
}
</style>
