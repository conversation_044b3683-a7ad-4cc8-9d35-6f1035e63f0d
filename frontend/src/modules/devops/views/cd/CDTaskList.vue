<template>
  <div class="cd-task-list">
    <DataTable
      :data="tasks"
      :columns="tableColumns"
      :loading="loading"
      title="CD 任务列表"
      searchable
      search-placeholder="搜索任务名称或描述..."
      @search="handleSearch"
      @row-click="handleRowClick"
    >
      <template #toolbar-actions>
        <button
          @click="$emit('refresh')"
          :disabled="loading"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
        <button
          @click="$emit('create')"
          class="btn btn-primary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          创建任务
        </button>
      </template>

      <template #cell-applicationName="{ record }">
        <span class="application-link" @click="viewApplication(record.applicationId)">
          {{ getApplicationName(record.applicationId) }}
        </span>
      </template>

      <template #cell-deploymentStrategy="{ value }">
        <span class="strategy-badge" :class="`strategy-${value?.toLowerCase()}`">
          {{ getStrategyLabel(value) }}
        </span>
      </template>

      <template #cell-targetEnvironment="{ record }">
        <span class="environment-badge" :class="`env-${record.configuration?.targetEnvironment?.toLowerCase()}`">
          {{ getEnvironmentLabel(record.configuration?.targetEnvironment) }}
        </span>
      </template>

      <template #cell-status="{ value }">
        <StatusTag :status="value" />
      </template>

      <template #cell-lastDeployment="{ record }">
        <div class="deployment-info">
          <div class="deployment-time">
            {{ formatDeploymentTime(record.lastDeploymentTime) }}
          </div>
          <div class="deployment-version" v-if="record.currentVersion">
            版本: {{ record.currentVersion }}
          </div>
        </div>
      </template>

      <template #cell-actions="{ record }">
        <div class="action-buttons">
          <button
            @click.stop="$emit('deploy', record)"
            :disabled="record.status === 'DEPLOYING'"
            class="btn btn-sm btn-success"
            title="部署"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click.stop="$emit('rollback', record)"
            :disabled="!record.currentVersion || record.status === 'DEPLOYING'"
            class="btn btn-sm btn-warning"
            title="回滚"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click.stop="$emit('view-logs', record)"
            class="btn btn-sm btn-secondary"
            title="查看日志"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z" />
            </svg>
          </button>
          <button
            @click.stop="$emit('edit', record)"
            class="btn btn-sm btn-secondary"
            title="编辑任务"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            @click.stop="$emit('delete', record)"
            class="btn btn-sm btn-danger"
            title="删除任务"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import StatusTag from '@/components/StatusTag.vue'
import type { DevOpsCdTask, DevOpsApplication, TableColumn } from '@/modules/devops/types/devops'

// Props
interface Props {
  tasks: DevOpsCdTask[]
  loading: boolean
  applications: DevOpsApplication[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  create: []
  edit: [task: DevOpsCdTask]
  delete: [task: DevOpsCdTask]
  deploy: [task: DevOpsCdTask]
  rollback: [task: DevOpsCdTask]
  'view-logs': [task: DevOpsCdTask]
  refresh: []
}>()

// 响应式数据
const searchQuery = ref('')

// 计算属性
const tableColumns = computed<TableColumn[]>(() => [
  {
    key: 'name',
    title: '任务名称',
    sortable: true,
    width: '200px'
  },
  {
    key: 'applicationName',
    title: '关联应用',
    width: '150px'
  },
  {
    key: 'deploymentStrategy',
    title: '部署策略',
    width: '120px'
  },
  {
    key: 'targetEnvironment',
    title: '目标环境',
    width: '120px'
  },
  {
    key: 'status',
    title: '状态',
    width: '100px'
  },
  {
    key: 'lastDeployment',
    title: '最近部署',
    width: '180px'
  },
  {
    key: 'createdAt',
    title: '创建时间',
    sortable: true,
    width: '150px'
  },
  {
    key: 'actions',
    title: '操作',
    width: '250px',
    fixed: 'right'
  }
])

const filteredTasks = computed(() => {
  if (!searchQuery.value) return props.tasks
  
  const query = searchQuery.value.toLowerCase()
  return props.tasks.filter(task =>
    task.name.toLowerCase().includes(query) ||
    (task.description && task.description.toLowerCase().includes(query))
  )
})

// 方法
const handleSearch = (query: string) => {
  searchQuery.value = query
}

const handleRowClick = (task: DevOpsCdTask) => {
  emit('edit', task)
}

const getApplicationName = (applicationId: number) => {
  const application = props.applications.find(a => a.id === applicationId)
  return application?.name || '未知应用'
}

const getStrategyLabel = (strategy: string) => {
  const labels = {
    rolling_update: '滚动更新',
    blue_green: '蓝绿部署',
    canary: '金丝雀部署',
    a_b_testing: 'A/B 测试'
  }
  return labels[strategy] || strategy
}

const getEnvironmentLabel = (environment: string) => {
  const labels = {
    development: '开发环境',
    testing: '测试环境',
    staging: '预发布环境',
    production: '生产环境'
  }
  return labels[environment] || environment
}

const formatDeploymentTime = (time: string) => {
  if (!time) return '从未部署'
  
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes} 分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)} 小时前`
  return `${Math.floor(minutes / 1440)} 天前`
}

const viewApplication = (applicationId: number) => {
  // 跳转到应用详情页面
  console.log('查看应用:', applicationId)
}
</script>

<style scoped>
.cd-task-list {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

/* 应用链接 */
.application-link {
  @apply text-blue-600 hover:text-blue-800 cursor-pointer font-medium;
}

/* 部署策略标识 */
.strategy-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.strategy-rolling_update {
  @apply bg-blue-100 text-blue-800;
}

.strategy-blue_green {
  @apply bg-green-100 text-green-800;
}

.strategy-canary {
  @apply bg-yellow-100 text-yellow-800;
}

.strategy-a_b_testing {
  @apply bg-purple-100 text-purple-800;
}

/* 环境标识 */
.environment-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.env-development {
  @apply bg-gray-100 text-gray-800;
}

.env-testing {
  @apply bg-blue-100 text-blue-800;
}

.env-staging {
  @apply bg-yellow-100 text-yellow-800;
}

.env-production {
  @apply bg-red-100 text-red-800;
}

/* 部署信息 */
.deployment-info {
  @apply text-sm;
}

.deployment-time {
  @apply text-gray-600 mb-1;
}

.deployment-version {
  @apply text-xs text-gray-500 font-mono;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-2;
}

.btn {
  @apply inline-flex items-center gap-1 px-3 py-1.5 rounded-md font-medium transition-colors;
}

.btn-sm {
  @apply px-2 py-1 text-sm;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700;
}

.btn-warning {
  @apply bg-yellow-500 text-white hover:bg-yellow-600;
}

.btn-danger {
  @apply bg-red-100 text-red-700 hover:bg-red-200;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-icon {
  @apply w-4 h-4;
}
</style>
