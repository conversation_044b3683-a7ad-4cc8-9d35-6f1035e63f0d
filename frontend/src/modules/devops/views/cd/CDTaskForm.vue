<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2>{{ isEditing ? '编辑 CD 任务' : '创建 CD 任务' }}</h2>
        <button @click="$emit('cancel')" class="close-button">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit" class="task-form">
          <!-- 基础信息 -->
          <div class="form-section">
            <h3>基础信息</h3>
            <div class="form-grid">
              <FormField
                v-model="formData.name"
                label="任务名称"
                type="text"
                placeholder="请输入任务名称"
                required
                :error-message="formErrors.name"
              />

              <FormField
                v-model="formData.description"
                label="任务描述"
                type="textarea"
                placeholder="请输入任务描述"
                :rows="3"
                :error-message="formErrors.description"
              />

              <FormField
                v-model="formData.applicationId"
                label="关联应用"
                type="select"
                :options="applicationOptions"
                required
                :error-message="formErrors.applicationId"
                @change="handleApplicationChange"
              />

              <FormField
                v-model="formData.deploymentStrategy"
                label="部署策略"
                type="select"
                :options="deploymentStrategyOptions"
                required
                :error-message="formErrors.deploymentStrategy"
              />
            </div>
          </div>

          <!-- 组件版本配置 -->
          <div class="form-section">
            <h3>组件版本配置</h3>
            <div class="version-config">
              <div class="version-header">
                <span>组件</span>
                <span>版本</span>
                <span>操作</span>
              </div>
              <div
                v-for="(version, componentId) in formData.componentVersions"
                :key="componentId"
                class="version-item"
              >
                <span class="component-name">{{ getComponentName(Number(componentId)) }}</span>
                <input
                  v-model="formData.componentVersions[componentId]"
                  type="text"
                  placeholder="版本号"
                  class="version-input"
                />
                <button
                  type="button"
                  @click="removeComponentVersion(componentId)"
                  class="btn btn-sm btn-danger"
                >
                  删除
                </button>
              </div>
              <button
                type="button"
                @click="showAddComponentModal = true"
                class="btn btn-secondary"
              >
                <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                添加组件版本
              </button>
            </div>
          </div>

          <!-- 部署配置 -->
          <div class="form-section">
            <h3>部署配置</h3>
            <div class="config-tabs">
              <button
                v-for="tab in configTabs"
                :key="tab.key"
                type="button"
                :class="['tab-button', { active: activeConfigTab === tab.key }]"
                @click="activeConfigTab = tab.key"
              >
                {{ tab.label }}
              </button>
            </div>

            <div class="config-content">
              <!-- 基础配置 -->
              <div v-if="activeConfigTab === 'basic'" class="config-panel">
                <div class="form-grid">
                  <FormField
                    v-model="formData.configuration.targetEnvironment"
                    label="目标环境"
                    type="select"
                    :options="environmentOptions"
                    required
                    :error-message="formErrors.targetEnvironment"
                  />

                  <FormField
                    v-model="formData.configuration.namespace"
                    label="命名空间"
                    type="text"
                    placeholder="default"
                  />

                  <FormField
                    v-model="formData.configuration.replicas"
                    label="副本数量"
                    type="number"
                    :min="1"
                    :max="100"
                  />

                  <FormField
                    v-model="formData.configuration.timeout"
                    label="部署超时（分钟）"
                    type="number"
                    :min="1"
                    :max="120"
                  />
                </div>
              </div>

              <!-- 回滚配置 -->
              <div v-if="activeConfigTab === 'rollback'" class="config-panel">
                <div class="form-grid">
                  <div class="checkbox-group">
                    <label class="checkbox-item">
                      <input
                        v-model="formData.configuration.autoRollback"
                        type="checkbox"
                      />
                      <span>启用自动回滚</span>
                    </label>
                  </div>

                  <FormField
                    v-if="formData.configuration.autoRollback"
                    v-model="formData.configuration.rollbackTimeout"
                    label="回滚超时（分钟）"
                    type="number"
                    :min="1"
                    :max="60"
                  />

                  <FormField
                    v-if="formData.configuration.autoRollback"
                    v-model="formData.configuration.maxRollbackAttempts"
                    label="最大回滚尝试次数"
                    type="number"
                    :min="1"
                    :max="10"
                  />
                </div>
              </div>

              <!-- 健康检查配置 -->
              <div v-if="activeConfigTab === 'health'" class="config-panel">
                <div class="form-grid">
                  <div class="checkbox-group">
                    <label class="checkbox-item">
                      <input
                        v-model="formData.configuration.healthCheckEnabled"
                        type="checkbox"
                      />
                      <span>启用健康检查</span>
                    </label>
                  </div>

                  <FormField
                    v-if="formData.configuration.healthCheckEnabled"
                    v-model="formData.configuration.healthCheckEndpoint"
                    label="健康检查端点"
                    type="text"
                    placeholder="/health"
                  />

                  <FormField
                    v-if="formData.configuration.healthCheckEnabled"
                    v-model="formData.configuration.healthCheckTimeout"
                    label="检查超时（秒）"
                    type="number"
                    :min="1"
                    :max="300"
                  />

                  <FormField
                    v-if="formData.configuration.healthCheckEnabled"
                    v-model="formData.configuration.healthCheckRetries"
                    label="重试次数"
                    type="number"
                    :min="1"
                    :max="10"
                  />
                </div>
              </div>

              <!-- 通知配置 -->
              <div v-if="activeConfigTab === 'notification'" class="config-panel">
                <div class="form-grid">
                  <div class="checkbox-group">
                    <label class="checkbox-item">
                      <input
                        v-model="formData.configuration.notifyOnSuccess"
                        type="checkbox"
                      />
                      <span>部署成功时通知</span>
                    </label>
                    <label class="checkbox-item">
                      <input
                        v-model="formData.configuration.notifyOnFailure"
                        type="checkbox"
                      />
                      <span>部署失败时通知</span>
                    </label>
                    <label class="checkbox-item">
                      <input
                        v-model="formData.configuration.notifyOnRollback"
                        type="checkbox"
                      />
                      <span>回滚时通知</span>
                    </label>
                  </div>

                  <FormField
                    v-model="formData.configuration.notificationChannels"
                    label="通知渠道"
                    type="select"
                    multiple
                    :options="notificationChannelOptions"
                    help-text="可选择多个通知渠道"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 表单操作 -->
          <div class="form-actions">
            <button
              type="button"
              @click="validateConfiguration"
              :disabled="validatingConfig"
              class="btn btn-secondary"
            >
              <svg v-if="validatingConfig" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
              </svg>
              <svg v-else class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              {{ validatingConfig ? '验证中...' : '验证配置' }}
            </button>

            <div class="action-buttons">
              <button
                type="button"
                @click="$emit('cancel')"
                class="btn btn-secondary"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="submitting"
                class="btn btn-primary"
              >
                <svg v-if="submitting" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                {{ submitting ? '保存中...' : (isEditing ? '更新任务' : '创建任务') }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- 添加组件版本模态框 -->
    <div v-if="showAddComponentModal" class="modal-overlay" @click="showAddComponentModal = false">
      <div class="modal-container small" @click.stop>
        <div class="modal-header">
          <h3>添加组件版本</h3>
          <button @click="showAddComponentModal = false" class="close-button">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-grid">
            <FormField
              v-model="newComponentVersion.componentId"
              label="选择组件"
              type="select"
              :options="availableComponentOptions"
              required
            />
            <FormField
              v-model="newComponentVersion.version"
              label="版本号"
              type="text"
              placeholder="1.0.0"
              required
            />
          </div>
          <div class="form-actions">
            <button
              type="button"
              @click="showAddComponentModal = false"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="button"
              @click="addComponentVersion"
              class="btn btn-primary"
            >
              添加
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import FormField from '@/components/FormField.vue'
import type { DevOpsCdTask, DevOpsApplication, DevOpsComponent } from '@/modules/devops/types/devops'

// Props
interface Props {
  visible: boolean
  task?: DevOpsCdTask | null
  applications: DevOpsApplication[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  submit: [data: any]
  cancel: []
}>()

// 响应式数据
const submitting = ref(false)
const validatingConfig = ref(false)
const activeConfigTab = ref('basic')
const showAddComponentModal = ref(false)

const formData = ref({
  name: '',
  description: '',
  applicationId: null as number | null,
  deploymentStrategy: 'rolling_update',
  componentVersions: {} as Record<string, string>,
  configuration: {
    targetEnvironment: 'development',
    namespace: 'default',
    replicas: 1,
    timeout: 30,
    autoRollback: false,
    rollbackTimeout: 10,
    maxRollbackAttempts: 3,
    healthCheckEnabled: true,
    healthCheckEndpoint: '/health',
    healthCheckTimeout: 30,
    healthCheckRetries: 3,
    notifyOnSuccess: false,
    notifyOnFailure: true,
    notifyOnRollback: true,
    notificationChannels: []
  }
})

const formErrors = ref({
  name: '',
  description: '',
  applicationId: '',
  deploymentStrategy: '',
  targetEnvironment: ''
})

const newComponentVersion = ref({
  componentId: null as number | null,
  version: ''
})

// 计算属性
const isEditing = computed(() => !!props.task)

const applicationOptions = computed(() =>
  props.applications.map(app => ({
    value: app.id,
    label: app.name
  }))
)

const deploymentStrategyOptions = [
  { value: 'rolling_update', label: '滚动更新' },
  { value: 'blue_green', label: '蓝绿部署' },
  { value: 'canary', label: '金丝雀部署' },
  { value: 'a_b_testing', label: 'A/B 测试' }
]

const environmentOptions = [
  { value: 'development', label: '开发环境' },
  { value: 'testing', label: '测试环境' },
  { value: 'staging', label: '预发布环境' },
  { value: 'production', label: '生产环境' }
]

const notificationChannelOptions = [
  { value: 'email', label: '邮件' },
  { value: 'slack', label: 'Slack' },
  { value: 'webhook', label: 'Webhook' },
  { value: 'dingtalk', label: '钉钉' }
]

const configTabs = [
  { key: 'basic', label: '基础配置' },
  { key: 'rollback', label: '回滚配置' },
  { key: 'health', label: '健康检查' },
  { key: 'notification', label: '通知配置' }
]

const availableComponentOptions = computed(() => {
  // 模拟可用组件选项
  const allComponents = [
    { id: 1, name: '用户认证服务' },
    { id: 2, name: '订单处理引擎' },
    { id: 3, name: '支付网关' }
  ]
  
  return allComponents
    .filter(comp => !formData.value.componentVersions[comp.id.toString()])
    .map(comp => ({
      value: comp.id,
      label: comp.name
    }))
})

// 方法
const handleOverlayClick = () => {
  emit('cancel')
}

const handleApplicationChange = () => {
  // 应用变更时重置组件版本配置
  formData.value.componentVersions = {}
}

const getComponentName = (componentId: number) => {
  const components = [
    { id: 1, name: '用户认证服务' },
    { id: 2, name: '订单处理引擎' },
    { id: 3, name: '支付网关' }
  ]
  return components.find(c => c.id === componentId)?.name || '未知组件'
}

const addComponentVersion = () => {
  if (newComponentVersion.value.componentId && newComponentVersion.value.version) {
    formData.value.componentVersions[newComponentVersion.value.componentId.toString()] = newComponentVersion.value.version
    newComponentVersion.value = { componentId: null, version: '' }
    showAddComponentModal.value = false
  }
}

const removeComponentVersion = (componentId: string) => {
  delete formData.value.componentVersions[componentId]
}

const validateForm = () => {
  const errors = {
    name: '',
    description: '',
    applicationId: '',
    deploymentStrategy: '',
    targetEnvironment: ''
  }
  let isValid = true

  if (!formData.value.name.trim()) {
    errors.name = '任务名称不能为空'
    isValid = false
  }

  if (!formData.value.applicationId) {
    errors.applicationId = '请选择关联应用'
    isValid = false
  }

  if (!formData.value.deploymentStrategy) {
    errors.deploymentStrategy = '请选择部署策略'
    isValid = false
  }

  if (!formData.value.configuration.targetEnvironment) {
    errors.targetEnvironment = '请选择目标环境'
    isValid = false
  }

  formErrors.value = errors
  return isValid
}

const validateConfiguration = async () => {
  validatingConfig.value = true
  try {
    // 模拟配置验证
    await new Promise(resolve => setTimeout(resolve, 2000))

    if (Object.keys(formData.value.componentVersions).length === 0) {
      alert('请至少添加一个组件版本')
      return
    }

    alert('配置验证通过！')
  } catch (error) {
    alert(`配置验证失败: ${error}`)
  } finally {
    validatingConfig.value = false
  }
}

const handleSubmit = async () => {
  if (!validateForm()) return

  submitting.value = true
  try {
    emit('submit', { ...formData.value })
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    applicationId: null,
    deploymentStrategy: 'rolling_update',
    componentVersions: {},
    configuration: {
      targetEnvironment: 'development',
      namespace: 'default',
      replicas: 1,
      timeout: 30,
      autoRollback: false,
      rollbackTimeout: 10,
      maxRollbackAttempts: 3,
      healthCheckEnabled: true,
      healthCheckEndpoint: '/health',
      healthCheckTimeout: 30,
      healthCheckRetries: 3,
      notifyOnSuccess: false,
      notifyOnFailure: true,
      notifyOnRollback: true,
      notificationChannels: []
    }
  }
  formErrors.value = {
    name: '',
    description: '',
    applicationId: '',
    deploymentStrategy: '',
    targetEnvironment: ''
  }
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    if (props.task) {
      // 编辑模式，填充表单数据
      formData.value = {
        name: props.task.name,
        description: props.task.description || '',
        applicationId: props.task.applicationId,
        deploymentStrategy: props.task.deploymentStrategy || 'rolling_update',
        componentVersions: props.task.componentVersions || {},
        configuration: {
          targetEnvironment: props.task.configuration?.targetEnvironment || 'development',
          namespace: props.task.configuration?.namespace || 'default',
          replicas: props.task.configuration?.replicas || 1,
          timeout: props.task.configuration?.timeout || 30,
          autoRollback: props.task.configuration?.autoRollback || false,
          rollbackTimeout: props.task.configuration?.rollbackTimeout || 10,
          maxRollbackAttempts: props.task.configuration?.maxRollbackAttempts || 3,
          healthCheckEnabled: props.task.configuration?.healthCheckEnabled || true,
          healthCheckEndpoint: props.task.configuration?.healthCheckEndpoint || '/health',
          healthCheckTimeout: props.task.configuration?.healthCheckTimeout || 30,
          healthCheckRetries: props.task.configuration?.healthCheckRetries || 3,
          notifyOnSuccess: props.task.configuration?.notifyOnSuccess || false,
          notifyOnFailure: props.task.configuration?.notifyOnFailure || true,
          notifyOnRollback: props.task.configuration?.notifyOnRollback || true,
          notificationChannels: props.task.configuration?.notificationChannels || []
        }
      }
    } else {
      // 创建模式，重置表单
      resetForm()
    }
  }
})
</script>

<style scoped>
/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden;
}

.modal-container.small {
  @apply max-w-md;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-header h2,
.modal-header h3 {
  @apply text-xl font-semibold text-gray-900 m-0;
}

.close-button {
  @apply p-2 text-gray-400 hover:text-gray-600 transition-colors;
}

.close-button svg {
  @apply w-5 h-5;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-120px)];
}

/* 表单样式 */
.task-form {
  @apply space-y-8;
}

.form-section {
  @apply space-y-4;
}

.form-section h3 {
  @apply text-lg font-medium text-gray-900 m-0 pb-2 border-b border-gray-200;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

/* 版本配置 */
.version-config {
  @apply space-y-4;
}

.version-header {
  @apply grid grid-cols-3 gap-4 p-3 bg-gray-50 rounded-lg font-medium text-gray-700;
}

.version-item {
  @apply grid grid-cols-3 gap-4 p-3 border border-gray-200 rounded-lg items-center;
}

.component-name {
  @apply font-medium text-gray-900;
}

.version-input {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

/* 配置标签页 */
.config-tabs {
  @apply flex border-b border-gray-200 mb-6;
}

.tab-button {
  @apply px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:text-gray-900 hover:border-gray-300 transition-colors;
}

.tab-button.active {
  @apply text-blue-600 border-blue-600;
}

.config-content {
  @apply min-h-[300px];
}

.config-panel {
  @apply space-y-6;
}

/* 复选框组 */
.checkbox-group {
  @apply space-y-3;
}

.checkbox-item {
  @apply flex items-center gap-3 cursor-pointer;
}

.checkbox-item input[type="checkbox"] {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
}

.checkbox-item span {
  @apply text-sm text-gray-700;
}

/* 表单操作 */
.form-actions {
  @apply flex items-center justify-between pt-6 border-t border-gray-200;
}

.action-buttons {
  @apply flex items-center gap-3;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-sm {
  @apply px-3 py-1.5 text-sm;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn-danger {
  @apply bg-red-100 text-red-700 hover:bg-red-200;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-icon {
  @apply w-4 h-4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
