<template>
  <div class="monitoring-center">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>监控中心</h1>
        <p>实时监控系统状态，查看任务执行历史，分析性能指标和日志</p>
      </div>
      <div class="header-actions">
        <button @click="refreshData" :disabled="loading" class="btn btn-primary">
          <svg v-if="loading" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
            <path d="M10 2a8 8 0 018 8" fill="currentColor">
              <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
            </path>
          </svg>
          <svg v-else class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          {{ loading ? '刷新中...' : '刷新数据' }}
        </button>
      </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon system-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3>系统状态</h3>
        </div>
        <div class="stat-value">
          <span class="value">{{ systemStats.status }}</span>
          <span class="label">运行正常</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon cpu-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M13 7H7v6h6V7z" />
              <path fill-rule="evenodd" d="M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2h1a1 1 0 110 2h-1v2h1a1 1 0 110 2h-1v2a2 2 0 01-2 2h-2v1a1 1 0 11-2 0v-1H9v1a1 1 0 11-2 0v-1H5a2 2 0 01-2-2v-2H2a1 1 0 110-2h1V9H2a1 1 0 010-2h1V5a2 2 0 012-2h2V2zM5 5v10h10V5H5z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3>CPU 使用率</h3>
        </div>
        <div class="stat-value">
          <span class="value">{{ systemStats.cpuUsage }}%</span>
          <span class="label">当前使用</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon memory-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
            </svg>
          </div>
          <h3>内存使用</h3>
        </div>
        <div class="stat-value">
          <span class="value">{{ systemStats.memoryUsage }}%</span>
          <span class="label">{{ systemStats.memoryUsed }}/{{ systemStats.memoryTotal }}GB</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon task-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3>活跃任务</h3>
        </div>
        <div class="stat-value">
          <span class="value">{{ systemStats.activeTasks }}</span>
          <span class="label">正在运行</span>
        </div>
      </div>
    </div>

    <!-- 监控图表区域 -->
    <div class="monitoring-content">
      <div class="chart-section">
        <div class="section-header">
          <h2>性能监控</h2>
          <div class="time-range-selector">
            <button 
              v-for="range in timeRanges" 
              :key="range.value"
              @click="selectedTimeRange = range.value"
              :class="['time-btn', { active: selectedTimeRange === range.value }]"
            >
              {{ range.label }}
            </button>
          </div>
        </div>
        
        <div class="charts-grid">
          <div class="chart-card">
            <h3>CPU 使用趋势</h3>
            <div class="chart-placeholder">
              <p>CPU 使用率图表</p>
              <small>图表功能开发中...</small>
            </div>
          </div>
          
          <div class="chart-card">
            <h3>内存使用趋势</h3>
            <div class="chart-placeholder">
              <p>内存使用率图表</p>
              <small>图表功能开发中...</small>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务监控 -->
      <div class="task-monitoring">
        <div class="section-header">
          <h2>任务监控</h2>
          <div class="task-filters">
            <select v-model="selectedTaskType" class="filter-select">
              <option value="">所有类型</option>
              <option value="CI">CI 任务</option>
              <option value="CD">CD 任务</option>
            </select>
            <select v-model="selectedTaskStatus" class="filter-select">
              <option value="">所有状态</option>
              <option value="RUNNING">运行中</option>
              <option value="COMPLETED">已完成</option>
              <option value="FAILED">失败</option>
            </select>
          </div>
        </div>

        <div class="task-list">
          <div v-if="filteredTasks.length === 0" class="empty-state">
            <p>暂无任务数据</p>
          </div>
          <div v-else class="task-items">
            <div v-for="task in filteredTasks" :key="task.id" class="task-item">
              <div class="task-info">
                <div class="task-name">{{ task.name }}</div>
                <div class="task-meta">
                  <span class="task-type">{{ task.type }}</span>
                  <span class="task-time">{{ formatTime(task.startTime) }}</span>
                </div>
              </div>
              <div class="task-status">
                <StatusTag :status="task.status" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Breadcrumb from '@/components/Breadcrumb.vue'
import StatusTag from '@/components/StatusTag.vue'
import type { BreadcrumbItem } from '@/modules/devops/types/devops'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const selectedTimeRange = ref('1h')
const selectedTaskType = ref('')
const selectedTaskStatus = ref('')

// 系统统计数据
const systemStats = ref({
  status: '正常',
  cpuUsage: 45,
  memoryUsage: 68,
  memoryUsed: 6.8,
  memoryTotal: 10,
  activeTasks: 12
})

// 时间范围选项
const timeRanges = [
  { label: '1小时', value: '1h' },
  { label: '6小时', value: '6h' },
  { label: '24小时', value: '24h' },
  { label: '7天', value: '7d' }
]

// 模拟任务数据
const tasks = ref([
  {
    id: 1,
    name: '用户服务构建',
    type: 'CI',
    status: 'RUNNING',
    startTime: new Date(Date.now() - 1000 * 60 * 30)
  },
  {
    id: 2,
    name: '订单服务部署',
    type: 'CD',
    status: 'COMPLETED',
    startTime: new Date(Date.now() - 1000 * 60 * 60 * 2)
  },
  {
    id: 3,
    name: '支付服务测试',
    type: 'CI',
    status: 'FAILED',
    startTime: new Date(Date.now() - 1000 * 60 * 60 * 4)
  }
])

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: '监控中心', active: true, icon: 'monitoring' }
])

const filteredTasks = computed(() => {
  return tasks.value.filter(task => {
    if (selectedTaskType.value && task.type !== selectedTaskType.value) {
      return false
    }
    if (selectedTaskStatus.value && task.status !== selectedTaskStatus.value) {
      return false
    }
    return true
  })
})

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新系统统计数据
    systemStats.value = {
      status: '正常',
      cpuUsage: Math.floor(Math.random() * 100),
      memoryUsage: Math.floor(Math.random() * 100),
      memoryUsed: Math.round(Math.random() * 10 * 100) / 100,
      memoryTotal: 10,
      activeTasks: Math.floor(Math.random() * 20)
    }
  } finally {
    loading.value = false
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.monitoring-center {
  @apply p-6 space-y-6;
}

.page-header {
  @apply flex justify-between items-start;
}

.header-content h1 {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.header-content p {
  @apply text-gray-600;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50;
}

.btn-icon {
  @apply w-4 h-4;
}

.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.stat-header {
  @apply flex items-center gap-3 mb-4;
}

.stat-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center;
}

.stat-icon svg {
  @apply w-5 h-5;
}

.system-icon {
  @apply bg-green-100 text-green-600;
}

.cpu-icon {
  @apply bg-blue-100 text-blue-600;
}

.memory-icon {
  @apply bg-purple-100 text-purple-600;
}

.task-icon {
  @apply bg-orange-100 text-orange-600;
}

.stat-header h3 {
  @apply text-sm font-medium text-gray-700;
}

.stat-value .value {
  @apply text-2xl font-bold text-gray-900 block;
}

.stat-value .label {
  @apply text-sm text-gray-500;
}

.monitoring-content {
  @apply space-y-8;
}

.section-header {
  @apply flex justify-between items-center mb-6;
}

.section-header h2 {
  @apply text-lg font-semibold text-gray-900;
}

.time-range-selector {
  @apply flex gap-2;
}

.time-btn {
  @apply px-3 py-1 text-sm rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50;
}

.time-btn.active {
  @apply bg-blue-600 text-white border-blue-600;
}

.charts-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.chart-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.chart-card h3 {
  @apply text-base font-medium text-gray-900 mb-4;
}

.chart-placeholder {
  @apply h-64 flex flex-col items-center justify-center text-gray-500 bg-gray-50 rounded-lg;
}

.task-monitoring {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.task-filters {
  @apply flex gap-3;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm;
}

.task-list {
  @apply mt-4;
}

.empty-state {
  @apply text-center py-8 text-gray-500;
}

.task-items {
  @apply space-y-3;
}

.task-item {
  @apply flex justify-between items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50;
}

.task-name {
  @apply font-medium text-gray-900;
}

.task-meta {
  @apply flex gap-3 text-sm text-gray-500 mt-1;
}
</style>
