<template>
  <div class="cicd-management">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>CI/CD 管理</h1>
          <p>配置和管理持续集成和持续部署流水线，自动化构建、测试和部署流程</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            创建任务
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card ci-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.ci.total }}</div>
          <div class="stat-label">CI 任务</div>
          <div class="stat-detail">
            <span class="running">{{ stats.ci.running }} 运行中</span>
            <span class="completed">{{ stats.ci.completed }} 已完成</span>
          </div>
        </div>
      </div>

      <div class="stat-card cd-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.cd.total }}</div>
          <div class="stat-label">CD 任务</div>
          <div class="stat-detail">
            <span class="running">{{ stats.cd.running }} 运行中</span>
            <span class="completed">{{ stats.cd.completed }} 已完成</span>
          </div>
        </div>
      </div>

      <div class="stat-card pipeline-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.pipelines.total }}</div>
          <div class="stat-label">流水线</div>
          <div class="stat-detail">
            <span class="active">{{ stats.pipelines.active }} 活跃</span>
            <span class="inactive">{{ stats.pipelines.inactive }} 非活跃</span>
          </div>
        </div>
      </div>

      <div class="stat-card success-rate-card">
        <div class="stat-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.successRate }}%</div>
          <div class="stat-label">成功率</div>
          <div class="stat-detail">
            <span class="success">{{ stats.successful }} 成功</span>
            <span class="failed">{{ stats.failed }} 失败</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务类型切换 -->
    <div class="task-type-tabs">
      <button
        v-for="tab in taskTypeTabs"
        :key="tab.key"
        :class="['tab-button', { active: activeTab === tab.key }]"
        @click="activeTab = tab.key"
      >
        <svg class="tab-icon" viewBox="0 0 20 20" fill="currentColor">
          <path :d="tab.icon" />
        </svg>
        {{ tab.label }}
        <span class="tab-count">{{ getTabCount(tab.key) }}</span>
      </button>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section">
      <div class="filter-group">
        <label class="filter-label">按组件筛选：</label>
        <select
          v-model="selectedComponentId"
          @change="handleComponentFilter"
          class="filter-select"
        >
          <option value="">全部组件</option>
          <option
            v-for="component in components"
            :key="component.id"
            :value="component.id"
          >
            {{ component.name }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select
          v-model="selectedStatus"
          @change="handleStatusFilter"
          class="filter-select"
        >
          <option value="">全部状态</option>
          <option value="PENDING">等待中</option>
          <option value="RUNNING">运行中</option>
          <option value="COMPLETED">已完成</option>
          <option value="FAILED">失败</option>
          <option value="CANCELLED">已取消</option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">时间范围：</label>
        <select
          v-model="selectedTimeRange"
          @change="handleTimeRangeFilter"
          class="filter-select"
        >
          <option value="">全部时间</option>
          <option value="today">今天</option>
          <option value="week">本周</option>
          <option value="month">本月</option>
        </select>
      </div>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :data="filteredTasks"
      :columns="tableColumns"
      :loading="loading"
      :title="getTableTitle()"
      searchable
      search-placeholder="搜索任务名称或描述..."
      @search="handleSearch"
      @row-click="handleRowClick"
    >
      <template #toolbar-actions>
        <button
          @click="refreshData"
          :disabled="loading"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
      </template>

      <template #cell-componentName="{ record }">
        <span class="component-link" @click="viewComponent(record.componentId)">
          {{ getComponentName(record.componentId) }}
        </span>
      </template>

      <template #cell-type="{ value }">
        <span class="task-type-badge" :class="`type-${value?.toLowerCase()}`">
          {{ getTaskTypeLabel(value) }}
        </span>
      </template>

      <template #cell-status="{ value }">
        <StatusTag :status="value" />
      </template>

      <template #cell-duration="{ record }">
        <span class="duration-text">
          {{ formatDuration(record.startTime, record.endTime) }}
        </span>
      </template>

      <template #cell-createdAt="{ value }">
        {{ formatDate(value) }}
      </template>

      <template #actions="{ record }">
        <div class="action-buttons">
          <button
            @click="viewTask(record)"
            class="action-btn action-btn-view"
            title="查看详情"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canViewLogs(record)"
            @click="viewLogs(record)"
            class="action-btn action-btn-logs"
            title="查看日志"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
              <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canExecuteTask(record)"
            @click="executeTask(record)"
            class="action-btn action-btn-execute"
            title="执行任务"
            :disabled="executingTask"
          >
            <svg v-if="executingTask" class="animate-spin" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
              <path d="M10 2a8 8 0 018 8" fill="currentColor">
                <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
              </path>
            </svg>
            <svg v-else viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            v-if="canStopTask(record)"
            @click="stopTask(record)"
            class="action-btn action-btn-stop"
            title="停止任务"
            :disabled="stoppingTask"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click="editTask(record)"
            class="action-btn action-btn-edit"
            title="编辑任务"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            @click="deleteTask(record)"
            class="action-btn action-btn-delete"
            title="删除任务"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </template>
    </DataTable>

    <!-- 创建/编辑任务模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-container modal-large" @click.stop>
        <div class="modal-header">
          <h3>{{ showCreateModal ? '创建任务' : '编辑任务' }}</h3>
          <button @click="closeModal" class="modal-close">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="submitForm" class="modal-form">
          <div class="form-grid">
            <div class="form-column">
              <FormField
                v-model="formData.name"
                label="任务名称"
                type="text"
                placeholder="请输入任务名称"
                required
                :error-message="formErrors.name"
              />

              <FormField
                v-model="formData.description"
                label="任务描述"
                type="textarea"
                placeholder="请输入任务描述"
                :rows="3"
                :error-message="formErrors.description"
              />

              <FormField
                v-model="formData.type"
                label="任务类型"
                type="select"
                :options="taskTypeOptions"
                required
                :error-message="formErrors.type"
                @change="handleTaskTypeChange"
              />

              <FormField
                v-model="formData.componentId"
                label="关联组件"
                type="select"
                :options="componentOptions"
                :error-message="formErrors.componentId"
              />
            </div>

            <div class="form-column">
              <FormField
                v-model="formData.triggerType"
                label="触发方式"
                type="select"
                :options="triggerTypeOptions"
                :error-message="formErrors.triggerType"
              />

              <FormField
                v-if="formData.triggerType === 'SCHEDULE'"
                v-model="formData.schedule"
                label="定时表达式"
                type="text"
                placeholder="0 0 2 * * ?"
                :error-message="formErrors.schedule"
              />

              <FormField
                v-model="formData.status"
                label="任务状态"
                type="select"
                :options="statusOptions"
                :error-message="formErrors.status"
              />

              <FormField
                v-model="formData.timeout"
                label="超时时间(分钟)"
                type="number"
                placeholder="30"
                :error-message="formErrors.timeout"
              />
            </div>
          </div>

          <!-- 任务配置 -->
          <div class="task-config-section">
            <h4 class="config-title">任务配置</h4>
            <div class="config-tabs">
              <button
                v-for="tab in configTabs"
                :key="tab.key"
                type="button"
                :class="['config-tab', { active: activeConfigTab === tab.key }]"
                @click="activeConfigTab = tab.key"
              >
                {{ tab.label }}
              </button>
            </div>

            <div class="config-content">
              <!-- 基础配置 -->
              <div v-if="activeConfigTab === 'basic'" class="config-panel">
                <div class="config-grid">
                  <FormField
                    v-model="formData.configuration.workingDirectory"
                    label="工作目录"
                    type="text"
                    placeholder="/workspace"
                  />
                  <FormField
                    v-model="formData.configuration.environment"
                    label="环境变量"
                    type="textarea"
                    placeholder="KEY1=value1&#10;KEY2=value2"
                    :rows="3"
                  />
                </div>
              </div>

              <!-- 脚本配置 -->
              <div v-if="activeConfigTab === 'script'" class="config-panel">
                <div class="script-editor-section">
                  <YamlEditor
                    v-model="formData.configuration.script"
                    title="执行脚本配置"
                    description="配置任务的执行脚本，支持 YAML 格式的 CI/CD 流水线配置"
                    height="400px"
                    :templates="scriptTemplates"
                    @validate="handleScriptValidation"
                  />
                </div>
              </div>

              <!-- 通知配置 -->
              <div v-if="activeConfigTab === 'notification'" class="config-panel">
                <div class="config-grid">
                  <FormField
                    v-model="formData.configuration.notifyOnSuccess"
                    label="成功时通知"
                    type="switch"
                  />
                  <FormField
                    v-model="formData.configuration.notifyOnFailure"
                    label="失败时通知"
                    type="switch"
                  />
                  <FormField
                    v-model="formData.configuration.notificationChannels"
                    label="通知渠道"
                    type="checkbox"
                    :options="notificationChannelOptions"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="modal-actions">
            <button type="button" @click="closeModal" class="btn btn-secondary">
              取消
            </button>
            <button
              type="button"
              @click="validateConfiguration"
              :disabled="validatingConfig"
              class="btn btn-outline"
            >
              <svg v-if="validatingConfig" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
                <path d="M10 2a8 8 0 018 8" fill="currentColor">
                  <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
                </path>
              </svg>
              {{ validatingConfig ? '验证中...' : '验证配置' }}
            </button>
            <button type="submit" :disabled="submitting" class="btn btn-primary">
              <svg v-if="submitting" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
                <path d="M10 2a8 8 0 018 8" fill="currentColor">
                  <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
                </path>
              </svg>
              {{ submitting ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 日志查看模态框 -->
    <div v-if="showLogsModal" class="modal-overlay" @click="closeLogsModal">
      <div class="modal-container modal-logs" @click.stop>
        <div class="modal-header">
          <h3>任务日志 - {{ currentTask?.name }}</h3>
          <div class="logs-actions">
            <button @click="refreshLogs" class="btn btn-sm btn-secondary">
              <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
              </svg>
              刷新
            </button>
            <button @click="closeLogsModal" class="modal-close">
              <svg viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>

        <div class="logs-content">
          <div v-if="loadingLogs" class="logs-loading">
            <div class="loading-spinner">
              <div class="spinner"></div>
              <span>加载日志中...</span>
            </div>
          </div>
          <div v-else-if="taskLogs.length === 0" class="logs-empty">
            <p>暂无日志信息</p>
          </div>
          <div v-else class="logs-container">
            <div
              v-for="(log, index) in taskLogs"
              :key="index"
              :class="['log-line', `log-${log.level?.toLowerCase()}`]"
            >
              <span class="log-timestamp">{{ formatLogTime(log.timestamp) }}</span>
              <span class="log-level">{{ log.level }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认删除对话框 -->
    <ConfirmDialog
      :visible="showDeleteDialog"
      title="确认删除任务"
      :message="`确定要删除任务 &quot;${deleteTarget?.name}&quot; 吗？此操作不可撤销。`"
      type="danger"
      confirm-text="删除"
      :loading="deleting"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import StatusTag from '@/components/StatusTag.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import FormField from '@/components/FormField.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import YamlEditor from '@/components/YamlEditor.vue'
import { useDevOpsStore } from '@/modules/devops/stores/devops'
import type { DevOpsCiTask, DevOpsCdTask, DevOpsComponent, BreadcrumbItem, TableColumn } from '@/modules/devops/types/devops'

const router = useRouter()
const devopsStore = useDevOpsStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const deleting = ref(false)
const executingTask = ref(false)
const stoppingTask = ref(false)
const validatingConfig = ref(false)
const loadingLogs = ref(false)

const tasks = ref<(DevOpsCiTask | DevOpsCdTask)[]>([])
const components = ref<DevOpsComponent[]>([])
const taskLogs = ref<any[]>([])
const searchQuery = ref('')
const selectedComponentId = ref<number | ''>('')
const selectedStatus = ref('')
const selectedTimeRange = ref('')
const activeTab = ref('all')
const activeConfigTab = ref('basic')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const showLogsModal = ref(false)
const deleteTarget = ref<DevOpsCiTask | DevOpsCdTask | null>(null)
const currentTask = ref<DevOpsCiTask | DevOpsCdTask | null>(null)

// 统计数据
const stats = ref({
  ci: { total: 0, running: 0, completed: 0, failed: 0 },
  cd: { total: 0, running: 0, completed: 0, failed: 0 },
  pipelines: { total: 0, active: 0, inactive: 0 },
  successRate: 0,
  successful: 0,
  failed: 0
})

// 表单数据
const formData = ref({
  name: '',
  description: '',
  type: 'CI',
  componentId: null as number | null,
  triggerType: 'MANUAL',
  schedule: '',
  status: 'ACTIVE',
  timeout: 30,
  configuration: {
    workingDirectory: '/workspace',
    environment: '',
    script: '',
    notifyOnSuccess: false,
    notifyOnFailure: true,
    notificationChannels: []
  }
})

const formErrors = ref({
  name: '',
  description: '',
  type: '',
  componentId: '',
  triggerType: '',
  schedule: '',
  status: '',
  timeout: ''
})

const editingTask = ref<DevOpsCiTask | DevOpsCdTask | null>(null)

// 脚本模板
const scriptTemplates = computed(() => [
  {
    id: 'ci-build',
    name: 'CI 构建脚本',
    description: '标准的持续集成构建流水线',
    icon: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z',
    content: `name: CI Build Pipeline
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run tests
      run: npm test -- --coverage

    - name: Build application
      run: npm run build

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts
        path: dist/`
  },
  {
    id: 'cd-deploy',
    name: 'CD 部署脚本',
    description: '标准的持续部署流水线',
    icon: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z',
    content: `name: CD Deploy Pipeline
on:
  workflow_run:
    workflows: ["CI Build Pipeline"]
    types: [completed]
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: \${{ github.event.workflow_run.conclusion == 'success' }}
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Download artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-artifacts
        path: dist/

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here

    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # Add your smoke test commands here

    - name: Deploy to production
      if: success()
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here

    - name: Notify deployment
      if: always()
      run: |
        echo "Sending deployment notification..."
        # Add notification commands here`
  },
  {
    id: 'docker-build',
    name: 'Docker 构建脚本',
    description: 'Docker 镜像构建和推送流水线',
    icon: 'M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z',
    content: `name: Docker Build and Push
on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: \${{ github.repository }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: \${{ env.REGISTRY }}
        username: \${{ github.actor }}
        password: \${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: \${{ env.REGISTRY }}/\${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: \${{ steps.meta.outputs.tags }}
        labels: \${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max`
  }
])

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: 'CI/CD 管理', active: true, icon: 'cicd' }
])

const tableColumns: TableColumn[] = [
  { key: 'name', title: '任务名称', sortable: true },
  { key: 'componentName', title: '关联组件', width: '150px' },
  { key: 'type', title: '类型', width: '80px' },
  { key: 'status', title: '状态', width: '120px' },
  { key: 'duration', title: '执行时长', width: '120px' },
  { key: 'createdAt', title: '创建时间', width: '180px', sortable: true }
]

const taskTypeTabs = [
  { key: 'all', label: '全部任务', icon: 'M4 6a2 2 0 012-2h8a2 2 0 012 2v7a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM6 8a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 4a1 1 0 100 2h6a1 1 0 100-2H7z' },
  { key: 'ci', label: 'CI 任务', icon: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z' },
  { key: 'cd', label: 'CD 任务', icon: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z' }
]

const configTabs = [
  { key: 'basic', label: '基础配置' },
  { key: 'script', label: '脚本配置' },
  { key: 'notification', label: '通知配置' }
]

const componentOptions = computed(() =>
  components.value.map(comp => ({
    label: comp.name,
    value: comp.id!
  }))
)

const taskTypeOptions = [
  { label: 'CI 任务', value: 'CI' },
  { label: 'CD 任务', value: 'CD' }
]

const triggerTypeOptions = [
  { label: '手动触发', value: 'MANUAL' },
  { label: '代码提交', value: 'PUSH' },
  { label: '定时触发', value: 'SCHEDULE' },
  { label: 'API 触发', value: 'API' }
]

const statusOptions = [
  { label: '活跃', value: 'ACTIVE' },
  { label: '非活跃', value: 'INACTIVE' },
  { label: '已暂停', value: 'PAUSED' }
]

const notificationChannelOptions = [
  { label: '邮件', value: 'email' },
  { label: '钉钉', value: 'dingtalk' },
  { label: '企业微信', value: 'wechat' },
  { label: 'Slack', value: 'slack' }
]

const filteredTasks = computed(() => {
  let result = [...tasks.value]

  // 按任务类型筛选
  if (activeTab.value !== 'all') {
    result = result.filter(task => task.type?.toLowerCase() === activeTab.value)
  }

  // 按组件筛选
  if (selectedComponentId.value) {
    result = result.filter(task => task.componentId === selectedComponentId.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    result = result.filter(task => task.status === selectedStatus.value)
  }

  // 按时间范围筛选
  if (selectedTimeRange.value) {
    const now = new Date()
    const filterDate = new Date()

    switch (selectedTimeRange.value) {
      case 'today':
        filterDate.setHours(0, 0, 0, 0)
        break
      case 'week':
        filterDate.setDate(now.getDate() - 7)
        break
      case 'month':
        filterDate.setMonth(now.getMonth() - 1)
        break
    }

    result = result.filter(task => new Date(task.createdAt) >= filterDate)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(task =>
      task.name.toLowerCase().includes(query) ||
      (task.description && task.description.toLowerCase().includes(query))
    )
  }

  return result
})

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据
    tasks.value = [
      {
        id: 1,
        name: '用户服务构建',
        description: '构建用户认证服务',
        type: 'CI',
        componentId: 1,
        status: 'COMPLETED',
        triggerType: 'PUSH',
        startTime: '2024-01-15T10:30:00Z',
        endTime: '2024-01-15T10:35:00Z',
        createdAt: '2024-01-15T10:30:00Z',
        userId: 1,
        configuration: {}
      },
      {
        id: 2,
        name: '订单服务部署',
        description: '部署订单处理服务到生产环境',
        type: 'CD',
        componentId: 2,
        status: 'RUNNING',
        triggerType: 'MANUAL',
        startTime: '2024-01-15T11:00:00Z',
        createdAt: '2024-01-15T11:00:00Z',
        userId: 1,
        configuration: {}
      },
      {
        id: 3,
        name: '支付服务测试',
        description: '运行支付服务的自动化测试',
        type: 'CI',
        componentId: 3,
        status: 'FAILED',
        triggerType: 'SCHEDULE',
        startTime: '2024-01-15T09:00:00Z',
        endTime: '2024-01-15T09:10:00Z',
        createdAt: '2024-01-15T09:00:00Z',
        userId: 1,
        configuration: {}
      }
    ]

    // 更新统计数据
    updateStats()
  } finally {
    loading.value = false
  }
}

const loadComponents = async () => {
  try {
    // 模拟组件数据
    components.value = [
      { id: 1, name: '用户认证服务', description: '用户认证', applicationId: 1, status: 'ACTIVE', createdAt: '2024-01-15T10:30:00Z', userId: 1 },
      { id: 2, name: '订单处理引擎', description: '订单处理', applicationId: 1, status: 'ACTIVE', createdAt: '2024-01-16T14:20:00Z', userId: 1 },
      { id: 3, name: '支付网关', description: '支付处理', applicationId: 1, status: 'ACTIVE', createdAt: '2024-01-17T09:15:00Z', userId: 1 }
    ]
  } catch (error) {
    console.error('加载组件失败:', error)
  }
}

const updateStats = () => {
  const ciTasks = tasks.value.filter(task => task.type === 'CI')
  const cdTasks = tasks.value.filter(task => task.type === 'CD')

  stats.value = {
    ci: {
      total: ciTasks.length,
      running: ciTasks.filter(task => task.status === 'RUNNING').length,
      completed: ciTasks.filter(task => task.status === 'COMPLETED').length,
      failed: ciTasks.filter(task => task.status === 'FAILED').length
    },
    cd: {
      total: cdTasks.length,
      running: cdTasks.filter(task => task.status === 'RUNNING').length,
      completed: cdTasks.filter(task => task.status === 'COMPLETED').length,
      failed: cdTasks.filter(task => task.status === 'FAILED').length
    },
    pipelines: {
      total: tasks.value.length,
      active: tasks.value.filter(task => task.status === 'ACTIVE').length,
      inactive: tasks.value.filter(task => task.status === 'INACTIVE').length
    },
    successRate: Math.round((tasks.value.filter(task => task.status === 'COMPLETED').length / tasks.value.length) * 100) || 0,
    successful: tasks.value.filter(task => task.status === 'COMPLETED').length,
    failed: tasks.value.filter(task => task.status === 'FAILED').length
  }
}

const refreshData = () => {
  loadTasks()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
}

const handleComponentFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStatusFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleTimeRangeFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleRowClick = (task: DevOpsCiTask | DevOpsCdTask) => {
  viewTask(task)
}

const getTableTitle = () => {
  const tabLabel = taskTypeTabs.find(tab => tab.key === activeTab.value)?.label || '任务列表'
  return `${tabLabel} (${filteredTasks.value.length})`
}

const getTabCount = (tabKey: string) => {
  if (tabKey === 'all') return tasks.value.length
  if (tabKey === 'ci') return tasks.value.filter(task => task.type === 'CI').length
  if (tabKey === 'cd') return tasks.value.filter(task => task.type === 'CD').length
  return 0
}

const getComponentName = (componentId: number) => {
  const component = components.value.find(c => c.id === componentId)
  return component?.name || '未知组件'
}

const getTaskTypeLabel = (type: string) => {
  return type === 'CI' ? 'CI' : 'CD'
}

const formatDuration = (startTime?: string, endTime?: string) => {
  if (!startTime) return '-'

  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : new Date()
  const duration = Math.floor((end.getTime() - start.getTime()) / 1000)

  if (duration < 60) return `${duration}秒`
  if (duration < 3600) return `${Math.floor(duration / 60)}分${duration % 60}秒`
  return `${Math.floor(duration / 3600)}时${Math.floor((duration % 3600) / 60)}分`
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatLogTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

const viewComponent = (componentId: number) => {
  router.push(`/devops/components/${componentId}`)
}

const viewTask = (task: DevOpsCiTask | DevOpsCdTask) => {
  router.push(`/devops/ci-cd/${task.id}`)
}

const canViewLogs = (task: DevOpsCiTask | DevOpsCdTask) => {
  return task && task.status && ['RUNNING', 'COMPLETED', 'FAILED'].includes(task.status)
}

const canExecuteTask = (task: DevOpsCiTask | DevOpsCdTask) => {
  return task && task.status && ['PENDING', 'FAILED', 'CANCELLED'].includes(task.status)
}

const canStopTask = (task: DevOpsCiTask | DevOpsCdTask) => {
  return task && task.status === 'RUNNING'
}

const viewLogs = async (task: DevOpsCiTask | DevOpsCdTask) => {
  currentTask.value = task
  showLogsModal.value = true
  await loadTaskLogs(task.id!)
}

const loadTaskLogs = async (taskId: number) => {
  loadingLogs.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟日志数据
    taskLogs.value = [
      { timestamp: '2024-01-15T10:30:00Z', level: 'INFO', message: '开始执行任务...' },
      { timestamp: '2024-01-15T10:30:05Z', level: 'INFO', message: '正在拉取代码...' },
      { timestamp: '2024-01-15T10:30:10Z', level: 'INFO', message: '代码拉取完成' },
      { timestamp: '2024-01-15T10:30:15Z', level: 'INFO', message: '开始构建...' },
      { timestamp: '2024-01-15T10:32:00Z', level: 'WARN', message: '发现警告: 未使用的导入' },
      { timestamp: '2024-01-15T10:34:30Z', level: 'INFO', message: '构建完成' },
      { timestamp: '2024-01-15T10:35:00Z', level: 'INFO', message: '任务执行成功' }
    ]
  } finally {
    loadingLogs.value = false
  }
}

const refreshLogs = () => {
  if (currentTask.value) {
    loadTaskLogs(currentTask.value.id!)
  }
}

const executeTask = async (task: DevOpsCiTask | DevOpsCdTask) => {
  if (!canExecuteTask(task)) return

  executingTask.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 更新任务状态
    const index = tasks.value.findIndex(t => t.id === task.id)
    if (index !== -1) {
      tasks.value[index] = {
        ...tasks.value[index],
        status: 'RUNNING',
        startTime: new Date().toISOString()
      }
    }

    updateStats()
    alert(`任务 "${task.name}" 已开始执行`)
  } catch (error) {
    alert(`执行任务失败: ${error}`)
  } finally {
    executingTask.value = false
  }
}

const stopTask = async (task: DevOpsCiTask | DevOpsCdTask) => {
  if (!canStopTask(task)) return

  stoppingTask.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新任务状态
    const index = tasks.value.findIndex(t => t.id === task.id)
    if (index !== -1) {
      tasks.value[index] = {
        ...tasks.value[index],
        status: 'CANCELLED',
        endTime: new Date().toISOString()
      }
    }

    updateStats()
    alert(`任务 "${task.name}" 已停止`)
  } catch (error) {
    alert(`停止任务失败: ${error}`)
  } finally {
    stoppingTask.value = false
  }
}

const editTask = (task: any) => {
  editingTask.value = task
  formData.value = {
    name: task.name,
    description: task.description || '',
    type: task.type || 'CI',
    componentId: task.componentId ?? null,
    triggerType: task.triggerType || 'MANUAL',
    schedule: task.schedule || '',
    status: task.status || 'ACTIVE',
    timeout: task.timeout ?? 30,
    configuration: {
      workingDirectory: task.configuration?.workingDirectory ?? '/workspace',
      environment: task.configuration?.environment ?? '',
      script: task.configuration?.script ?? '',
      notifyOnSuccess: task.configuration?.notifyOnSuccess ?? false,
      notifyOnFailure: task.configuration?.notifyOnFailure ?? true,
      notificationChannels: task.configuration?.notificationChannels ?? []
    }
  }
  showEditModal.value = true
}

const deleteTask = (task: DevOpsCiTask | DevOpsCdTask) => {
  deleteTarget.value = task
  showDeleteDialog.value = true
}

const handleTaskTypeChange = () => {
  // 根据任务类型设置默认配置
  if (formData.value.type === 'CI') {
    formData.value.configuration.script = '#!/bin/bash\n# CI 构建脚本\necho "开始构建..."\n# 在这里添加构建命令'
  } else if (formData.value.type === 'CD') {
    formData.value.configuration.script = '#!/bin/bash\n# CD 部署脚本\necho "开始部署..."\n# 在这里添加部署命令'
  }
}

const handleScriptValidation = (errors: any[]) => {
  // 处理脚本验证结果
  if (errors.length > 0) {
    console.warn('脚本配置存在验证错误:', errors)
  }
}

const validateConfiguration = async () => {
  validatingConfig.value = true
  try {
    // 模拟配置验证
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 简单验证
    if (!formData.value.configuration.script) {
      alert('请配置执行脚本')
      return
    }

    alert('配置验证通过！')
  } catch (error) {
    alert(`配置验证失败: ${error}`)
  } finally {
    validatingConfig.value = false
  }
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  resetForm()
}

const closeLogsModal = () => {
  showLogsModal.value = false
  currentTask.value = null
  taskLogs.value = []
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    type: 'CI',
    componentId: null,
    triggerType: 'MANUAL',
    schedule: '',
    status: 'ACTIVE',
    timeout: 30,
    configuration: {
      workingDirectory: '/workspace',
      environment: '',
      script: '',
      notifyOnSuccess: false,
      notifyOnFailure: true,
      notificationChannels: []
    }
  }
  formErrors.value = {
    name: '',
    description: '',
    type: '',
    componentId: '',
    triggerType: '',
    schedule: '',
    status: '',
    timeout: ''
  }
  editingTask.value = null
  activeConfigTab.value = 'basic'
}

const validateForm = () => {
  const errors = {
    name: '',
    description: '',
    type: '',
    componentId: '',
    triggerType: '',
    schedule: '',
    status: '',
    timeout: ''
  }
  let isValid = true

  if (!formData.value.name.trim()) {
    errors.name = '任务名称不能为空'
    isValid = false
  }

  if (!formData.value.type) {
    errors.type = '请选择任务类型'
    isValid = false
  }

  if (formData.value.triggerType === 'SCHEDULE' && !formData.value.schedule) {
    errors.schedule = '定时触发需要配置定时表达式'
    isValid = false
  }

  formErrors.value = errors
  return isValid
}

const submitForm = async () => {
  if (!validateForm()) return

  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (showCreateModal.value) {
      // 创建任务
      if (formData.value.componentId === null) {
        alert('请选择关联组件');
        submitting.value = false;
        return;
      }
      const newTask: DevOpsCiTask | DevOpsCdTask = {
        id: Date.now(),
        ...formData.value,
        componentId: formData.value.componentId, // guaranteed to be number here
        createdAt: new Date().toISOString(),
        userId: 1,
        taskType: formData.value.type // Add this line to satisfy the interface
      }
      tasks.value.unshift(newTask)
    } else if (showEditModal.value && editingTask.value) {
      // 更新任务
      const index = tasks.value.findIndex(t => t.id === editingTask.value!.id)
      if (index !== -1) {
        if (formData.value.componentId === null) {
          alert('请选择关联组件');
          submitting.value = false;
          return;
        }
        tasks.value[index] = {
          ...tasks.value[index],
          ...formData.value,
          componentId: formData.value.componentId, // guaranteed to be number here
          updatedAt: new Date().toISOString()
        }
      }
    }

    updateStats()
    closeModal()
  } finally {
    submitting.value = false
  }
}

const confirmDelete = async () => {
  if (!deleteTarget.value) return

  deleting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const index = tasks.value.findIndex(t => t.id === deleteTarget.value!.id)
    if (index !== -1) {
      tasks.value.splice(index, 1)
    }

    updateStats()
    showDeleteDialog.value = false
    deleteTarget.value = null
  } finally {
    deleting.value = false
  }
}

// 生命周期
onMounted(() => {
  loadComponents()
  loadTasks()
})
</script>

<style scoped>
.cicd-management {
  @apply p-6 bg-gray-50 min-h-screen;
}

/* 页面头部 */
.page-header {
  @apply mb-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-2xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-3;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 flex items-center gap-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.stat-icon svg {
  @apply w-6 h-6;
}

.ci-card .stat-icon {
  @apply bg-blue-100 text-blue-600;
}

.cd-card .stat-icon {
  @apply bg-green-100 text-green-600;
}

.pipeline-card .stat-icon {
  @apply bg-purple-100 text-purple-600;
}

.success-rate-card .stat-icon {
  @apply bg-emerald-100 text-emerald-600;
}

.stat-content {
  @apply flex-1;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

.stat-detail {
  @apply flex items-center gap-3 mt-2 text-xs;
}

.stat-detail .running {
  @apply text-blue-600;
}

.stat-detail .completed {
  @apply text-green-600;
}

.stat-detail .active {
  @apply text-green-600;
}

.stat-detail .inactive {
  @apply text-gray-500;
}

.stat-detail .success {
  @apply text-green-600;
}

.stat-detail .failed {
  @apply text-red-600;
}

/* 任务类型切换 */
.task-type-tabs {
  @apply flex items-center gap-2 mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-2;
}

.tab-button {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors;
}

.tab-button:not(.active) {
  @apply text-gray-600 hover:text-gray-900 hover:bg-gray-50;
}

.tab-button.active {
  @apply bg-blue-100 text-blue-700;
}

.tab-icon {
  @apply w-4 h-4;
}

.tab-count {
  @apply ml-1 px-2 py-0.5 text-xs bg-gray-200 text-gray-700 rounded-full;
}

.tab-button.active .tab-count {
  @apply bg-blue-200 text-blue-800;
}

/* 筛选器部分 */
.filters-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6 flex items-center gap-6;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none;
  min-width: 150px;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-outline {
  @apply bg-transparent text-blue-600 border border-blue-300 hover:bg-blue-50 focus:ring-blue-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-icon {
  @apply w-4 h-4;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 组件链接 */
.component-link {
  @apply text-blue-600 hover:text-blue-800 cursor-pointer hover:underline transition-colors;
}

/* 任务类型标签 */
.task-type-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
}

.task-type-badge.type-ci {
  @apply bg-blue-100 text-blue-800;
}

.task-type-badge.type-cd {
  @apply bg-green-100 text-green-800;
}

/* 执行时长 */
.duration-text {
  @apply text-sm text-gray-600 font-mono;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-1;
}

.action-btn {
  @apply p-2 rounded-md transition-colors;
}

.action-btn svg {
  @apply w-4 h-4;
}

.action-btn-view {
  @apply text-blue-600 hover:bg-blue-50;
}

.action-btn-logs {
  @apply text-gray-600 hover:bg-gray-50;
}

.action-btn-execute {
  @apply text-green-600 hover:bg-green-50;
}

.action-btn-stop {
  @apply text-red-600 hover:bg-red-50;
}

.action-btn-edit {
  @apply text-orange-600 hover:bg-orange-50;
}

.action-btn-delete {
  @apply text-red-600 hover:bg-red-50;
}

/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl w-full max-w-md;
}

.modal-large {
  @apply max-w-6xl;
}

.modal-logs {
  @apply max-w-5xl max-h-[80vh] flex flex-col;
}

.modal-header {
  @apply flex items-center justify-between p-6 pb-4;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.logs-actions {
  @apply flex items-center gap-2;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 p-1 rounded;
}

.modal-close svg {
  @apply w-5 h-5;
}

.modal-form {
  @apply px-6 pb-6;
}

.form-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.form-column {
  @apply space-y-4;
}

.modal-actions {
  @apply flex items-center justify-end gap-3 mt-6;
}

/* 任务配置 */
.task-config-section {
  @apply mt-6 border-t border-gray-200 pt-6;
}

.config-title {
  @apply text-base font-semibold text-gray-900 mb-4 m-0;
}

.config-tabs {
  @apply flex items-center gap-1 mb-4 border-b border-gray-200;
}

.config-tab {
  @apply px-4 py-2 text-sm font-medium text-gray-600 border-b-2 border-transparent hover:text-gray-900 hover:border-gray-300 transition-colors;
}

.config-tab.active {
  @apply text-blue-600 border-blue-600;
}

.config-content {
  @apply min-h-48;
}

.config-panel {
  @apply space-y-4;
}

.config-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

/* 脚本编辑器 */
.script-editor-section {
  @apply border border-gray-200 rounded-lg overflow-hidden;
}

/* 日志查看 */
.logs-content {
  @apply flex-1 overflow-hidden;
}

.logs-loading {
  @apply flex items-center justify-center py-12;
}

.loading-spinner {
  @apply flex items-center gap-3 text-gray-600;
}

.spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin;
}

.logs-empty {
  @apply flex items-center justify-center py-12 text-gray-500;
}

.logs-container {
  @apply h-96 overflow-y-auto bg-gray-900 text-gray-100 p-4 font-mono text-sm;
}

.log-line {
  @apply flex items-start gap-3 py-1 border-l-2 border-transparent pl-3;
}

.log-line.log-info {
  @apply border-blue-500;
}

.log-line.log-warn {
  @apply border-yellow-500 text-yellow-300;
}

.log-line.log-error {
  @apply border-red-500 text-red-300;
}

.log-timestamp {
  @apply text-gray-400 text-xs whitespace-nowrap;
}

.log-level {
  @apply text-xs font-medium px-2 py-0.5 rounded uppercase whitespace-nowrap;
}

.log-line.log-info .log-level {
  @apply bg-blue-600 text-white;
}

.log-line.log-warn .log-level {
  @apply bg-yellow-600 text-white;
}

.log-line.log-error .log-level {
  @apply bg-red-600 text-white;
}

.log-message {
  @apply flex-1 break-words;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cicd-management {
    @apply p-4;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }

  .stats-grid {
    @apply grid-cols-1 gap-4;
  }

  .task-type-tabs {
    @apply flex-col items-stretch gap-2;
  }

  .filters-section {
    @apply flex-col items-start gap-4;
  }

  .filter-group {
    @apply w-full;
  }

  .filter-select {
    @apply w-full;
  }

  .modal-container {
    @apply max-w-full mx-4;
  }

  .form-grid {
    @apply grid-cols-1;
  }

  .config-grid {
    @apply grid-cols-1;
  }

  .action-buttons {
    @apply flex-wrap gap-2;
  }
}

/* 动画效果 */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 悬停效果 */
.action-btn:hover {
  @apply transform scale-105;
}

.component-link:hover {
  @apply transform translate-x-1;
}

.stat-card:hover {
  @apply transform -translate-y-1;
}

/* 加载动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
