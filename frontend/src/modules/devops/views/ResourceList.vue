<template>
  <div class="resource-list">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>资源管理</h1>
          <p>管理组件相关的资源，包括数据库、缓存、存储等基础设施资源</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            创建资源
          </button>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section">
      <div class="filter-group">
        <label class="filter-label">按组件筛选：</label>
        <select
          v-model="selectedComponentId"
          @change="handleComponentFilter"
          class="filter-select"
        >
          <option value="">全部组件</option>
          <option
            v-for="component in components"
            :key="component.id"
            :value="component.id"
          >
            {{ component.name }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">资源类型：</label>
        <select
          v-model="selectedResourceType"
          @change="handleResourceTypeFilter"
          class="filter-select"
        >
          <option value="">全部类型</option>
          <option
            v-for="type in resourceTypes"
            :key="type"
            :value="type"
          >
            {{ getResourceTypeLabel(type) }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select
          v-model="selectedStatus"
          @change="handleStatusFilter"
          class="filter-select"
        >
          <option value="">全部状态</option>
          <option value="ACTIVE">活跃</option>
          <option value="INACTIVE">非活跃</option>
          <option value="MAINTENANCE">维护中</option>
        </select>
      </div>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :data="filteredResources"
      :columns="tableColumns"
      :loading="loading"
      title="资源列表"
      searchable
      search-placeholder="搜索资源名称或路径..."
      @search="handleSearch"
      @row-click="handleRowClick"
    >
      <template #toolbar-actions>
        <button
          @click="refreshData"
          :disabled="loading"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
      </template>

      <template #cell-componentName="{ record }">
        <span class="component-link" @click="viewComponent(record?.componentId)">
          {{ getComponentName(record?.componentId) }}
        </span>
      </template>

      <template #cell-type="{ value }">
        <span class="resource-type-badge" :class="`type-${value?.toLowerCase()}`">
          <svg class="type-icon" viewBox="0 0 20 20" fill="currentColor">
            <path :d="getResourceTypeIcon(value)" />
          </svg>
          {{ getResourceTypeLabel(value) }}
        </span>
      </template>

      <template #cell-path="{ value }">
        <code class="resource-path">{{ value || '-' }}</code>
      </template>

      <template #cell-status="{ value }">
        <StatusTag :status="value" />
      </template>

      <template #cell-createdAt="{ value }">
        {{ formatDate(value) }}
      </template>

      <template #actions="{ record }">
        <div class="action-buttons">
          <button
            @click="viewResource(record)"
            class="action-btn action-btn-view"
            title="查看详情"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click="testConnection(record)"
            class="action-btn action-btn-test"
            title="测试连接"
            :disabled="testingConnection"
          >
            <svg v-if="testingConnection" class="animate-spin" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
              <path d="M10 2a8 8 0 018 8" fill="currentColor">
                <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
              </path>
            </svg>
            <svg v-else viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click="editResource(record)"
            class="action-btn action-btn-edit"
            title="编辑资源"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            @click="deleteResource(record)"
            class="action-btn action-btn-delete"
            title="删除资源"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </template>
    </DataTable>

    <!-- 创建/编辑资源模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-container modal-large" @click.stop>
        <div class="modal-header">
          <h3>{{ showCreateModal ? '创建资源' : '编辑资源' }}</h3>
          <button @click="closeModal" class="modal-close">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="submitForm" class="modal-form">
          <div class="form-grid">
            <div class="form-column">
              <FormField
                v-model="formData.name"
                label="资源名称"
                type="text"
                placeholder="请输入资源名称"
                required
                :error-message="formErrors.name"
              />

              <FormField
                v-model="formData.description"
                label="资源描述"
                type="textarea"
                placeholder="请输入资源描述"
                :rows="3"
                :error-message="formErrors.description"
              />

              <FormField
                v-model="formData.componentId"
                label="所属组件"
                type="select"
                :options="componentOptions"
                required
                :error-message="formErrors.componentId"
              />

              <FormField
                v-model="formData.type"
                label="资源类型"
                type="select"
                :options="resourceTypeOptions"
                required
                :error-message="formErrors.type"
                @change="handleResourceTypeChange"
              />
            </div>

            <div class="form-column">
              <FormField
                v-model="formData.path"
                label="资源路径"
                type="text"
                :placeholder="getPathPlaceholder(formData.type)"
                :error-message="formErrors.path"
              />

              <FormField
                v-model="formData.status"
                label="资源状态"
                type="select"
                :options="statusOptions"
                :error-message="formErrors.status"
              />

              <!-- 动态配置字段 -->
              <div v-if="formData.type" class="dynamic-config">
                <h4 class="config-title">{{ getResourceTypeLabel(formData.type) }}配置</h4>
                <div v-for="field in getConfigFields(formData.type)" :key="field.key" class="config-field">
                  <FormField
                    v-model="formData.configuration[field.key]"
                    :label="field.label"
                    :type="field.type"
                    :placeholder="field.placeholder"
                    :required="field.required"
                    :options="field.options"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="modal-actions">
            <button type="button" @click="closeModal" class="btn btn-secondary">
              取消
            </button>
            <button
              v-if="formData.type && canTestConnection(formData.type)"
              type="button"
              @click="testFormConnection"
              :disabled="testingConnection"
              class="btn btn-outline"
            >
              <svg v-if="testingConnection" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
                <path d="M10 2a8 8 0 018 8" fill="currentColor">
                  <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
                </path>
              </svg>
              {{ testingConnection ? '测试中...' : '测试连接' }}
            </button>
            <button type="submit" :disabled="submitting" class="btn btn-primary">
              <svg v-if="submitting" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
                <path d="M10 2a8 8 0 018 8" fill="currentColor">
                  <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
                </path>
              </svg>
              {{ submitting ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 确认删除对话框 -->
    <ConfirmDialog
      :visible="showDeleteDialog"
      title="确认删除资源"
      :message="`确定要删除资源 &quot;${deleteTarget?.name}&quot; 吗？此操作不可撤销。`"
      type="danger"
      confirm-text="删除"
      :loading="deleting"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import StatusTag from '@/components/StatusTag.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import FormField from '@/components/FormField.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { useDevOpsStore } from '@/modules/devops/stores/devops'
import type { DevOpsResource, DevOpsComponent, BreadcrumbItem, TableColumn } from '@/modules/devops/types/devops'
import { RESOURCE_TYPES } from '@/modules/devops/types/devops'

const router = useRouter()
const route = useRoute()
const devopsStore = useDevOpsStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const deleting = ref(false)
const testingConnection = ref(false)
const resources = ref<DevOpsResource[]>([])
const components = ref<DevOpsComponent[]>([])
const resourceTypes = ref<string[]>([])
const searchQuery = ref('')
const selectedComponentId = ref<number | ''>('')
const selectedResourceType = ref('')
const selectedStatus = ref('')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const deleteTarget = ref<DevOpsResource | null>(null)

// 表单数据
const formData = ref({
  name: '',
  description: '',
  componentId: null as number | null,
  type: '',
  path: '',
  status: 'ACTIVE',
  configuration: {} as Record<string, any>
})

const formErrors = ref({
  name: '',
  description: '',
  componentId: '',
  type: '',
  path: '',
  status: ''
})

const editingResource = ref<DevOpsResource | null>(null)

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: '资源管理', active: true, icon: 'resource' }
])

const tableColumns: TableColumn[] = [
  { key: 'name', title: '资源名称', sortable: true },
  { key: 'componentName', title: '所属组件', width: '150px' },
  { key: 'type', title: '资源类型', width: '120px' },
  { key: 'path', title: '资源路径', width: '200px' },
  { key: 'status', title: '状态', width: '120px' },
  { key: 'createdAt', title: '创建时间', width: '180px', sortable: true }
]

const componentOptions = computed(() =>
  components.value.map(comp => ({
    label: comp.name,
    value: comp.id!
  }))
)

const statusOptions = [
  { label: '活跃', value: 'ACTIVE' },
  { label: '非活跃', value: 'INACTIVE' },
  { label: '维护中', value: 'MAINTENANCE' }
]

const resourceTypeOptions = computed(() =>
  resourceTypes.value.map(type => ({
    label: getResourceTypeLabel(type),
    value: type
  }))
)

const filteredResources = computed(() => {
  let result = [...resources.value]

  // 按组件筛选
  if (selectedComponentId.value) {
    result = result.filter(res => res.componentId === selectedComponentId.value)
  }

  // 按资源类型筛选
  if (selectedResourceType.value) {
    result = result.filter(res => res.type === selectedResourceType.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    result = result.filter(res => res.status === selectedStatus.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(res =>
      res.name.toLowerCase().includes(query) ||
      (res.path && res.path.toLowerCase().includes(query)) ||
      (res.description && res.description.toLowerCase().includes(query))
    )
  }

  return result
})

// 资源类型配置
type FormFieldType =
  | 'number'
  | 'text'
  | 'email'
  | 'password'
  | 'url'
  | 'textarea'
  | 'select'
  | 'checkbox'
  | 'radio'
  | 'switch'
  | 'date'
  | 'datetime-local'
  | 'file'
  | undefined

type ResourceTypeConfig = {
  label: string
  icon: string
  pathPlaceholder: string
  canTest: boolean
  configFields: Array<{
    key: string
    label: string
    type: FormFieldType
    placeholder?: string
    required?: boolean
    options?: Array<{ label: string; value: string }>
  }>
}
const resourceTypeConfig: { [key: string]: ResourceTypeConfig } = {
  [RESOURCE_TYPES.SERVICE]: {
    label: '服务',
    icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
    pathPlaceholder: '/api/service',
    canTest: true,
    configFields: [
      { key: 'host', label: '主机地址', type: 'text', placeholder: 'localhost', required: true },
      { key: 'port', label: '端口', type: 'number', placeholder: '8080', required: true },
      { key: 'protocol', label: '协议', type: 'select', options: [
        { label: 'HTTP', value: 'http' },
        { label: 'HTTPS', value: 'https' }
      ], required: true }
    ]
  },
  [RESOURCE_TYPES.DATABASE]: {
    label: '数据库',
    icon: 'M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4',
    pathPlaceholder: '******************************',
    canTest: true,
    configFields: [
      { key: 'host', label: '数据库主机', type: 'text', placeholder: 'localhost', required: true },
      { key: 'port', label: '端口', type: 'number', placeholder: '3306', required: true },
      { key: 'database', label: '数据库名', type: 'text', placeholder: 'mydb', required: true },
      { key: 'username', label: '用户名', type: 'text', placeholder: 'root', required: true },
      { key: 'password', label: '密码', type: 'password', placeholder: '******', required: true }
    ]
  },
  [RESOURCE_TYPES.CACHE]: {
    label: '缓存',
    icon: 'M13 10V3L4 14h7v7l9-11h-7z',
    pathPlaceholder: 'redis://localhost:6379',
    canTest: true,
    configFields: [
      { key: 'host', label: '缓存主机', type: 'text', placeholder: 'localhost', required: true },
      { key: 'port', label: '端口', type: 'number', placeholder: '6379', required: true },
      { key: 'database', label: '数据库索引', type: 'number', placeholder: '0' },
      { key: 'password', label: '密码', type: 'password', placeholder: '留空表示无密码' }
    ]
  },
  [RESOURCE_TYPES.QUEUE]: {
    label: '消息队列',
    icon: 'M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h4a1 1 0 011 1v2a1 1 0 01-1 1h-1v9a2 2 0 01-2 2H6a2 2 0 01-2-2V8H3a1 1 0 01-1-1V5a1 1 0 011-1h4zM9 3v1h2V3H9zM6 8v9h8V8H6z',
    pathPlaceholder: 'amqp://localhost:5672',
    canTest: true,
    configFields: [
      { key: 'host', label: '队列主机', type: 'text', placeholder: 'localhost', required: true },
      { key: 'port', label: '端口', type: 'number', placeholder: '5672', required: true },
      { key: 'vhost', label: '虚拟主机', type: 'text', placeholder: '/' },
      { key: 'username', label: '用户名', type: 'text', placeholder: 'guest' },
      { key: 'password', label: '密码', type: 'password', placeholder: 'guest' }
    ]
  },
  [RESOURCE_TYPES.STORAGE]: {
    label: '存储',
    icon: 'M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z',
    pathPlaceholder: '/data/storage',
    canTest: false,
    configFields: [
      { key: 'type', label: '存储类型', type: 'select', options: [
        { label: '本地存储', value: 'local' },
        { label: 'AWS S3', value: 's3' },
        { label: '阿里云OSS', value: 'oss' }
      ], required: true },
      { key: 'bucket', label: '存储桶', type: 'text', placeholder: 'my-bucket' },
      { key: 'region', label: '区域', type: 'text', placeholder: 'us-east-1' },
      { key: 'accessKey', label: '访问密钥', type: 'text', placeholder: 'AKIAIOSFODNN7EXAMPLE' },
      { key: 'secretKey', label: '密钥', type: 'password', placeholder: '******' }
    ]
  },
  [RESOURCE_TYPES.API]: {
    label: 'API接口',
    icon: 'M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z',
    pathPlaceholder: '/api/v1/endpoint',
    canTest: true,
    configFields: [
      { key: 'baseUrl', label: '基础URL', type: 'url', placeholder: 'https://api.example.com', required: true },
      { key: 'version', label: 'API版本', type: 'text', placeholder: 'v1' },
      { key: 'authType', label: '认证类型', type: 'select', options: [
        { label: '无认证', value: 'none' },
        { label: 'API Key', value: 'apikey' },
        { label: 'Bearer Token', value: 'bearer' }
      ], required: true },
      { key: 'apiKey', label: 'API密钥', type: 'password', placeholder: '******' },
      { key: 'timeout', label: '超时时间(秒)', type: 'number', placeholder: '30' }
    ]
  }
}

// 方法
const loadResources = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据
    resources.value = [
      {
        id: 1,
        name: '用户数据库',
        description: '存储用户信息的MySQL数据库',
        componentId: 1,
        type: RESOURCE_TYPES.DATABASE,
        path: '*********************************',
        status: 'ACTIVE',
        configuration: {
          host: 'localhost',
          port: 3306,
          database: 'users',
          username: 'root'
        },
        createdAt: '2024-01-15T10:30:00Z',
        userId: 1
      },
      {
        id: 2,
        name: '会话缓存',
        description: 'Redis缓存服务器',
        componentId: 1,
        type: RESOURCE_TYPES.CACHE,
        path: 'redis://localhost:6379',
        status: 'ACTIVE',
        configuration: {
          host: 'localhost',
          port: 6379,
          database: 0
        },
        createdAt: '2024-01-16T14:20:00Z',
        userId: 1
      },
      {
        id: 3,
        name: '订单队列',
        description: 'RabbitMQ消息队列',
        componentId: 2,
        type: RESOURCE_TYPES.QUEUE,
        path: 'amqp://localhost:5672',
        status: 'ACTIVE',
        configuration: {
          host: 'localhost',
          port: 5672,
          vhost: '/',
          username: 'guest'
        },
        createdAt: '2024-01-17T09:15:00Z',
        userId: 1
      },
      {
        id: 4,
        name: '文件存储',
        description: 'AWS S3存储桶',
        componentId: 3,
        type: RESOURCE_TYPES.STORAGE,
        path: '/uploads',
        status: 'ACTIVE',
        configuration: {
          type: 's3',
          bucket: 'my-app-uploads',
          region: 'us-east-1'
        },
        createdAt: '2024-01-18T16:45:00Z',
        userId: 1
      }
    ]
  } finally {
    loading.value = false
  }
}

const loadComponents = async () => {
  try {
    // 模拟组件数据
    components.value = [
      { id: 1, name: '用户认证服务', description: '用户认证', applicationId: 1, status: 'ACTIVE', createdAt: '2024-01-15T10:30:00Z', userId: 1 },
      { id: 2, name: '订单处理引擎', description: '订单处理', applicationId: 1, status: 'ACTIVE', createdAt: '2024-01-16T14:20:00Z', userId: 1 },
      { id: 3, name: '支付网关', description: '支付处理', applicationId: 1, status: 'ACTIVE', createdAt: '2024-01-17T09:15:00Z', userId: 1 }
    ]
  } catch (error) {
    console.error('加载组件失败:', error)
  }
}

const loadResourceTypes = async () => {
  try {
    // 模拟资源类型数据
    resourceTypes.value = Object.values(RESOURCE_TYPES)
  } catch (error) {
    console.error('加载资源类型失败:', error)
  }
}

const refreshData = () => {
  loadResources()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
}

const handleComponentFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleResourceTypeFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStatusFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleRowClick = (resource: DevOpsResource) => {
  viewResource(resource)
}

const viewComponent = (componentId: number) => {
  router.push(`/devops/components/${componentId}`)
}

const viewResource = (resource: DevOpsResource) => {
  router.push(`/devops/resources/${resource.id}`)
}

const editResource = (resource: DevOpsResource) => {
  editingResource.value = resource
  formData.value = {
    name: resource.name,
    description: resource.description || '',
    componentId: resource.componentId,
    type: resource.type,
    path: resource.path || '',
    status: resource.status || 'ACTIVE',
    configuration: { ...resource.configuration }
  }
  showEditModal.value = true
}

const deleteResource = (resource: DevOpsResource) => {
  deleteTarget.value = resource
  showDeleteDialog.value = true
}

const getComponentName = (componentId: number) => {
  const component = components.value.find(c => c.id === componentId)
  return component?.name || '未知组件'
}

const getResourceTypeLabel = (type: string) => {
  return resourceTypeConfig[type]?.label || type
}

const getResourceTypeIcon = (type: string) => {
  return resourceTypeConfig[type]?.icon || 'M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4z'
}

const getPathPlaceholder = (type: string) => {
  return resourceTypeConfig[type]?.pathPlaceholder || '请输入资源路径'
}

const getConfigFields = (type: string) => {
  return resourceTypeConfig[type]?.configFields || []
}

const canTestConnection = (type: string) => {
  return resourceTypeConfig[type]?.canTest || false
}

const handleResourceTypeChange = () => {
  // 重置配置字段
  formData.value.configuration = {}

  // 设置默认路径
  if (formData.value.type) {
    formData.value.path = getPathPlaceholder(formData.value.type)
  }
}

const testConnection = async (resource: DevOpsResource) => {
  if (!canTestConnection(resource.type)) return

  testingConnection.value = true
  try {
    // 模拟连接测试
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 随机成功或失败
    const success = Math.random() > 0.3
    if (success) {
      alert(`资源 "${resource.name}" 连接测试成功！`)
    } else {
      alert(`资源 "${resource.name}" 连接测试失败，请检查配置。`)
    }
  } catch (error) {
    alert(`连接测试失败: ${error}`)
  } finally {
    testingConnection.value = false
  }
}

const testFormConnection = async () => {
  if (!formData.value.type || !canTestConnection(formData.value.type)) return

  testingConnection.value = true
  try {
    // 模拟连接测试
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 简单验证必填字段
    const configFields = getConfigFields(formData.value.type)
    const requiredFields = configFields.filter(field => field.required)
    const missingFields = requiredFields.filter(field => !formData.value.configuration[field.key])

    if (missingFields.length > 0) {
      alert(`请填写必填字段: ${missingFields.map(f => f.label).join(', ')}`)
      return
    }

    alert('连接测试成功！')
  } catch (error) {
    alert(`连接测试失败: ${error}`)
  } finally {
    testingConnection.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  resetForm()
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    componentId: null,
    type: '',
    path: '',
    status: 'ACTIVE',
    configuration: {}
  }
  formErrors.value = {
    name: '',
    description: '',
    componentId: '',
    type: '',
    path: '',
    status: ''
  }
  editingResource.value = null
}

const validateForm = () => {
  const errors = {
    name: '',
    description: '',
    componentId: '',
    type: '',
    path: '',
    status: ''
  }
  let isValid = true

  if (!formData.value.name.trim()) {
    errors.name = '资源名称不能为空'
    isValid = false
  }

  if (!formData.value.componentId) {
    errors.componentId = '请选择所属组件'
    isValid = false
  }

  if (!formData.value.type) {
    errors.type = '请选择资源类型'
    isValid = false
  }

  formErrors.value = errors
  return isValid
}

const submitForm = async () => {
  if (!validateForm()) return

  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (showCreateModal.value) {
      // 创建资源
      const newResource: DevOpsResource = {
        id: Date.now(),
        ...formData.value,
        componentId: formData.value.componentId!,
        createdAt: new Date().toISOString(),
        userId: 1
      }
      resources.value.unshift(newResource)
    } else if (showEditModal.value && editingResource.value) {
      // 更新资源
      const index = resources.value.findIndex(r => r.id === editingResource.value!.id)
      if (index !== -1) {
        resources.value[index] = {
          ...resources.value[index],
          ...formData.value,
          componentId: formData.value.componentId!,
          updatedAt: new Date().toISOString()
        }
      }
    }

    closeModal()
  } finally {
    submitting.value = false
  }
}

const confirmDelete = async () => {
  if (!deleteTarget.value) return

  deleting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const index = resources.value.findIndex(r => r.id === deleteTarget.value!.id)
    if (index !== -1) {
      resources.value.splice(index, 1)
    }

    showDeleteDialog.value = false
    deleteTarget.value = null
  } finally {
    deleting.value = false
  }
}

// 生命周期
onMounted(() => {
  loadComponents()
  loadResourceTypes()
  loadResources()

  // 如果URL中有componentId参数，自动筛选
  const componentId = route.query.componentId
  if (componentId) {
    selectedComponentId.value = Number(componentId)
  }
})
</script>

<style scoped>
.resource-list {
  @apply p-6 bg-gray-50 min-h-screen;
}

/* 页面头部 */
.page-header {
  @apply mb-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-2xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-3;
}

/* 筛选器部分 */
.filters-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6 flex items-center gap-6;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none;
  min-width: 150px;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-outline {
  @apply bg-transparent text-blue-600 border border-blue-300 hover:bg-blue-50 focus:ring-blue-500;
}

.btn-icon {
  @apply w-4 h-4;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 组件链接 */
.component-link {
  @apply text-blue-600 hover:text-blue-800 cursor-pointer hover:underline transition-colors;
}

/* 资源类型标签 */
.resource-type-badge {
  @apply inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full;
}

.type-icon {
  @apply w-3 h-3;
}

.resource-type-badge.type-service {
  @apply bg-green-100 text-green-800;
}

.resource-type-badge.type-database {
  @apply bg-blue-100 text-blue-800;
}

.resource-type-badge.type-cache {
  @apply bg-red-100 text-red-800;
}

.resource-type-badge.type-queue {
  @apply bg-yellow-100 text-yellow-800;
}

.resource-type-badge.type-storage {
  @apply bg-purple-100 text-purple-800;
}

.resource-type-badge.type-api {
  @apply bg-indigo-100 text-indigo-800;
}

/* 资源路径 */
.resource-path {
  @apply px-2 py-1 bg-gray-100 text-gray-800 text-xs font-mono rounded border;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-1;
}

.action-btn {
  @apply p-2 rounded-md transition-colors;
}

.action-btn svg {
  @apply w-4 h-4;
}

.action-btn-view {
  @apply text-blue-600 hover:bg-blue-50;
}

.action-btn-test {
  @apply text-green-600 hover:bg-green-50;
}

.action-btn-edit {
  @apply text-orange-600 hover:bg-orange-50;
}

.action-btn-delete {
  @apply text-red-600 hover:bg-red-50;
}

/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl w-full max-w-md;
}

.modal-large {
  @apply max-w-6xl;
}

.modal-header {
  @apply flex items-center justify-between p-6 pb-4;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 p-1 rounded;
}

.modal-close svg {
  @apply w-5 h-5;
}

.modal-form {
  @apply px-6 pb-6;
}

.form-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.form-column {
  @apply space-y-4;
}

.modal-actions {
  @apply flex items-center justify-end gap-3 mt-6;
}

/* 动态配置 */
.dynamic-config {
  @apply mt-4 p-4 bg-gray-50 rounded-lg border;
}

.config-title {
  @apply text-sm font-semibold text-gray-900 mb-3 m-0;
}

.config-field {
  @apply mb-3 last:mb-0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resource-list {
    @apply p-4;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }

  .filters-section {
    @apply flex-col items-start gap-4;
  }

  .filter-group {
    @apply w-full;
  }

  .filter-select {
    @apply w-full;
  }

  .modal-container {
    @apply max-w-full mx-4;
  }

  .form-grid {
    @apply grid-cols-1;
  }

  .action-buttons {
    @apply flex-wrap gap-2;
  }

  .resource-path {
    @apply text-xs break-all;
  }
}

/* 动画效果 */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 悬停效果 */
.action-btn:hover {
  @apply transform scale-105;
}

.component-link:hover {
  @apply transform translate-x-1;
}

/* 加载动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 表单验证状态 */
.form-field.error input,
.form-field.error textarea,
.form-field.error select {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}
</style>
