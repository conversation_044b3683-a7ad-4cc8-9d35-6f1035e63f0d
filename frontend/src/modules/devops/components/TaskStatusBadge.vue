<template>
  <span :class="badgeClasses">
    <svg v-if="showIcon" class="status-icon" viewBox="0 0 20 20" fill="currentColor">
      <path :d="iconPath" />
    </svg>
    <span class="status-text">{{ statusText }}</span>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props
interface Props {
  status: string
  size?: 'sm' | 'md' | 'lg'
  showIcon?: boolean
  variant?: 'default' | 'outline' | 'solid'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showIcon: true,
  variant: 'default'
})

// 计算属性
const badgeClasses = computed(() => {
  const baseClasses = 'inline-flex items-center gap-1 rounded-full font-medium'
  
  // 尺寸样式
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  }
  
  // 状态样式
  const statusClasses = {
    // CI/CD 通用状态
    ACTIVE: getVariantClasses('green'),
    INACTIVE: getVariantClasses('gray'),
    PENDING: getVariantClasses('yellow'),
    RUNNING: getVariantClasses('blue'),
    COMPLETED: getVariantClasses('green'),
    FAILED: getVariantClasses('red'),
    CANCELLED: getVariantClasses('gray'),
    STOPPED: getVariantClasses('gray'),
    
    // CD 特有状态
    DEPLOYING: getVariantClasses('blue'),
    DEPLOYED: getVariantClasses('green'),
    ROLLBACK: getVariantClasses('orange'),
    
    // 其他状态
    SUCCESS: getVariantClasses('green'),
    ERROR: getVariantClasses('red'),
    WARNING: getVariantClasses('yellow'),
    INFO: getVariantClasses('blue')
  }
  
  return [
    baseClasses,
    sizeClasses[props.size],
    statusClasses[props.status] || statusClasses.INACTIVE
  ].join(' ')
})

const statusText = computed(() => {
  const statusLabels = {
    // CI/CD 通用状态
    ACTIVE: '活跃',
    INACTIVE: '非活跃',
    PENDING: '等待中',
    RUNNING: '运行中',
    COMPLETED: '已完成',
    FAILED: '失败',
    CANCELLED: '已取消',
    STOPPED: '已停止',
    
    // CD 特有状态
    DEPLOYING: '部署中',
    DEPLOYED: '已部署',
    ROLLBACK: '回滚中',
    
    // 其他状态
    SUCCESS: '成功',
    ERROR: '错误',
    WARNING: '警告',
    INFO: '信息'
  }
  
  return statusLabels[props.status] || props.status
})

const iconPath = computed(() => {
  const iconPaths = {
    // 成功/完成状态
    ACTIVE: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z',
    COMPLETED: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z',
    DEPLOYED: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z',
    SUCCESS: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z',
    
    // 运行中状态
    RUNNING: 'M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z',
    DEPLOYING: 'M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z',
    
    // 失败状态
    FAILED: 'M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z',
    ERROR: 'M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z',
    
    // 等待/暂停状态
    PENDING: 'M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z',
    INACTIVE: 'M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z',
    STOPPED: 'M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z',
    CANCELLED: 'M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z',
    
    // 警告状态
    WARNING: 'M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z',
    
    // 回滚状态
    ROLLBACK: 'M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z',
    
    // 信息状态
    INFO: 'M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z'
  }
  
  return iconPaths[props.status] || iconPaths.INFO
})

// 方法
function getVariantClasses(color: string) {
  const variants = {
    default: {
      green: 'bg-green-100 text-green-800',
      blue: 'bg-blue-100 text-blue-800',
      red: 'bg-red-100 text-red-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      gray: 'bg-gray-100 text-gray-800',
      orange: 'bg-orange-100 text-orange-800'
    },
    outline: {
      green: 'border border-green-300 text-green-700 bg-white',
      blue: 'border border-blue-300 text-blue-700 bg-white',
      red: 'border border-red-300 text-red-700 bg-white',
      yellow: 'border border-yellow-300 text-yellow-700 bg-white',
      gray: 'border border-gray-300 text-gray-700 bg-white',
      orange: 'border border-orange-300 text-orange-700 bg-white'
    },
    solid: {
      green: 'bg-green-600 text-white',
      blue: 'bg-blue-600 text-white',
      red: 'bg-red-600 text-white',
      yellow: 'bg-yellow-600 text-white',
      gray: 'bg-gray-600 text-white',
      orange: 'bg-orange-600 text-white'
    }
  }
  
  return variants[props.variant][color] || variants[props.variant].gray
}
</script>

<style scoped>
.status-icon {
  @apply w-3 h-3 flex-shrink-0;
}

.status-text {
  @apply whitespace-nowrap;
}

/* 动画效果 */
.status-icon {
  transition: transform 0.2s ease-in-out;
}

/* 运行中状态的脉冲动画 */
.bg-blue-100 .status-icon,
.bg-blue-600 .status-icon,
.border-blue-300 .status-icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 悬停效果 */
.inline-flex:hover .status-icon {
  transform: scale(1.1);
}
</style>
