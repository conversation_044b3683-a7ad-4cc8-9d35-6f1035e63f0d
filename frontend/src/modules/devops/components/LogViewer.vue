<template>
  <div class="log-viewer">
    <div class="viewer-header">
      <div class="header-info">
        <h3>{{ title }}</h3>
        <div class="log-stats">
          <span class="stat-item">{{ totalLines }} 行</span>
          <span class="stat-item">{{ filteredLines }} 显示</span>
          <span v-if="errorCount > 0" class="stat-item error">{{ errorCount }} 错误</span>
          <span v-if="warningCount > 0" class="stat-item warning">{{ warningCount }} 警告</span>
        </div>
      </div>
      <div class="header-actions">
        <div class="search-box">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索日志..."
            class="search-input"
          />
          <svg class="search-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="filter-controls">
          <select v-model="levelFilter" class="level-filter">
            <option value="">全部级别</option>
            <option value="ERROR">错误</option>
            <option value="WARN">警告</option>
            <option value="INFO">信息</option>
            <option value="DEBUG">调试</option>
          </select>
        </div>
        <div class="view-controls">
          <label class="control-item">
            <input
              v-model="autoScroll"
              type="checkbox"
            />
            <span>自动滚动</span>
          </label>
          <label class="control-item">
            <input
              v-model="showTimestamp"
              type="checkbox"
            />
            <span>显示时间</span>
          </label>
          <label class="control-item">
            <input
              v-model="wrapLines"
              type="checkbox"
            />
            <span>自动换行</span>
          </label>
        </div>
        <div class="action-buttons">
          <button
            @click="clearLogs"
            class="btn btn-sm btn-secondary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            清空
          </button>
          <button
            @click="downloadLogs"
            class="btn btn-sm btn-secondary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
            下载
          </button>
          <button
            @click="refreshLogs"
            :disabled="loading"
            class="btn btn-sm btn-primary"
          >
            <svg v-if="loading" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            <svg v-else class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            刷新
          </button>
        </div>
      </div>
    </div>

    <div class="viewer-body" :style="{ height: height }">
      <div
        ref="logContainer"
        class="log-container"
        :class="{ 'wrap-lines': wrapLines }"
        @scroll="handleScroll"
      >
        <div v-if="loading && logs.length === 0" class="loading-state">
          <svg class="animate-spin" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          <span>加载日志中...</span>
        </div>
        <div v-else-if="filteredLogLines.length === 0" class="empty-state">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z" />
          </svg>
          <span>{{ logs.length === 0 ? '暂无日志' : '没有匹配的日志' }}</span>
        </div>
        <div v-else class="log-lines">
          <div
            v-for="(line, index) in filteredLogLines"
            :key="index"
            :class="['log-line', `log-${line.level?.toLowerCase()}`]"
            @click="selectLine(index)"
          >
            <span v-if="showTimestamp" class="log-timestamp">{{ line.timestamp }}</span>
            <span class="log-level">{{ line.level }}</span>
            <span class="log-message">{{ line.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="viewer-footer">
      <div class="footer-info">
        <span>{{ currentPosition }} / {{ totalLines }}</span>
        <span v-if="selectedLine !== null">已选择第 {{ selectedLine + 1 }} 行</span>
      </div>
      <div class="footer-actions">
        <button
          @click="scrollToTop"
          class="btn btn-xs btn-secondary"
        >
          顶部
        </button>
        <button
          @click="scrollToBottom"
          class="btn btn-xs btn-secondary"
        >
          底部
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted, useTemplateRef } from 'vue'

// Props
interface Props {
  logs: string
  title?: string
  height?: string
  autoRefresh?: boolean
  refreshInterval?: number
  loading?: boolean
}

interface LogLine {
  timestamp?: string
  level?: string
  message: string
  raw: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '日志查看器',
  height: '400px',
  autoRefresh: false,
  refreshInterval: 5000,
  loading: false
})

// Emits
const emit = defineEmits<{
  refresh: []
  clear: []
}>()

// 响应式数据
const searchQuery = ref('')
const levelFilter = ref('')
const autoScroll = ref(true)
const showTimestamp = ref(true)
const wrapLines = ref(false)
const selectedLine = ref<number | null>(null)
const currentPosition = ref(0)
const refreshTimer = ref<number>(0)

const logContainer = useTemplateRef('logContainer')

// 计算属性
const logLines = computed<LogLine[]>(() => {
  if (!props.logs) return []
  
  return props.logs.split('\n')
    .filter(line => line.trim())
    .map(line => parseLine(line))
})

const filteredLogLines = computed(() => {
  let filtered = logLines.value

  // 级别过滤
  if (levelFilter.value) {
    filtered = filtered.filter(line => line.level === levelFilter.value)
  }

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(line => 
      line.message.toLowerCase().includes(query) ||
      line.raw.toLowerCase().includes(query)
    )
  }

  return filtered
})

const totalLines = computed(() => logLines.value.length)
const filteredLines = computed(() => filteredLogLines.value.length)

const errorCount = computed(() => 
  logLines.value.filter(line => line.level === 'ERROR').length
)

const warningCount = computed(() => 
  logLines.value.filter(line => line.level === 'WARN').length
)

// 方法
const parseLine = (line: string): LogLine => {
  // 解析日志行格式：时间戳 级别 消息
  const timestampMatch = line.match(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z?)/)
  const levelMatch = line.match(/\s(ERROR|WARN|INFO|DEBUG)\s/)
  
  let timestamp = ''
  let level = ''
  let message = line

  if (timestampMatch) {
    timestamp = timestampMatch[1]
    message = line.substring(timestampMatch[0].length).trim()
  }

  if (levelMatch) {
    level = levelMatch[1]
    message = message.replace(levelMatch[0], ' ').trim()
  }

  return {
    timestamp,
    level,
    message,
    raw: line
  }
}

const handleScroll = () => {
  if (logContainer.value) {
    const container = logContainer.value
    const scrollTop = container.scrollTop
    const scrollHeight = container.scrollHeight
    const clientHeight = container.clientHeight
    
    // 计算当前位置
    const lineHeight = 24 // 假设每行高度为24px
    currentPosition.value = Math.floor(scrollTop / lineHeight) + 1
    
    // 如果滚动到底部，禁用自动滚动
    if (scrollTop + clientHeight >= scrollHeight - 10) {
      // 接近底部时保持自动滚动
    } else {
      // 用户手动滚动时暂时禁用自动滚动
      if (autoScroll.value) {
        setTimeout(() => {
          autoScroll.value = true
        }, 5000)
      }
    }
  }
}

const selectLine = (index: number) => {
  selectedLine.value = index
}

const scrollToTop = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = 0
  }
}

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight
  }
}

const clearLogs = () => {
  emit('clear')
  selectedLine.value = null
}

const downloadLogs = () => {
  const blob = new Blob([props.logs], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `logs-${new Date().toISOString().slice(0, 19)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const refreshLogs = () => {
  emit('refresh')
}

const startAutoRefresh = () => {
  if (props.autoRefresh && props.refreshInterval > 0) {
    refreshTimer.value = window.setInterval(() => {
      refreshLogs()
    }, props.refreshInterval)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = undefined
  }
}

// 监听器
watch(() => props.logs, () => {
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
})

watch(() => props.autoRefresh, (enabled) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

// 生命周期
onMounted(() => {
  if (props.autoRefresh) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.log-viewer {
  @apply border border-gray-300 rounded-lg overflow-hidden bg-white;
}

/* 查看器头部 */
.viewer-header {
  @apply flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200;
}

.header-info h3 {
  @apply text-lg font-medium text-gray-900 m-0 mb-2;
}

.log-stats {
  @apply flex items-center gap-4 text-sm;
}

.stat-item {
  @apply text-gray-600;
}

.stat-item.error {
  @apply text-red-600 font-medium;
}

.stat-item.warning {
  @apply text-yellow-600 font-medium;
}

.header-actions {
  @apply flex items-center gap-4;
}

/* 搜索框 */
.search-box {
  @apply relative;
}

.search-input {
  @apply pl-8 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.search-icon {
  @apply absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400;
}

/* 过滤控制 */
.filter-controls {
  @apply flex items-center gap-2;
}

.level-filter {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

/* 视图控制 */
.view-controls {
  @apply flex items-center gap-4;
}

.control-item {
  @apply flex items-center gap-2 cursor-pointer text-sm;
}

.control-item input[type="checkbox"] {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-2;
}

/* 查看器主体 */
.viewer-body {
  @apply relative;
}

.log-container {
  @apply h-full overflow-auto bg-gray-900 text-gray-100 font-mono text-sm;
}

.log-container.wrap-lines {
  @apply whitespace-pre-wrap;
}

.log-container:not(.wrap-lines) {
  @apply whitespace-pre;
}

/* 状态显示 */
.loading-state,
.empty-state {
  @apply flex items-center justify-center h-full text-gray-400 gap-2;
}

.loading-state svg,
.empty-state svg {
  @apply w-5 h-5;
}

/* 日志行 */
.log-lines {
  @apply p-4;
}

.log-line {
  @apply flex items-start gap-3 py-1 hover:bg-gray-800 cursor-pointer transition-colors;
}

.log-line:hover {
  @apply bg-gray-800;
}

.log-timestamp {
  @apply text-gray-400 text-xs flex-shrink-0 w-20;
}

.log-level {
  @apply text-xs font-bold flex-shrink-0 w-12;
}

.log-message {
  @apply flex-1 break-words;
}

/* 日志级别样式 */
.log-info .log-level {
  @apply text-blue-400;
}

.log-warn .log-level {
  @apply text-yellow-400;
}

.log-error .log-level {
  @apply text-red-400;
}

.log-debug .log-level {
  @apply text-gray-400;
}

/* 查看器底部 */
.viewer-footer {
  @apply flex items-center justify-between p-2 bg-gray-50 border-t border-gray-200 text-sm;
}

.footer-info {
  @apply flex items-center gap-4 text-gray-600;
}

.footer-actions {
  @apply flex items-center gap-2;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-3 py-1.5 rounded-md font-medium transition-colors;
}

.btn-xs {
  @apply px-2 py-1 text-xs;
}

.btn-sm {
  @apply px-2 py-1 text-sm;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-icon {
  @apply w-4 h-4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
