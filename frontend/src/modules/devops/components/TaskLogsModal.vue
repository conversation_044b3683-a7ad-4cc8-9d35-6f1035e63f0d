<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3>任务日志 - {{ taskName }}</h3>
        <div class="header-actions">
          <button @click="refreshLogs" class="refresh-btn" :disabled="loading">
            <svg viewBox="0 0 20 20" fill="currentColor" :class="{ 'animate-spin': loading }">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            刷新
          </button>
          <button @click="$emit('close')" class="close-btn">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      <div class="modal-body">
        <div class="logs-container">
          <div v-if="loading && !logs" class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载日志中...</p>
          </div>
          
          <div v-else-if="error" class="error-state">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <p>{{ error }}</p>
            <button @click="refreshLogs" class="retry-btn">重试</button>
          </div>
          
          <div v-else-if="!logs || logs.length === 0" class="empty-state">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clip-rule="evenodd" />
            </svg>
            <p>暂无日志数据</p>
          </div>
          
          <div v-else class="logs-content">
            <div class="logs-header">
              <div class="logs-info">
                <span class="log-count">共 {{ logs.length }} 条日志</span>
                <span class="last-update">最后更新: {{ formatTime(lastUpdate) }}</span>
              </div>
              <div class="logs-actions">
                <button @click="scrollToTop" class="scroll-btn">
                  <svg viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L10 4.414 4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                  顶部
                </button>
                <button @click="scrollToBottom" class="scroll-btn">
                  <svg viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L10 15.586l5.293-5.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  底部
                </button>
              </div>
            </div>
            
            <div ref="logsScrollContainer" class="logs-scroll-container">
              <div class="logs-list">
                <div
                  v-for="(log, index) in logs"
                  :key="index"
                  :class="['log-entry', `log-${log.level?.toLowerCase() || 'info'}`]"
                >
                  <div class="log-timestamp">{{ formatTime(log.timestamp) }}</div>
                  <div class="log-level">{{ log.level || 'INFO' }}</div>
                  <div class="log-message">{{ log.message }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button @click="$emit('close')" class="btn btn-secondary">关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'

interface LogEntry {
  timestamp: string
  level: string
  message: string
}

interface Props {
  visible: boolean
  taskName?: string
  logs?: LogEntry[]
  loading?: boolean
  error?: string | null
}

interface Emits {
  (e: 'close'): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  taskName: '未知任务',
  logs: () => [],
  loading: false,
  error: null
})

const emit = defineEmits<Emits>()

const logsScrollContainer = ref<HTMLElement>()
const lastUpdate = ref(new Date())

watch(() => props.logs, () => {
  lastUpdate.value = new Date()
  nextTick(() => {
    scrollToBottom()
  })
})

const refreshLogs = () => {
  emit('refresh')
}

const handleOverlayClick = () => {
  emit('close')
}

const scrollToTop = () => {
  if (logsScrollContainer.value) {
    logsScrollContainer.value.scrollTop = 0
  }
}

const scrollToBottom = () => {
  if (logsScrollContainer.value) {
    logsScrollContainer.value.scrollTop = logsScrollContainer.value.scrollHeight
  }
}

const formatTime = (date: Date | string) => {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900;
}

.header-actions {
  @apply flex items-center space-x-2;
}

.refresh-btn {
  @apply flex items-center space-x-2 px-3 py-2 text-sm bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors disabled:opacity-50;
}

.refresh-btn svg {
  @apply w-4 h-4;
}

.close-btn {
  @apply p-2 hover:bg-gray-100 rounded-lg transition-colors;
}

.close-btn svg {
  @apply w-5 h-5 text-gray-500;
}

.modal-body {
  @apply flex-1 overflow-hidden;
}

.logs-container {
  @apply h-full flex flex-col;
}

.loading-state,
.error-state,
.empty-state {
  @apply flex flex-col items-center justify-center h-96 text-gray-500;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-4;
}

.error-state svg,
.empty-state svg {
  @apply w-12 h-12 mb-4 text-gray-400;
}

.retry-btn {
  @apply mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors;
}

.logs-content {
  @apply flex flex-col h-full;
}

.logs-header {
  @apply flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200;
}

.logs-info {
  @apply flex items-center space-x-4 text-sm text-gray-600;
}

.logs-actions {
  @apply flex items-center space-x-2;
}

.scroll-btn {
  @apply flex items-center space-x-1 px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.scroll-btn svg {
  @apply w-3 h-3;
}

.logs-scroll-container {
  @apply flex-1 overflow-y-auto;
}

.logs-list {
  @apply font-mono text-sm;
}

.log-entry {
  @apply flex items-start space-x-3 px-4 py-2 border-b border-gray-100 hover:bg-gray-50;
}

.log-timestamp {
  @apply text-gray-500 whitespace-nowrap;
}

.log-level {
  @apply font-medium whitespace-nowrap;
}

.log-message {
  @apply flex-1 break-words;
}

.log-info .log-level {
  @apply text-blue-600;
}

.log-warn .log-level {
  @apply text-yellow-600;
}

.log-error .log-level {
  @apply text-red-600;
}

.log-debug .log-level {
  @apply text-gray-600;
}

.modal-footer {
  @apply flex justify-end p-6 border-t border-gray-200;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}
</style>
