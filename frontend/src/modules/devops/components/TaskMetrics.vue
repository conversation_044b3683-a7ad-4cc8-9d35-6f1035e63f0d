<template>
  <div class="task-metrics">
    <div class="metrics-header">
      <h3>{{ title }}</h3>
      <div class="time-range-selector">
        <select v-model="selectedTimeRange" @change="handleTimeRangeChange" class="time-select">
          <option value="1h">最近1小时</option>
          <option value="24h">最近24小时</option>
          <option value="7d">最近7天</option>
          <option value="30d">最近30天</option>
        </select>
        <button @click="refreshMetrics" :disabled="loading" class="btn btn-sm btn-secondary">
          <svg v-if="loading" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          <svg v-else class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
      </div>
    </div>

    <div class="metrics-content">
      <!-- 概览指标 -->
      <div class="overview-metrics">
        <div class="metric-card success-rate">
          <div class="metric-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.successRate }}%</div>
            <div class="metric-label">成功率</div>
            <div class="metric-change" :class="getChangeClass(metrics.successRateChange)">
              {{ formatChange(metrics.successRateChange) }}
            </div>
          </div>
        </div>

        <div class="metric-card avg-duration">
          <div class="metric-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatDuration(metrics.avgDuration) }}</div>
            <div class="metric-label">平均耗时</div>
            <div class="metric-change" :class="getChangeClass(-metrics.avgDurationChange)">
              {{ formatChange(metrics.avgDurationChange, 's') }}
            </div>
          </div>
        </div>

        <div class="metric-card total-executions">
          <div class="metric-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.totalExecutions }}</div>
            <div class="metric-label">总执行次数</div>
            <div class="metric-change" :class="getChangeClass(metrics.totalExecutionsChange)">
              {{ formatChange(metrics.totalExecutionsChange) }}
            </div>
          </div>
        </div>

        <div class="metric-card failure-rate">
          <div class="metric-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.failureRate }}%</div>
            <div class="metric-label">失败率</div>
            <div class="metric-change" :class="getChangeClass(-metrics.failureRateChange)">
              {{ formatChange(metrics.failureRateChange) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 趋势图表 -->
      <div class="trend-charts">
        <div class="chart-container">
          <h4>执行趋势</h4>
          <div class="chart-placeholder">
            <svg class="chart-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
            </svg>
            <p>执行次数随时间变化趋势</p>
            <div class="chart-data">
              <div class="data-point" v-for="point in trendData" :key="point.time">
                <div class="point-bar" :style="{ height: `${point.value}%` }"></div>
                <div class="point-label">{{ point.label }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="chart-container">
          <h4>成功率趋势</h4>
          <div class="chart-placeholder">
            <svg class="chart-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <p>成功率随时间变化趋势</p>
            <div class="success-rate-line">
              <div class="rate-point" v-for="point in successRateData" :key="point.time">
                <div class="point-dot" :style="{ bottom: `${point.rate}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细统计 -->
      <div class="detailed-stats">
        <div class="stats-section">
          <h4>执行状态分布</h4>
          <div class="status-distribution">
            <div class="status-item" v-for="status in statusDistribution" :key="status.name">
              <div class="status-bar">
                <div 
                  class="status-fill" 
                  :class="`status-${status.name.toLowerCase()}`"
                  :style="{ width: `${status.percentage}%` }"
                ></div>
              </div>
              <div class="status-info">
                <span class="status-name">{{ status.label }}</span>
                <span class="status-count">{{ status.count }}</span>
                <span class="status-percentage">{{ status.percentage }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="stats-section">
          <h4>耗时分布</h4>
          <div class="duration-stats">
            <div class="duration-item">
              <span class="duration-label">最短耗时</span>
              <span class="duration-value">{{ formatDuration(metrics.minDuration) }}</span>
            </div>
            <div class="duration-item">
              <span class="duration-label">最长耗时</span>
              <span class="duration-value">{{ formatDuration(metrics.maxDuration) }}</span>
            </div>
            <div class="duration-item">
              <span class="duration-label">中位数</span>
              <span class="duration-value">{{ formatDuration(metrics.medianDuration) }}</span>
            </div>
            <div class="duration-item">
              <span class="duration-label">95分位数</span>
              <span class="duration-value">{{ formatDuration(metrics.p95Duration) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Props
interface Props {
  title?: string
  taskId?: number
  taskType?: 'ci' | 'cd'
}

interface MetricsData {
  successRate: number
  successRateChange: number
  avgDuration: number
  avgDurationChange: number
  totalExecutions: number
  totalExecutionsChange: number
  failureRate: number
  failureRateChange: number
  minDuration: number
  maxDuration: number
  medianDuration: number
  p95Duration: number
}

interface TrendPoint {
  time: string
  value: number
  label: string
}

interface StatusDistribution {
  name: string
  label: string
  count: number
  percentage: number
}

const props = withDefaults(defineProps<Props>(), {
  title: '任务指标',
  taskType: 'ci'
})

// Emits
const emit = defineEmits<{
  refresh: [timeRange: string]
}>()

// 响应式数据
const loading = ref(false)
const selectedTimeRange = ref('24h')

const metrics = ref<MetricsData>({
  successRate: 85,
  successRateChange: 5,
  avgDuration: 180,
  avgDurationChange: -15,
  totalExecutions: 156,
  totalExecutionsChange: 23,
  failureRate: 15,
  failureRateChange: -5,
  minDuration: 45,
  maxDuration: 420,
  medianDuration: 165,
  p95Duration: 350
})

const trendData = ref<TrendPoint[]>([
  { time: '00:00', value: 60, label: '00:00' },
  { time: '04:00', value: 40, label: '04:00' },
  { time: '08:00', value: 80, label: '08:00' },
  { time: '12:00', value: 100, label: '12:00' },
  { time: '16:00', value: 75, label: '16:00' },
  { time: '20:00', value: 90, label: '20:00' }
])

const successRateData = ref([
  { time: '00:00', rate: 85 },
  { time: '04:00', rate: 90 },
  { time: '08:00', rate: 82 },
  { time: '12:00', rate: 88 },
  { time: '16:00', rate: 85 },
  { time: '20:00', rate: 87 }
])

const statusDistribution = ref<StatusDistribution[]>([
  { name: 'COMPLETED', label: '成功', count: 132, percentage: 85 },
  { name: 'FAILED', label: '失败', count: 18, percentage: 12 },
  { name: 'CANCELLED', label: '取消', count: 6, percentage: 3 }
])

// 方法
const handleTimeRangeChange = () => {
  loadMetrics()
}

const refreshMetrics = () => {
  emit('refresh', selectedTimeRange.value)
  loadMetrics()
}

const loadMetrics = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 根据时间范围更新数据
    // 这里应该调用实际的API
  } finally {
    loading.value = false
  }
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }
}

const formatChange = (change: number, unit = '') => {
  const sign = change > 0 ? '+' : ''
  return `${sign}${change}${unit}`
}

const getChangeClass = (change: number) => {
  if (change > 0) return 'positive'
  if (change < 0) return 'negative'
  return 'neutral'
}

// 生命周期
onMounted(() => {
  loadMetrics()
})
</script>

<style scoped>
.task-metrics {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

/* 指标头部 */
.metrics-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.metrics-header h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.time-range-selector {
  @apply flex items-center gap-3;
}

.time-select {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

/* 指标内容 */
.metrics-content {
  @apply p-6 space-y-8;
}

/* 概览指标 */
.overview-metrics {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.metric-card {
  @apply flex items-center gap-4 p-4 rounded-lg border;
}

.success-rate {
  @apply border-green-200 bg-green-50;
}

.avg-duration {
  @apply border-blue-200 bg-blue-50;
}

.total-executions {
  @apply border-purple-200 bg-purple-50;
}

.failure-rate {
  @apply border-red-200 bg-red-50;
}

.metric-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0;
}

.success-rate .metric-icon {
  @apply bg-green-100 text-green-600;
}

.avg-duration .metric-icon {
  @apply bg-blue-100 text-blue-600;
}

.total-executions .metric-icon {
  @apply bg-purple-100 text-purple-600;
}

.failure-rate .metric-icon {
  @apply bg-red-100 text-red-600;
}

.metric-icon svg {
  @apply w-6 h-6;
}

.metric-content {
  @apply flex-1;
}

.metric-value {
  @apply text-2xl font-bold text-gray-900 mb-1;
}

.metric-label {
  @apply text-sm font-medium text-gray-600 mb-1;
}

.metric-change {
  @apply text-xs font-medium;
}

.metric-change.positive {
  @apply text-green-600;
}

.metric-change.negative {
  @apply text-red-600;
}

.metric-change.neutral {
  @apply text-gray-600;
}

/* 趋势图表 */
.trend-charts {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.chart-container {
  @apply border border-gray-200 rounded-lg p-4;
}

.chart-container h4 {
  @apply text-base font-medium text-gray-900 mb-4;
}

.chart-placeholder {
  @apply flex flex-col items-center justify-center h-48 text-gray-500;
}

.chart-icon {
  @apply w-8 h-8 mb-2;
}

.chart-placeholder p {
  @apply text-sm mb-4;
}

.chart-data {
  @apply flex items-end justify-between w-full h-20 gap-2;
}

.data-point {
  @apply flex flex-col items-center flex-1;
}

.point-bar {
  @apply w-full bg-blue-500 rounded-t min-h-[4px];
}

.point-label {
  @apply text-xs text-gray-500 mt-1;
}

.success-rate-line {
  @apply relative w-full h-20 border-b border-gray-200;
}

.rate-point {
  @apply absolute w-full;
}

.point-dot {
  @apply absolute w-2 h-2 bg-green-500 rounded-full;
}

/* 详细统计 */
.detailed-stats {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.stats-section {
  @apply border border-gray-200 rounded-lg p-4;
}

.stats-section h4 {
  @apply text-base font-medium text-gray-900 mb-4;
}

/* 状态分布 */
.status-distribution {
  @apply space-y-3;
}

.status-item {
  @apply space-y-2;
}

.status-bar {
  @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;
}

.status-fill {
  @apply h-full transition-all duration-300;
}

.status-completed {
  @apply bg-green-500;
}

.status-failed {
  @apply bg-red-500;
}

.status-cancelled {
  @apply bg-gray-500;
}

.status-info {
  @apply flex items-center justify-between text-sm;
}

.status-name {
  @apply font-medium text-gray-900;
}

.status-count {
  @apply text-gray-600;
}

.status-percentage {
  @apply font-medium text-gray-900;
}

/* 耗时分布 */
.duration-stats {
  @apply space-y-3;
}

.duration-item {
  @apply flex items-center justify-between;
}

.duration-label {
  @apply text-sm text-gray-600;
}

.duration-value {
  @apply text-sm font-medium text-gray-900;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-3 py-1.5 rounded-md font-medium transition-colors;
}

.btn-sm {
  @apply px-2 py-1 text-sm;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-icon {
  @apply w-4 h-4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
