<template>
  <div class="task-config-editor">
    <div class="editor-header">
      <div class="header-info">
        <h3>{{ title }}</h3>
        <p v-if="description" class="description">{{ description }}</p>
      </div>
      <div class="header-actions">
        <button
          v-if="showTemplateSelector"
          @click="showTemplates = !showTemplates"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" />
          </svg>
          模板
        </button>
        <button
          @click="validateConfig"
          :disabled="validating"
          class="btn btn-secondary"
        >
          <svg v-if="validating" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          <svg v-else class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          {{ validating ? '验证中...' : '验证' }}
        </button>
        <button
          @click="formatConfig"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
          </svg>
          格式化
        </button>
      </div>
    </div>

    <!-- 模板选择器 -->
    <div v-if="showTemplates" class="template-selector">
      <div class="template-header">
        <h4>选择模板</h4>
        <button @click="showTemplates = false" class="close-button">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
      <div class="template-list">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-item"
          @click="applyTemplate(template)"
        >
          <div class="template-info">
            <div class="template-name">{{ template.name }}</div>
            <div class="template-description">{{ template.description }}</div>
          </div>
          <div class="template-actions">
            <button class="btn btn-sm btn-primary">使用</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div class="editor-body">
      <div class="editor-container" :style="{ height: height }">
        <textarea
          ref="editorTextarea"
          v-model="internalValue"
          :placeholder="placeholder"
          class="config-textarea"
          @input="handleInput"
          @keydown="handleKeydown"
          @scroll="handleScroll"
        />
        <div class="line-numbers" ref="lineNumbers">
          <div
            v-for="n in lineCount"
            :key="n"
            class="line-number"
          >
            {{ n }}
          </div>
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="editor-footer">
      <div class="status-info">
        <span class="status-item">行: {{ currentLine }}</span>
        <span class="status-item">列: {{ currentColumn }}</span>
        <span class="status-item">字符: {{ characterCount }}</span>
        <span v-if="validationStatus" :class="['status-item', validationStatus.type]">
          {{ validationStatus.message }}
        </span>
      </div>
      <div class="editor-actions">
        <button
          v-if="showFullscreen"
          @click="toggleFullscreen"
          class="btn btn-sm btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          全屏
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'

// Props
interface Props {
  modelValue: string
  title?: string
  description?: string
  placeholder?: string
  height?: string
  showTemplateSelector?: boolean
  showFullscreen?: boolean
  templates?: ConfigTemplate[]
  language?: 'yaml' | 'json' | 'shell' | 'dockerfile'
}

interface ConfigTemplate {
  id: string
  name: string
  description: string
  content: string
  language?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '配置编辑器',
  placeholder: '请输入配置内容...',
  height: '400px',
  showTemplateSelector: true,
  showFullscreen: true,
  templates: () => [],
  language: 'yaml'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
  validate: [isValid: boolean, errors: string[]]
}>()

// 响应式数据
const internalValue = ref(props.modelValue)
const showTemplates = ref(false)
const validating = ref(false)
const isFullscreen = ref(false)
const currentLine = ref(1)
const currentColumn = ref(1)
const validationStatus = ref<{ type: 'success' | 'error' | 'warning', message: string } | null>(null)

const editorTextarea = ref<HTMLTextAreaElement>()
const lineNumbers = ref<HTMLElement>()

// 计算属性
const lineCount = computed(() => {
  return internalValue.value.split('\n').length
})

const characterCount = computed(() => {
  return internalValue.value.length
})

// 方法
const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  internalValue.value = target.value
  emit('update:modelValue', target.value)
  updateCursorPosition()
  clearValidationStatus()
}

const handleKeydown = (event: KeyboardEvent) => {
  const textarea = event.target as HTMLTextAreaElement
  
  // Tab 键处理
  if (event.key === 'Tab') {
    event.preventDefault()
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const spaces = '  ' // 2个空格作为缩进
    
    textarea.value = textarea.value.substring(0, start) + spaces + textarea.value.substring(end)
    textarea.selectionStart = textarea.selectionEnd = start + spaces.length
    
    internalValue.value = textarea.value
    emit('update:modelValue', textarea.value)
  }
  
  // 自动括号匹配
  if (event.key === '{' || event.key === '[' || event.key === '(') {
    const closingBracket = { '{': '}', '[': ']', '(': ')' }[event.key]
    if (closingBracket) {
      event.preventDefault()
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      
      textarea.value = textarea.value.substring(0, start) + event.key + closingBracket + textarea.value.substring(end)
      textarea.selectionStart = textarea.selectionEnd = start + 1
      
      internalValue.value = textarea.value
      emit('update:modelValue', textarea.value)
    }
  }
  
  updateCursorPosition()
}

const handleScroll = () => {
  if (lineNumbers.value && editorTextarea.value) {
    lineNumbers.value.scrollTop = editorTextarea.value.scrollTop
  }
}

const updateCursorPosition = () => {
  nextTick(() => {
    if (editorTextarea.value) {
      const textarea = editorTextarea.value
      const text = textarea.value.substring(0, textarea.selectionStart)
      const lines = text.split('\n')
      currentLine.value = lines.length
      currentColumn.value = lines[lines.length - 1].length + 1
    }
  })
}

const validateConfig = async () => {
  validating.value = true
  validationStatus.value = null
  
  try {
    // 模拟验证过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const errors: string[] = []
    
    // 基本格式验证
    if (!internalValue.value.trim()) {
      errors.push('配置内容不能为空')
    }
    
    // YAML 格式验证（简单版本）
    if (props.language === 'yaml') {
      const lines = internalValue.value.split('\n')
      lines.forEach((line, index) => {
        if (line.trim() && !line.match(/^[\s]*[a-zA-Z_][a-zA-Z0-9_]*\s*:/) && !line.match(/^[\s]*-/)) {
          if (!line.startsWith(' ') && !line.startsWith('#')) {
            errors.push(`第 ${index + 1} 行格式可能有误`)
          }
        }
      })
    }
    
    const isValid = errors.length === 0
    
    if (isValid) {
      validationStatus.value = { type: 'success', message: '配置验证通过' }
    } else {
      validationStatus.value = { type: 'error', message: `发现 ${errors.length} 个错误` }
    }
    
    emit('validate', isValid, errors)
  } catch (error) {
    validationStatus.value = { type: 'error', message: '验证过程出错' }
    emit('validate', false, ['验证过程出错'])
  } finally {
    validating.value = false
  }
}

const formatConfig = () => {
  try {
    if (props.language === 'json') {
      const parsed = JSON.parse(internalValue.value)
      internalValue.value = JSON.stringify(parsed, null, 2)
    } else if (props.language === 'yaml') {
      // 简单的 YAML 格式化
      const lines = internalValue.value.split('\n')
      const formatted = lines.map(line => {
        const trimmed = line.trim()
        if (!trimmed || trimmed.startsWith('#')) return line
        
        // 简单的缩进处理
        const indentLevel = (line.length - line.trimStart().length) / 2
        return '  '.repeat(indentLevel) + trimmed
      })
      internalValue.value = formatted.join('\n')
    }
    
    emit('update:modelValue', internalValue.value)
    validationStatus.value = { type: 'success', message: '格式化完成' }
  } catch (error) {
    validationStatus.value = { type: 'error', message: '格式化失败' }
  }
}

const applyTemplate = (template: ConfigTemplate) => {
  internalValue.value = template.content
  emit('update:modelValue', template.content)
  showTemplates.value = false
  validationStatus.value = { type: 'success', message: `已应用模板: ${template.name}` }
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  // 这里可以添加全屏逻辑
}

const clearValidationStatus = () => {
  if (validationStatus.value) {
    setTimeout(() => {
      validationStatus.value = null
    }, 3000)
  }
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue !== internalValue.value) {
    internalValue.value = newValue
  }
})

// 生命周期
onMounted(() => {
  updateCursorPosition()
})
</script>

<style scoped>
.task-config-editor {
  @apply border border-gray-300 rounded-lg overflow-hidden bg-white;
}

/* 编辑器头部 */
.editor-header {
  @apply flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200;
}

.header-info h3 {
  @apply text-lg font-medium text-gray-900 m-0;
}

.description {
  @apply text-sm text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-2;
}

/* 模板选择器 */
.template-selector {
  @apply border-b border-gray-200 bg-gray-50;
}

.template-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.template-header h4 {
  @apply text-base font-medium text-gray-900 m-0;
}

.close-button {
  @apply p-1 text-gray-400 hover:text-gray-600 transition-colors;
}

.close-button svg {
  @apply w-4 h-4;
}

.template-list {
  @apply max-h-60 overflow-y-auto;
}

.template-item {
  @apply flex items-center justify-between p-4 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0;
}

.template-info {
  @apply flex-1;
}

.template-name {
  @apply font-medium text-gray-900;
}

.template-description {
  @apply text-sm text-gray-600 mt-1;
}

/* 编辑器主体 */
.editor-body {
  @apply relative;
}

.editor-container {
  @apply relative flex;
}

.line-numbers {
  @apply w-12 bg-gray-50 border-r border-gray-200 text-right text-sm text-gray-500 font-mono overflow-hidden;
  padding: 12px 8px;
  line-height: 1.5;
}

.line-number {
  @apply block;
  height: 1.5em;
}

.config-textarea {
  @apply flex-1 p-3 font-mono text-sm border-0 resize-none outline-none;
  line-height: 1.5;
  tab-size: 2;
}

.config-textarea:focus {
  @apply ring-0;
}

/* 编辑器底部 */
.editor-footer {
  @apply flex items-center justify-between p-2 bg-gray-50 border-t border-gray-200 text-sm;
}

.status-info {
  @apply flex items-center gap-4;
}

.status-item {
  @apply text-gray-600;
}

.status-item.success {
  @apply text-green-600;
}

.status-item.error {
  @apply text-red-600;
}

.status-item.warning {
  @apply text-yellow-600;
}

.editor-actions {
  @apply flex items-center gap-2;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-3 py-1.5 rounded-md font-medium transition-colors;
}

.btn-sm {
  @apply px-2 py-1 text-sm;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-icon {
  @apply w-4 h-4;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
