<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3>{{ isEdit ? '编辑CD任务' : '创建CD任务' }}</h3>
        <button @click="$emit('close')" class="close-btn">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <div class="form-grid">
            <!-- 基本信息 -->
            <div class="form-section">
              <h4>基本信息</h4>
              <div class="form-group">
                <label>任务名称 *</label>
                <input
                  v-model="formData.name"
                  type="text"
                  required
                  placeholder="请输入任务名称"
                />
              </div>
              <div class="form-group">
                <label>描述</label>
                <textarea
                  v-model="formData.description"
                  placeholder="请输入任务描述"
                  rows="3"
                ></textarea>
              </div>
              <div class="form-group">
                <label>应用ID *</label>
                <input
                  v-model.number="formData.applicationId"
                  type="number"
                  required
                  placeholder="请输入应用ID"
                />
              </div>
            </div>

            <!-- 部署配置 -->
            <div class="form-section">
              <h4>部署配置</h4>
              <div class="form-group">
                <label>部署策略 *</label>
                <select v-model="formData.deploymentStrategy" required>
                  <option value="">请选择部署策略</option>
                  <option value="rolling">滚动更新</option>
                  <option value="blue-green">蓝绿部署</option>
                  <option value="canary">金丝雀部署</option>
                </select>
              </div>
              <div class="form-group">
                <label>目标环境 *</label>
                <select v-model="formData.configuration.targetEnvironment" required>
                  <option value="">请选择环境</option>
                  <option value="development">开发环境</option>
                  <option value="staging">测试环境</option>
                  <option value="production">生产环境</option>
                </select>
              </div>
              <div class="form-group">
                <label>命名空间</label>
                <input
                  v-model="formData.configuration.namespace"
                  type="text"
                  placeholder="请输入命名空间"
                />
              </div>
              <div class="form-group">
                <label>副本数量</label>
                <input
                  v-model.number="formData.configuration.replicas"
                  type="number"
                  min="1"
                  placeholder="1"
                />
              </div>
            </div>
          </div>

          <div class="modal-actions">
            <button type="button" @click="$emit('close')" class="btn btn-secondary">
              取消
            </button>
            <button type="submit" class="btn btn-primary" :disabled="loading">
              {{ loading ? '保存中...' : (isEdit ? '更新' : '创建') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { DevOpsCdTask } from '@/modules/devops/types/devops'

interface Props {
  visible: boolean
  task?: DevOpsCdTask | null
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  task: null,
  loading: false
})

const emit = defineEmits<Emits>()

const isEdit = computed(() => !!props.task)

const formData = ref({
  name: '',
  description: '',
  applicationId: 0,
  deploymentStrategy: '',
  componentVersions: {},
  configuration: {
    targetEnvironment: '',
    namespace: '',
    replicas: 1,
    timeout: 300,
    autoRollback: true,
    rollbackTimeout: 60,
    maxRollbackAttempts: 3,
    healthCheckEnabled: true,
    healthCheckEndpoint: '/health',
    healthCheckTimeout: 30,
    healthCheckRetries: 3,
    notifyOnSuccess: false,
    notifyOnFailure: true,
    notifyOnRollback: true,
    notificationChannels: []
  }
})

watch(() => props.task, (newTask) => {
  if (newTask) {
    formData.value = {
      name: newTask.name,
      description: newTask.description || '',
      applicationId: newTask.applicationId,
      deploymentStrategy: newTask.deploymentStrategy,
      componentVersions: newTask.componentVersions || {},
      configuration: { ...newTask.configuration }
    }
  } else {
    resetForm()
  }
}, { immediate: true })

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    applicationId: 0,
    deploymentStrategy: '',
    componentVersions: {},
    configuration: {
      targetEnvironment: '',
      namespace: '',
      replicas: 1,
      timeout: 300,
      autoRollback: true,
      rollbackTimeout: 60,
      maxRollbackAttempts: 3,
      healthCheckEnabled: true,
      healthCheckEndpoint: '/health',
      healthCheckTimeout: 30,
      healthCheckRetries: 3,
      notifyOnSuccess: false,
      notifyOnFailure: true,
      notifyOnRollback: true,
      notificationChannels: []
    }
  }
}

const handleSubmit = () => {
  emit('submit', formData.value)
}

const handleOverlayClick = () => {
  emit('close')
}
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900;
}

.close-btn {
  @apply p-2 hover:bg-gray-100 rounded-lg transition-colors;
}

.close-btn svg {
  @apply w-5 h-5 text-gray-500;
}

.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-120px)];
}

.form-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.form-section {
  @apply space-y-4;
}

.form-section h4 {
  @apply text-base font-medium text-gray-900 border-b border-gray-200 pb-2;
}

.form-group {
  @apply space-y-2;
}

.form-group label {
  @apply block text-sm font-medium text-gray-700;
}

.form-group input,
.form-group select,
.form-group textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.modal-actions {
  @apply flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}
</style>
