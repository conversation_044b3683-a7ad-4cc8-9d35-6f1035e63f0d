/**
 * CI/CD 相关工具函数
 */

import type { DevOpsCiTask, DevOpsCdTask, DevOpsCiTaskInstance, DevOpsCdTaskInstance } from '@/modules/devops/types/devops'

// 任务状态相关
export const TASK_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  RUNNING: 'RUNNING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  STOPPED: 'STOPPED',
  DEPLOYING: 'DEPLOYING',
  DEPLOYED: 'DEPLOYED',
  ROLLBACK: 'ROLLBACK'
} as const

export type TaskStatus = typeof TASK_STATUS[keyof typeof TASK_STATUS]

// 任务类型相关
export const CI_TASK_TYPES = {
  BUILD: 'build',
  TEST: 'test',
  LINT: 'lint',
  SECURITY_SCAN: 'security_scan',
  CUSTOM: 'custom'
} as const

export const CD_DEPLOYMENT_STRATEGIES = {
  ROLLING_UPDATE: 'rolling_update',
  BLUE_GREEN: 'blue_green',
  CANARY: 'canary',
  A_B_TESTING: 'a_b_testing'
} as const

// 环境类型
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  TESTING: 'testing',
  STAGING: 'staging',
  PRODUCTION: 'production'
} as const

/**
 * 获取任务状态的显示文本
 */
export function getTaskStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    [TASK_STATUS.ACTIVE]: '活跃',
    [TASK_STATUS.INACTIVE]: '非活跃',
    [TASK_STATUS.PENDING]: '等待中',
    [TASK_STATUS.RUNNING]: '运行中',
    [TASK_STATUS.COMPLETED]: '已完成',
    [TASK_STATUS.FAILED]: '失败',
    [TASK_STATUS.CANCELLED]: '已取消',
    [TASK_STATUS.STOPPED]: '已停止',
    [TASK_STATUS.DEPLOYING]: '部署中',
    [TASK_STATUS.DEPLOYED]: '已部署',
    [TASK_STATUS.ROLLBACK]: '回滚中'
  }
  return statusMap[status] || status
}

/**
 * 获取任务状态的颜色类型
 */
export function getTaskStatusColor(status: string): 'success' | 'warning' | 'error' | 'info' | 'default' {
  switch (status) {
    case TASK_STATUS.ACTIVE:
    case TASK_STATUS.COMPLETED:
    case TASK_STATUS.DEPLOYED:
      return 'success'
    case TASK_STATUS.RUNNING:
    case TASK_STATUS.DEPLOYING:
    case TASK_STATUS.PENDING:
      return 'info'
    case TASK_STATUS.FAILED:
      return 'error'
    case TASK_STATUS.ROLLBACK:
      return 'warning'
    case TASK_STATUS.INACTIVE:
    case TASK_STATUS.CANCELLED:
    case TASK_STATUS.STOPPED:
    default:
      return 'default'
  }
}

/**
 * 获取CI任务类型的显示文本
 */
export function getCiTaskTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    [CI_TASK_TYPES.BUILD]: '构建',
    [CI_TASK_TYPES.TEST]: '测试',
    [CI_TASK_TYPES.LINT]: '代码检查',
    [CI_TASK_TYPES.SECURITY_SCAN]: '安全扫描',
    [CI_TASK_TYPES.CUSTOM]: '自定义'
  }
  return typeMap[type] || type
}

/**
 * 获取CD部署策略的显示文本
 */
export function getCdDeploymentStrategyText(strategy: string): string {
  const strategyMap: Record<string, string> = {
    [CD_DEPLOYMENT_STRATEGIES.ROLLING_UPDATE]: '滚动更新',
    [CD_DEPLOYMENT_STRATEGIES.BLUE_GREEN]: '蓝绿部署',
    [CD_DEPLOYMENT_STRATEGIES.CANARY]: '金丝雀部署',
    [CD_DEPLOYMENT_STRATEGIES.A_B_TESTING]: 'A/B 测试'
  }
  return strategyMap[strategy] || strategy
}

/**
 * 获取环境的显示文本
 */
export function getEnvironmentText(env: string): string {
  const envMap: Record<string, string> = {
    [ENVIRONMENTS.DEVELOPMENT]: '开发环境',
    [ENVIRONMENTS.TESTING]: '测试环境',
    [ENVIRONMENTS.STAGING]: '预发布环境',
    [ENVIRONMENTS.PRODUCTION]: '生产环境'
  }
  return envMap[env] || env
}

/**
 * 格式化持续时间
 */
export function formatDuration(startTime: string, endTime?: string): string {
  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : new Date()
  const duration = end.getTime() - start.getTime()
  
  if (duration < 1000) {
    return '< 1秒'
  }
  
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天 ${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟 ${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

/**
 * 格式化相对时间
 */
export function formatRelativeTime(time: string): string {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (seconds < 60) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

/**
 * 判断任务是否正在运行
 */
export function isTaskRunning(status: string | undefined): boolean {
  if (!status) return false
  return [TASK_STATUS.RUNNING, TASK_STATUS.DEPLOYING, TASK_STATUS.PENDING].includes(status as any)
}

/**
 * 判断任务是否已完成
 */
export function isTaskCompleted(status: string): boolean {
  return [TASK_STATUS.COMPLETED, TASK_STATUS.DEPLOYED].includes(status as any)
}

/**
 * 判断任务是否失败
 */
export function isTaskFailed(status: string): boolean {
  return [TASK_STATUS.FAILED].includes(status as any)
}

/**
 * 判断任务是否可以执行
 */
export function canExecuteTask(task: DevOpsCiTask | DevOpsCdTask): boolean {
  return task.status === TASK_STATUS.ACTIVE && !isTaskRunning(task.status)
}

/**
 * 判断任务是否可以停止
 */
export function canStopTask(task: DevOpsCiTask | DevOpsCdTask): boolean {
  return isTaskRunning(task.status)
}

/**
 * 判断CD任务是否可以回滚
 */
export function canRollbackCdTask(task: DevOpsCdTask): boolean {
  // 使用配置中的版本信息来判断是否可以回滚
  return task.status === TASK_STATUS.DEPLOYED && 
    task.configuration && task.configuration.currentVersion !== undefined
}

/**
 * 生成任务配置模板
 */
export function generateCiTaskTemplate(taskType: string): string {
  const templates: Record<string, string> = {
    [CI_TASK_TYPES.BUILD]: `name: CI Build Pipeline
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build`,

    [CI_TASK_TYPES.TEST]: `name: CI Test Pipeline
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test -- --coverage

    - name: Upload coverage
      uses: codecov/codecov-action@v3`,

    [CI_TASK_TYPES.LINT]: `name: CI Lint Pipeline
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run type checking
      run: npm run type-check`
  }

  return templates[taskType] || templates[CI_TASK_TYPES.BUILD]
}

/**
 * 验证YAML配置格式
 */
export function validateYamlConfig(config: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!config.trim()) {
    errors.push('配置内容不能为空')
    return { isValid: false, errors }
  }
  
  const lines = config.split('\n')
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const trimmed = line.trim()
    
    // 跳过空行和注释
    if (!trimmed || trimmed.startsWith('#')) {
      continue
    }
    
    // 检查缩进
    const currentIndent = line.length - line.trimStart().length
    if (currentIndent % 2 !== 0) {
      errors.push(`第 ${i + 1} 行: 缩进应该使用2个空格的倍数`)
    }
    
    // 检查键值对格式
    if (trimmed.includes(':') && !trimmed.startsWith('-')) {
      const colonIndex = trimmed.indexOf(':')
      const key = trimmed.substring(0, colonIndex).trim()
      
      if (!key.match(/^[a-zA-Z_][a-zA-Z0-9_-]*$/)) {
        errors.push(`第 ${i + 1} 行: 键名格式不正确`)
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 解析日志行
 */
export function parseLogLine(line: string): {
  timestamp?: string
  level?: string
  message: string
  raw: string
} {
  // 匹配时间戳格式：2024-01-15T10:30:00Z 或 2024-01-15T10:30:00.123Z
  const timestampMatch = line.match(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z?)/)
  // 匹配日志级别：ERROR, WARN, INFO, DEBUG
  const levelMatch = line.match(/\s(ERROR|WARN|INFO|DEBUG)\s/)
  
  let timestamp = ''
  let level = ''
  let message = line

  if (timestampMatch) {
    timestamp = timestampMatch[1]
    message = line.substring(timestampMatch[0].length).trim()
  }

  if (levelMatch) {
    level = levelMatch[1]
    message = message.replace(levelMatch[0], ' ').trim()
  }

  return {
    timestamp,
    level,
    message,
    raw: line
  }
}

/**
 * 计算任务成功率
 */
export function calculateSuccessRate(instances: (DevOpsCiTaskInstance | DevOpsCdTaskInstance)[]): number {
  if (instances.length === 0) return 0
  
  const successCount = instances.filter(instance => 
    isTaskCompleted(instance.status)
  ).length
  
  return Math.round((successCount / instances.length) * 100)
}

/**
 * 计算平均执行时间
 */
export function calculateAverageExecutionTime(instances: (DevOpsCiTaskInstance | DevOpsCdTaskInstance)[]): number {
  const completedInstances = instances.filter(instance => 
    instance.endTime && instance.startTime
  )
  
  if (completedInstances.length === 0) return 0
  
  const totalDuration = completedInstances.reduce((sum, instance) => {
    const start = new Date(instance.startTime!).getTime()
    const end = new Date(instance.endTime!).getTime()
    return sum + (end - start)
  }, 0)
  
  return Math.round(totalDuration / completedInstances.length / 1000) // 返回秒数
}

/**
 * 导出日志文件
 */
export function exportLogs(logs: string, filename: string): void {
  const blob = new Blob([logs], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * 生成实例ID
 */
export function generateInstanceId(taskType: 'ci' | 'cd'): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `${taskType}-${timestamp}-${random}`
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}
