/**
 * CD 任务状态管理
 * 管理持续部署任务的状态和操作
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { DevOpsCdTask, DevOpsCdTaskInstance, QueryParams } from '@/modules/devops/types/devops'
import { cdTaskApi } from '@/modules/devops/services/devopsApi'

// API 服务接口
interface CdApiService {
  getTasks(params?: QueryParams): Promise<DevOpsCdTask[]>
  getTaskById(id: number): Promise<DevOpsCdTask>
  createTask(task: CreateCdTaskRequest, applicationId: number): Promise<DevOpsCdTask>
  updateTask(id: number, task: UpdateCdTaskRequest): Promise<DevOpsCdTask>
  deleteTask(id: number): Promise<void>
  deployTask(id: number, params?: any): Promise<Record<string, any>>
  rollbackTask(id: number, targetVersion?: string): Promise<Record<string, any>>
  stopDeployment(id: number): Promise<void>
  getDeploymentLogs(id: number): Promise<Record<string, any>[]>
  getDeploymentInstances(taskId: number): Promise<Record<string, any>[]>
}

// 请求类型定义
export interface CreateCdTaskRequest {
  name: string
  description?: string
  applicationId: number
  deploymentStrategy: string
  componentVersions: Record<string, string>
  configuration: {
    targetEnvironment: string
    namespace: string
    replicas: number
    timeout: number
    autoRollback: boolean
    rollbackTimeout: number
    maxRollbackAttempts: number
    healthCheckEnabled: boolean
    healthCheckEndpoint: string
    healthCheckTimeout: number
    healthCheckRetries: number
    notifyOnSuccess: boolean
    notifyOnFailure: boolean
    notifyOnRollback: boolean
    notificationChannels: string[]
  }
}

export interface UpdateCdTaskRequest extends Partial<CreateCdTaskRequest> {
  status?: string
}

// 真实 API 服务
const createApiService = (): CdApiService => ({
  async getTasks(params?: QueryParams): Promise<DevOpsCdTask[]> {
    return cdTaskApi.getAll(params)
  },

  async getTaskById(id: number): Promise<DevOpsCdTask> {
    return cdTaskApi.getById(id)
  },

  async createTask(task: CreateCdTaskRequest, applicationId: number): Promise<DevOpsCdTask> {
    return cdTaskApi.create(task, applicationId)
  },

  async updateTask(id: number, task: UpdateCdTaskRequest): Promise<DevOpsCdTask> {
    return cdTaskApi.update(id, task)
  },

  async deleteTask(id: number): Promise<void> {
    return cdTaskApi.delete(id)
  },

  async deployTask(id: number, params?: any): Promise<Record<string, any>> {
    return cdTaskApi.start(id, params)
  },

  async rollbackTask(id: number, targetVersion?: string): Promise<Record<string, any>> {
    return cdTaskApi.rollback(id, targetVersion || 'previous')
  },

  async stopDeployment(id: number): Promise<void> {
    return cdTaskApi.stop(id)
  },

  async getDeploymentLogs(id: number): Promise<Record<string, any>[]> {
    return cdTaskApi.getHistory(id)
  },

  async getDeploymentInstances(taskId: number): Promise<Record<string, any>[]> {
    return cdTaskApi.getHistory(taskId)
  }
})

export const useCdStore = defineStore('cd', () => {
  // API 服务实例
  const apiService = createApiService()

  // 状态定义
  const tasks = ref<DevOpsCdTask[]>([])
  const currentTask = ref<DevOpsCdTask | null>(null)
  const deployments = ref<DevOpsCdTaskInstance[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const activeTasks = computed(() => 
    tasks.value.filter(task => task.status === 'ACTIVE')
  )
  
  const deployingTasks = computed(() =>
    tasks.value.filter(task => task.status === 'DEPLOYING')
  )

  const deployedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'DEPLOYED')
  )

  const failedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'FAILED')
  )

  const tasksByStrategy = computed(() => {
    const result: Record<string, DevOpsCdTask[]> = {}
    tasks.value.forEach(task => {
      // 使用默认值'default'来处理undefined情况
      const strategy = task.deploymentStrategy || 'default'
      if (!result[strategy]) {
        result[strategy] = []
      }
      result[strategy].push(task)
    })
    return result
  })

  const tasksByApplication = computed(() => {
    const result: Record<number, DevOpsCdTask[]> = {}
    tasks.value.forEach(task => {
      if (!result[task.applicationId]) {
        result[task.applicationId] = []
      }
      result[task.applicationId].push(task)
    })
    return result
  })

  const tasksByEnvironment = computed(() => {
    const result: Record<string, DevOpsCdTask[]> = {}
    tasks.value.forEach(task => {
      const env = task.configuration?.targetEnvironment || 'unknown'
      if (!result[env]) {
        result[env] = []
      }
      result[env].push(task)
    })
    return result
  })

  // 操作方法
  const fetchTasks = async (params?: QueryParams) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.getTasks(params)
      tasks.value = response
    } catch (err: any) {
      error.value = err.message || '获取 CD 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTaskById = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const task = await apiService.getTaskById(id)
      currentTask.value = task
      return task
    } catch (err: any) {
      error.value = err.message || '获取 CD 任务详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData: CreateCdTaskRequest, applicationId: number) => {
    loading.value = true
    error.value = null
    try {
      const newTask = await apiService.createTask(taskData, applicationId)
      tasks.value.unshift(newTask)
      return newTask
    } catch (err: any) {
      error.value = err.message || '创建 CD 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTask = async (id: number, taskData: UpdateCdTaskRequest) => {
    loading.value = true
    error.value = null
    try {
      const updatedTask = await apiService.updateTask(id, taskData)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value[index] = updatedTask
      }
      if (currentTask.value?.id === id) {
        currentTask.value = updatedTask
      }
      return updatedTask
    } catch (err: any) {
      error.value = err.message || '更新 CD 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteTask = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await apiService.deleteTask(id)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value.splice(index, 1)
      }
      if (currentTask.value?.id === id) {
        currentTask.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除 CD 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deployTask = async (id: number, params?: any) => {
    loading.value = true
    error.value = null
    try {
      const deployment = await apiService.deployTask(id, params)

      // 更新任务状态为部署中
      const taskIndex = tasks.value.findIndex(t => t.id === id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = {
          ...tasks.value[taskIndex],
          status: 'DEPLOYING',
          updatedAt: new Date().toISOString()
        }
      }

      return deployment
    } catch (err: any) {
      error.value = err.message || '部署 CD 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const rollbackTask = async (id: number, targetVersion?: string) => {
    loading.value = true
    error.value = null
    try {
      const rollback = await apiService.rollbackTask(id, targetVersion)

      // 更新任务状态为部署中（回滚也是一种部署）
      const taskIndex = tasks.value.findIndex(t => t.id === id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = {
          ...tasks.value[taskIndex],
          status: 'DEPLOYING',
          updatedAt: new Date().toISOString()
        }
      }

      return rollback
    } catch (err: any) {
      error.value = err.message || '回滚 CD 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const stopDeployment = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await apiService.stopDeployment(id)

      // 更新任务状态
      const taskIndex = tasks.value.findIndex(t => t.id === id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = {
          ...tasks.value[taskIndex],
          status: 'STOPPED',
          updatedAt: new Date().toISOString()
        }
      }
    } catch (err: any) {
      error.value = err.message || '停止部署失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchDeploymentLogs = async (id: number) => {
    try {
      return await apiService.getDeploymentLogs(id)
    } catch (err: any) {
      error.value = err.message || '获取部署日志失败'
      throw err
    }
  }

  const fetchDeploymentInstances = async (taskId: number) => {
    loading.value = true
    error.value = null
    try {
      const instances = await apiService.getDeploymentInstances(taskId)
      return instances
    } catch (err: any) {
      error.value = err.message || '获取部署实例失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const resetState = () => {
    tasks.value = []
    currentTask.value = null
    deployments.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    tasks,
    currentTask,
    deployments,
    loading,
    error,
    
    // 计算属性
    activeTasks,
    deployingTasks,
    deployedTasks,
    failedTasks,
    tasksByStrategy,
    tasksByApplication,
    tasksByEnvironment,
    
    // 方法
    fetchTasks,
    fetchTaskById,
    createTask,
    updateTask,
    deleteTask,
    deployTask,
    rollbackTask,
    stopDeployment,
    fetchDeploymentLogs,
    fetchDeploymentInstances,
    clearError,
    resetState
  }
})
