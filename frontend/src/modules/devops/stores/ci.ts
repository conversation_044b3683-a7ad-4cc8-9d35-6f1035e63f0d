/**
 * CI 任务状态管理
 * 管理持续集成任务的状态和操作
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { DevOpsCiTask, DevOpsCiTaskInstance, QueryParams } from '@/modules/devops/types/devops'
import { ciTaskApi } from '@/modules/devops/services/devopsApi'

// 请求类型定义
export interface CreateCiTaskRequest {
  name: string
  description?: string
  componentId: number
  taskType: string
  triggerType: string
  schedule?: string
  timeout?: number
  configuration: {
    workingDirectory: string
    environment: string
    script: string
    notifyOnSuccess: boolean
    notifyOnFailure: boolean
    notificationChannels: string[]
  }
}

export interface UpdateCiTaskRequest extends Partial<CreateCiTaskRequest> {
  status?: string
}

export const useCiStore = defineStore('ci', () => {
  // 状态定义
  const tasks = ref<DevOpsCiTask[]>([])
  const currentTask = ref<DevOpsCiTask | null>(null)
  const taskInstances = ref<DevOpsCiTaskInstance[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const activeTasks = computed(() => 
    tasks.value.filter(task => task.status === 'ACTIVE')
  )
  
  const runningTasks = computed(() =>
    tasks.value.filter(task => task.status === 'RUNNING')
  )

  const completedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'COMPLETED')
  )

  const failedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'FAILED')
  )

  const tasksByType = computed(() => {
    const result: Record<string, DevOpsCiTask[]> = {}
    tasks.value.forEach(task => {
      if (!result[task.taskType]) {
        result[task.taskType] = []
      }
      result[task.taskType].push(task)
    })
    return result
  })

  const tasksByComponent = computed(() => {
    const result: Record<number, DevOpsCiTask[]> = {}
    tasks.value.forEach(task => {
      if (!result[task.componentId]) {
        result[task.componentId] = []
      }
      result[task.componentId].push(task)
    })
    return result
  })

  // 操作方法
  const fetchTasks = async (params?: QueryParams) => {
    loading.value = true
    error.value = null
    try {
      const response = await ciTaskApi.getAll(params)
      tasks.value = response
    } catch (err: any) {
      error.value = err.message || '获取 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTaskById = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const task = await ciTaskApi.getById(id)
      currentTask.value = task
      return task
    } catch (err: any) {
      error.value = err.message || '获取 CI 任务详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData: Omit<DevOpsCiTask, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    try {
      const newTask = await ciTaskApi.create(taskData)
      tasks.value.unshift(newTask)
      return newTask
    } catch (err: any) {
      error.value = err.message || '创建 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTask = async (id: number, taskData: Partial<DevOpsCiTask>) => {
    loading.value = true
    error.value = null
    try {
      const updatedTask = await ciTaskApi.update(id, taskData)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value[index] = updatedTask
      }
      if (currentTask.value?.id === id) {
        currentTask.value = updatedTask
      }
      return updatedTask
    } catch (err: any) {
      error.value = err.message || '更新 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteTask = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await ciTaskApi.delete(id)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value.splice(index, 1)
      }
      if (currentTask.value?.id === id) {
        currentTask.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const executeTask = async (id: number, params?: any) => {
    loading.value = true
    error.value = null
    try {
      const result = await ciTaskApi.start(id, params)

      // 更新任务状态为运行中
      const taskIndex = tasks.value.findIndex(t => t.id === id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = {
          ...tasks.value[taskIndex],
          status: 'RUNNING',
          updatedAt: new Date().toISOString()
        }
      }

      return result
    } catch (err: any) {
      error.value = err.message || '执行 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const stopTask = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await ciTaskApi.stop(id)

      // 更新任务状态
      const taskIndex = tasks.value.findIndex(t => t.id === id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = {
          ...tasks.value[taskIndex],
          status: 'STOPPED',
          updatedAt: new Date().toISOString()
        }
      }
    } catch (err: any) {
      error.value = err.message || '停止 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTaskLogs = async (taskId: number) => {
    try {
      // 这里需要根据实际API实现
      // 暂时返回空字符串，等待后端API实现
      return ''
    } catch (err: any) {
      error.value = err.message || '获取任务日志失败'
      throw err
    }
  }

  const fetchTaskInstances = async (taskId: number) => {
    loading.value = true
    error.value = null
    try {
      // 这里需要根据实际API实现
      // 暂时返回空数组，等待后端API实现
      const instances: DevOpsCiTaskInstance[] = []
      taskInstances.value = instances
      return instances
    } catch (err: any) {
      error.value = err.message || '获取任务实例失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const resetState = () => {
    tasks.value = []
    currentTask.value = null
    taskInstances.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    tasks,
    currentTask,
    taskInstances,
    loading,
    error,
    
    // 计算属性
    activeTasks,
    runningTasks,
    completedTasks,
    failedTasks,
    tasksByType,
    tasksByComponent,
    
    // 方法
    fetchTasks,
    fetchTaskById,
    createTask,
    updateTask,
    deleteTask,
    executeTask,
    stopTask,
    fetchTaskLogs,
    fetchTaskInstances,
    clearError,
    resetState
  }
})
