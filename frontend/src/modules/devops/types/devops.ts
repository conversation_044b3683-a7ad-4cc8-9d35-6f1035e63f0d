/**
 * DevOps管理模块的TypeScript类型定义
 * 与后端API保持一致的数据结构
 */

// 基础实体接口
export interface BaseEntity {
  id?: number
  createdAt?: string
  updatedAt?: string
  userId?: number
}

// 项目实体
export interface DevOpsProject extends BaseEntity {
  name: string
  description?: string
  status?: string
  configuration?: Record<string, any>
}

// 应用实体
export interface DevOpsApplication extends BaseEntity {
  name: string
  description?: string
  projectId: number
  status?: string
  configuration?: Record<string, any>
}

// 组件实体
export interface DevOpsComponent extends BaseEntity {
  name: string
  description?: string
  applicationId: number
  repositoryUrl?: string
  repositoryType?: string
  repositoryBranch?: string
  buildPath?: string
  status?: string
  configuration?: Record<string, any>
}

// 资源实体
export interface DevOpsResource extends BaseEntity {
  name: string
  description?: string
  componentId: number
  type: string
  path?: string
  configuration?: Record<string, any>
  status?: string
}

// CI任务实体
export interface DevOpsCiTask extends BaseEntity {
  name: string
  description?: string
  componentId: number
  taskType: string
  configuration: Record<string, any>
  status?: string
  triggerType?: string
  timeout?: number
}

// CI任务实例
export interface DevOpsCiTaskInstance extends BaseEntity {
  ciTaskId: number
  instanceId: string
  status: string
  startTime?: string
  endTime?: string
  logs?: string
  resultData?: Record<string, any>
  errorMessage?: string
}

// CD任务实体
export interface DevOpsCdTask extends BaseEntity {
  name: string
  description?: string
  applicationId: number
  componentVersions: Record<string, string>
  status?: string
  configuration: Record<string, any>
  deploymentStrategy?: string
}

// CD任务实例
export interface DevOpsCdTaskInstance extends BaseEntity {
  cdTaskId: number
  instanceId: string
  status: string
  startTime?: string
  endTime?: string
  deploymentInfo?: Record<string, any>
  logs?: string
  errorMessage?: string
  version?: string
}

// 部署模板
export interface DevOpsDeploymentTemplate extends BaseEntity {
  name: string
  description?: string
  templateType: string
  templateContent: string
  variables?: Record<string, any>
}

// Tekton集群配置
export interface DevOpsTektonCluster extends BaseEntity {
  name: string
  description?: string
  apiServer: string
  namespace?: string
  credentials?: Record<string, any>
  status?: string
}

// API响应包装类型
export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

// 分页响应类型
export interface PagedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

// 查询参数类型
export interface QueryParams {
  page?: number
  size?: number
  sort?: string
  search?: string
  [key: string]: any
}

// 表单状态类型
export interface FormState {
  loading: boolean
  errors: Record<string, string>
  touched: Record<string, boolean>
}

// 表格列定义
export interface TableColumn {
  key: string
  title: string
  width?: string
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: any) => string | any
}

// 状态选项
export interface StatusOption {
  value: string
  label: string
  color: string
}

// 面包屑导航项
export interface BreadcrumbItem {
  title: string
  path?: string
  icon?: string
  active?: boolean
}

// 操作按钮配置
export interface ActionButton {
  key: string
  label: string
  icon?: string
  type?: 'primary' | 'secondary' | 'danger' | 'success'
  disabled?: boolean
  loading?: boolean
  onClick: () => void
}

// 模态框配置
export interface ModalConfig {
  title: string
  visible: boolean
  loading?: boolean
  width?: string
  onOk?: () => void
  onCancel?: () => void
}

// 任务执行参数
export interface TaskExecutionParams {
  [key: string]: any
}

// 任务状态统计
export interface TaskStatusStats {
  total: number
  running: number
  completed: number
  failed: number
  pending: number
}

// YAML编辑器配置
export interface YamlEditorConfig {
  language: string
  theme: string
  readOnly?: boolean
  minimap?: boolean
  lineNumbers?: boolean
  wordWrap?: boolean
}

// 日志查看器配置
export interface LogViewerConfig {
  autoScroll?: boolean
  showTimestamp?: boolean
  maxLines?: number
  refreshInterval?: number
}

// 导出类型常量
export const TASK_TYPES = {
  CI: {
    BUILD: 'build',
    TEST: 'test',
    DEPLOY: 'deploy',
    CUSTOM: 'custom'
  },
  CD: {
    DEPLOY: 'deploy',
    ROLLBACK: 'rollback',
    SCALE: 'scale'
  }
} as const

export const TASK_STATUS = {
  PENDING: 'PENDING',
  RUNNING: 'RUNNING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
} as const

export const RESOURCE_TYPES = {
  SERVICE: 'SERVICE',
  DATABASE: 'DATABASE',
  CACHE: 'CACHE',
  QUEUE: 'QUEUE',
  STORAGE: 'STORAGE',
  API: 'API'
} as const

export const REPOSITORY_TYPES = {
  GIT: 'GIT',
  SVN: 'SVN',
  MERCURIAL: 'MERCURIAL'
} as const
