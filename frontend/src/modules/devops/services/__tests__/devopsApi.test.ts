/**
 * DevOps API服务的单元测试
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { projectApi, applicationApi, componentApi, resourceApi } from '../devopsApi'
import type { DevOpsProject, DevOpsApplication, DevOpsComponent, DevOpsResource } from '@/modules/devops/types/devops'

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('DevOps API Services', () => {
  beforeEach(() => {
    mockFetch.mockClear()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('projectApi', () => {
    const mockProject: DevOpsProject = {
      id: 1,
      name: '测试项目',
      description: '这是一个测试项目',
      status: 'ACTIVE',
      createdAt: '2024-01-01T00:00:00Z',
      userId: 1
    }

    it('should fetch all projects', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockProject]
      })

      const result = await projectApi.getAll()
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/projects', {
        headers: { 'Content-Type': 'application/json' }
      })
      expect(result).toEqual([mockProject])
    })

    it('should fetch project by id', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockProject
      })

      const result = await projectApi.getById(1)
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/projects/1', {
        headers: { 'Content-Type': 'application/json' }
      })
      expect(result).toEqual(mockProject)
    })

    it('should create a new project', async () => {
      const newProject = {
        name: '新项目',
        description: '新项目描述',
        status: 'ACTIVE' as const,
        userId: 1
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ ...newProject, id: 2, createdAt: '2024-01-01T00:00:00Z' })
      })

      const result = await projectApi.create(newProject)
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newProject)
      })
      expect(result.name).toBe(newProject.name)
      expect(result.id).toBe(2)
    })

    it('should update a project', async () => {
      const updateData = { name: '更新的项目名称' }
      const updatedProject = { ...mockProject, ...updateData }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => updatedProject
      })

      const result = await projectApi.update(1, updateData)
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/projects/1', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })
      expect(result.name).toBe(updateData.name)
    })

    it('should delete a project', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => undefined
      })

      await projectApi.delete(1)
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/projects/1', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      })
    })

    it('should search projects', async () => {
      const searchQuery = '测试'
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockProject]
      })

      const result = await projectApi.search(searchQuery)
      
      expect(mockFetch).toHaveBeenCalledWith(`/api/devops/projects/search?q=${encodeURIComponent(searchQuery)}`, {
        headers: { 'Content-Type': 'application/json' }
      })
      expect(result).toEqual([mockProject])
    })

    it('should handle API errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        text: async () => 'Project not found'
      })

      await expect(projectApi.getById(999)).rejects.toThrow('Project not found')
    })
  })

  describe('applicationApi', () => {
    const mockApplication: DevOpsApplication = {
      id: 1,
      name: '测试应用',
      description: '这是一个测试应用',
      projectId: 1,
      status: 'ACTIVE',
      createdAt: '2024-01-01T00:00:00Z',
      userId: 1
    }

    it('should fetch applications by project', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockApplication]
      })

      const result = await applicationApi.getByProject(1)
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/projects/1/applications', {
        headers: { 'Content-Type': 'application/json' }
      })
      expect(result).toEqual([mockApplication])
    })

    it('should create a new application', async () => {
      const newApplication = {
        name: '新应用',
        description: '新应用描述',
        projectId: 1,
        status: 'ACTIVE' as const,
        userId: 1
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ ...newApplication, id: 2, createdAt: '2024-01-01T00:00:00Z' })
      })

      const result = await applicationApi.create(newApplication)
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/applications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newApplication)
      })
      expect(result.name).toBe(newApplication.name)
    })
  })

  describe('componentApi', () => {
    const mockComponent: DevOpsComponent = {
      id: 1,
      name: '测试组件',
      description: '这是一个测试组件',
      applicationId: 1,
      repositoryUrl: 'https://github.com/test/repo.git',
      repositoryType: 'GIT',
      status: 'ACTIVE',
      createdAt: '2024-01-01T00:00:00Z',
      userId: 1
    }

    it('should fetch components by application', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockComponent]
      })

      const result = await componentApi.getByApplication(1)
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/applications/1/components', {
        headers: { 'Content-Type': 'application/json' }
      })
      expect(result).toEqual([mockComponent])
    })

    it('should create a new component', async () => {
      const newComponent = {
        name: '新组件',
        description: '新组件描述',
        applicationId: 1,
        repositoryUrl: 'https://github.com/test/new-repo.git',
        repositoryType: 'GIT' as const,
        status: 'ACTIVE' as const,
        userId: 1
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ ...newComponent, id: 2, createdAt: '2024-01-01T00:00:00Z' })
      })

      const result = await componentApi.create(newComponent)
      
      expect(result.name).toBe(newComponent.name)
      expect(result.repositoryUrl).toBe(newComponent.repositoryUrl)
    })
  })

  describe('resourceApi', () => {
    const mockResource: DevOpsResource = {
      id: 1,
      name: '测试资源',
      description: '这是一个测试资源',
      componentId: 1,
      type: 'SERVICE',
      path: '/api/test',
      status: 'ACTIVE',
      createdAt: '2024-01-01T00:00:00Z',
      userId: 1
    }

    it('should fetch resources by component', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [mockResource]
      })

      const result = await resourceApi.getByComponent(1)
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/components/1/resources', {
        headers: { 'Content-Type': 'application/json' }
      })
      expect(result).toEqual([mockResource])
    })

    it('should fetch resource types', async () => {
      const mockTypes = ['SERVICE', 'DATABASE', 'CACHE']
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockTypes
      })

      const result = await resourceApi.getTypes()
      
      expect(mockFetch).toHaveBeenCalledWith('/api/devops/resources/types', {
        headers: { 'Content-Type': 'application/json' }
      })
      expect(result).toEqual(mockTypes)
    })

    it('should create a new resource', async () => {
      const newResource = {
        name: '新资源',
        description: '新资源描述',
        componentId: 1,
        type: 'DATABASE' as const,
        path: '/db/test',
        status: 'ACTIVE' as const,
        userId: 1
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ ...newResource, id: 2, createdAt: '2024-01-01T00:00:00Z' })
      })

      const result = await resourceApi.create(newResource)
      
      expect(result.name).toBe(newResource.name)
      expect(result.type).toBe(newResource.type)
    })
  })

  describe('Error handling', () => {
    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      await expect(projectApi.getAll()).rejects.toThrow('Network error')
    })

    it('should handle HTTP errors with custom message', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        text: async () => 'Internal server error'
      })

      await expect(projectApi.getAll()).rejects.toThrow('Internal server error')
    })

    it('should handle HTTP errors without custom message', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        text: async () => ''
      })

      await expect(projectApi.getAll()).rejects.toThrow('HTTP 404')
    })
  })
})
