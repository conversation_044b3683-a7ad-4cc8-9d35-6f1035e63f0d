<template>
  <div class="min-h-screen bg-gray-100">
    <header class="bg-primary text-white py-4 shadow-md">
      <div class="container mx-auto px-4 flex justify-between items-center">
        <h1 class="text-xl md:text-2xl font-bold">Spring Boot + Vue 3 应用</h1>
        <button @click="logout" class="btn btn-logout">退出登录</button>
      </div>
    </header>

    <main class="container mx-auto px-4 py-8">
      <div class="bg-white rounded-lg shadow-md p-6 md:p-8">
        <h2 class="text-xl md:text-2xl font-semibold text-primary mb-4">欢迎来到您的控制面板！</h2>
        <p class="text-gray-700">您已成功登录到应用程序。</p>
      </div>

      <!-- 响应式设计示例 -->
      <div class="responsive-grid mt-8">
        <div class="card">
          <h3 class="text-lg font-semibold text-primary mb-2">响应式设计</h3>
          <p class="text-gray-700">此应用程序支持移动端和PC端，自动适应不同屏幕尺寸。</p>
        </div>

        <div class="card">
          <h3 class="text-lg font-semibold text-primary mb-2">Tailwind CSS</h3>
          <p class="text-gray-700">使用 Tailwind CSS 实现样式，减少自定义 CSS 的需求。</p>
        </div>

        <div class="card">
          <h3 class="text-lg font-semibold text-primary mb-2">Spring WebFlux</h3>
          <p class="text-gray-700">后端使用 Spring WebFlux 实现响应式编程。</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/modules/common/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const logout = (): void => {
  authStore.logout()
  router.push({ 'path': '/login' })
}
</script>
