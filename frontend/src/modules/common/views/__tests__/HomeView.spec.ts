import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import HomeView from '../HomeView.vue'

// 创建模拟的路由实例
const mockPush = vi.fn()
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: HomeView },
    { path: '/login', name: 'login', component: { template: '<div>Login</div>' } }
  ]
})

// 模拟路由的push方法
vi.spyOn(router, 'push').mockImplementation(mockPush)

// 模拟 Pinia 存储
const mockLogout = vi.fn()
vi.mock('@/modules/common/stores/auth', () => ({
  useAuthStore: vi.fn(() => ({
    logout: mockLogout,
    isAuthenticated: true
  }))
}))

describe('HomeView', () => {
  beforeEach(() => {
    // 创建一个新的 Pinia 实例
    setActivePinia(createPinia())

    // 重置路由
    router.push('/')

    // 清除所有模拟
    vi.clearAllMocks()
    mockPush.mockClear()
    mockLogout.mockClear()
  })

  it('renders the home page correctly', () => {
    const wrapper = mount(HomeView, {
      global: {
        plugins: [router]
      }
    })

    // 验证标题
    expect(wrapper.find('h1').text()).toBe('Spring Boot + Vue 3 应用')

    // 验证欢迎信息
    expect(wrapper.find('h2').text()).toBe('欢迎来到您的控制面板！')

    // 验证功能卡片
    const cards = wrapper.findAll('.card')
    expect(cards.length).toBe(3)

    // 验证退出按钮
    expect(wrapper.find('button').text()).toBe('退出登录')
  })

  it('logs out and redirects when logout button is clicked', async () => {
    const wrapper = mount(HomeView, {
      global: {
        plugins: [router]
      }
    })

    // 点击退出按钮
    await wrapper.find('button').trigger('click')

    // 验证 store 方法被调用
    expect(mockLogout).toHaveBeenCalled()

    // 验证路由跳转
    expect(mockPush).toHaveBeenCalledWith({ path: '/login' })
  })

  it('displays responsive design information', () => {
    const wrapper = mount(HomeView, {
      global: {
        plugins: [router]
      }
    })

    // 验证响应式设计信息
    const cards = wrapper.findAll('.card')

    expect(cards[0].find('h3').text()).toBe('响应式设计')
    expect(cards[0].find('p').text()).toContain('移动端和PC端')

    expect(cards[1].find('h3').text()).toBe('Tailwind CSS')
    expect(cards[1].find('p').text()).toContain('Tailwind CSS')

    expect(cards[2].find('h3').text()).toBe('Spring WebFlux')
    expect(cards[2].find('p').text()).toContain('WebFlux')
  })
})
