import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '../auth'
import axios from 'axios'

// 模拟 axios
vi.mock('axios')

describe('Auth Store', () => {
  beforeEach(() => {
    // 创建一个新的 Pinia 实例
    setActivePinia(createPinia())

    // 清除所有模拟
    vi.clearAllMocks()

    // 清除 localStorage
    localStorage.clear()

    // 重置 axios 默认头
    delete axios.defaults.headers.common['Authorization']
  })

  it('initializes with correct state', () => {
    const store = useAuthStore()

    expect(store.token).toBeNull()
    expect(store.user).toBeNull()
    expect(store.loading).toBe(false)
    expect(store.error).toBeNull()
    expect(store.isAuthenticated).toBe(false)
  })

  it('initializes with token from localStorage', () => {
    // 设置 localStorage
    localStorage.setItem('token', 'test-token')

    const store = useAuthStore()

    expect(store.token).toBe('test-token')
    expect(store.isAuthenticated).toBe(true)
  })

  it('login success sets token', async () => {
    const store = useAuthStore()

    // 模拟 axios 响应
    vi.mocked(axios.post).mockResolvedValueOnce({
      data: { token: 'test-token' }
    })

    // 调用登录方法
    const result = await store.login('testuser', 'password')

    // 验证 axios 调用
    expect(axios.post).toHaveBeenCalledWith('/auth/login', {
      username: 'testuser',
      password: 'password'
    })

    // 验证状态更新
    expect(result).toBe(true)
    expect(store.token).toBe('test-token')
    expect(store.loading).toBe(false)
    expect(store.error).toBeNull()

    // 验证 localStorage 更新
    expect(localStorage.getItem('token')).toBe('test-token')
  })

  it('login failure sets error', async () => {
    const store = useAuthStore()

    // 模拟 axios 错误
    vi.mocked(axios.post).mockRejectedValueOnce({
      response: { data: { error: '登录失败' } }
    })

    // 调用登录方法
    const result = await store.login('testuser', 'wrong-password')

    // 验证状态更新
    expect(result).toBe(false)
    expect(store.token).toBeNull()
    expect(store.loading).toBe(false)
    expect(store.error).toBe('登录失败')

    // 验证 localStorage 未更新
    expect(localStorage.getItem('token')).toBeNull()
  })

  it('logout clears token', () => {
    const store = useAuthStore()

    // 设置初始状态
    store.token = 'test-token'
    localStorage.setItem('token', 'test-token')

    // 调用登出方法
    store.logout()

    // 验证状态更新
    expect(store.token).toBeNull()
    expect(store.user).toBeNull()

    // 验证 localStorage 更新
    expect(localStorage.getItem('token')).toBeNull()
  })

  it('initializeAuth restores token from localStorage', () => {
    // 设置 localStorage
    localStorage.setItem('token', 'stored-token')

    const store = useAuthStore()

    // 调用初始化方法
    store.initializeAuth()

    // 验证状态更新
    expect(store.token).toBe('stored-token')
    expect(store.isAuthenticated).toBe(true)
  })

  it('initializeAuth handles missing token', () => {
    // 确保 localStorage 为空
    localStorage.removeItem('token')

    const store = useAuthStore()

    // 调用初始化方法
    store.initializeAuth()

    // 验证状态保持为空
    expect(store.token).toBeNull()
    expect(store.isAuthenticated).toBe(false)
  })

  it('register success returns true', async () => {
    const store = useAuthStore()

    // 模拟 axios 响应
    vi.mocked(axios.post).mockResolvedValueOnce({})

    // 调用注册方法
    const result = await store.register({
      username: 'newuser',
      email: '<EMAIL>',
      password: 'password',
      fullName: 'New User'
    })

    // 验证 axios 调用
    expect(axios.post).toHaveBeenCalledWith('/auth/register', {
      username: 'newuser',
      email: '<EMAIL>',
      password: 'password',
      fullName: 'New User'
    })

    // 验证状态更新
    expect(result).toBe(true)
    expect(store.loading).toBe(false)
    expect(store.error).toBeNull()
  })

  it('register failure sets error', async () => {
    const store = useAuthStore()

    // 模拟 axios 错误
    vi.mocked(axios.post).mockRejectedValueOnce({
      response: { data: { error: '用户名或邮箱已存在' } }
    })

    // 调用注册方法
    const result = await store.register({
      username: 'existinguser',
      email: '<EMAIL>',
      password: 'password',
      fullName: 'Existing User'
    })

    // 验证状态更新
    expect(result).toBe(false)
    expect(store.loading).toBe(false)
    expect(store.error).toBe('用户名或邮箱已存在')
  })
})
