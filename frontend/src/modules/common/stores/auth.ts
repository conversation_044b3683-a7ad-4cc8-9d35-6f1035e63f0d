import { defineStore } from 'pinia'
import axios from 'axios'
import { User, LoginResponse, RegisterRequest } from '@/modules/common/types/user'

interface AuthState {
  token: string | null;
  user: User | null;
  loading: boolean;
  error: string | null;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    token: localStorage.getItem('token') || null,
    user: null,
    loading: false,
    error: null
  }),

  getters: {
    isAuthenticated: (state): boolean => !!state.token
  },

  actions: {
    async login(username: string, password: string): Promise<boolean> {
      this.loading = true
      this.error = null

      try {
        // 使用原始axios进行登录请求，因为此时还没有token
        const response = await axios.post<LoginResponse>('/auth/login', { username, password })
        this.token = response.data.token
        localStorage.setItem('token', this.token)

        return true
      } catch (error: any) {
        this.error = error.response?.data?.error || '登录失败'
        return false
      } finally {
        this.loading = false
      }
    },

    logout(): void {
      this.token = null
      this.user = null
      localStorage.removeItem('token')
    },

    async register(userData: RegisterRequest): Promise<boolean> {
      this.loading = true
      this.error = null

      try {
        // 使用原始axios进行注册请求，因为此时还没有token
        await axios.post('/auth/register', userData)
        return true
      } catch (error: any) {
        this.error = error.response?.data?.error || '注册失败'
        return false
      } finally {
        this.loading = false
      }
    },

    // 初始化方法：在应用启动时调用，用于恢复登录状态
    initializeAuth(): void {
      const token = localStorage.getItem('token')
      if (token) {
        this.token = token
      }
    }
  }
})
