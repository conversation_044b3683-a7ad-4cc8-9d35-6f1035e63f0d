<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-medium text-gray-900">
            {{ config ? 'Edit MCP Server Configuration' : 'Create New MCP Server Configuration' }}
          </h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700">Name *</label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="My MCP Server"
              />
            </div>
            
            <div>
              <label for="dockerImage" class="block text-sm font-medium text-gray-700">Docker Image *</label>
              <input
                id="dockerImage"
                v-model="form.dockerImage"
                type="text"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="node:18-alpine"
              />
            </div>
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              id="description"
              v-model="form.description"
              rows="3"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              placeholder="Description of what this MCP server does..."
            />
          </div>

          <!-- Command Configuration -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">Command Configuration</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="command" class="block text-sm font-medium text-gray-700">Command *</label>
                <input
                  id="command"
                  v-model="form.command"
                  type="text"
                  required
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="node"
                />
              </div>
              
              <div>
                <label for="workingDirectory" class="block text-sm font-medium text-gray-700">Working Directory</label>
                <input
                  id="workingDirectory"
                  v-model="form.workingDirectory"
                  type="text"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="/app"
                />
              </div>
            </div>

            <!-- Arguments -->
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Arguments</label>
              <div class="space-y-2">
                <div v-for="(arg, index) in form.arguments" :key="index" class="flex gap-2">
                  <input
                    v-model="form.arguments[index]"
                    type="text"
                    class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Argument value"
                  />
                  <button
                    type="button"
                    @click="removeArgument(index)"
                    class="px-3 py-2 text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                </div>
                <button
                  type="button"
                  @click="addArgument"
                  class="text-blue-600 hover:text-blue-800 text-sm"
                >
                  + Add Argument
                </button>
              </div>
            </div>
          </div>

          <!-- Environment Variables -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">Environment Variables</h4>
            <div class="space-y-2">
              <div v-for="(value, key, index) in form.environment" :key="index" class="flex gap-2">
                <input
                  v-model="envKeys[index]"
                  @blur="updateEnvKey(index)"
                  type="text"
                  class="w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Variable name"
                />
                <input
                  v-model="form.environment[key]"
                  type="text"
                  class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Variable value"
                />
                <button
                  type="button"
                  @click="removeEnvVar(key)"
                  class="px-3 py-2 text-red-600 hover:text-red-800"
                >
                  Remove
                </button>
              </div>
              <button
                type="button"
                @click="addEnvVar"
                class="text-blue-600 hover:text-blue-800 text-sm"
              >
                + Add Environment Variable
              </button>
            </div>
          </div>

          <!-- Resource Limits -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">Resource Limits</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label for="memoryLimit" class="block text-sm font-medium text-gray-700">Memory Limit (MB)</label>
                <input
                  id="memoryLimit"
                  v-model.number="memoryLimitMB"
                  type="number"
                  min="64"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="512"
                />
              </div>
              
              <div>
                <label for="cpuLimit" class="block text-sm font-medium text-gray-700">CPU Limit (cores)</label>
                <input
                  id="cpuLimit"
                  v-model.number="form.resourceLimits.cpuLimit"
                  type="number"
                  min="0.1"
                  step="0.1"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="1.0"
                />
              </div>
              
              <div>
                <label for="timeoutSeconds" class="block text-sm font-medium text-gray-700">Timeout (seconds)</label>
                <input
                  id="timeoutSeconds"
                  v-model.number="form.timeoutSeconds"
                  type="number"
                  min="30"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="300"
                />
              </div>
            </div>
          </div>

          <!-- Volume Mounts -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">Volume Mounts</h4>
            <div class="space-y-4">
              <div v-for="(mount, index) in form.volumeMounts" :key="index" class="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border border-gray-200 rounded-lg">
                <div>
                  <label :for="`hostPath-${index}`" class="block text-sm font-medium text-gray-700">Host Path</label>
                  <input
                    :id="`hostPath-${index}`"
                    v-model="mount.hostPath"
                    type="text"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="/host/path"
                  />
                </div>
                <div>
                  <label :for="`containerPath-${index}`" class="block text-sm font-medium text-gray-700">Container Path</label>
                  <input
                    :id="`containerPath-${index}`"
                    v-model="mount.containerPath"
                    type="text"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="/container/path"
                  />
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <input
                      :id="`readOnly-${index}`"
                      v-model="mount.readOnly"
                      type="checkbox"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label :for="`readOnly-${index}`" class="ml-2 block text-sm text-gray-900">
                      Read Only
                    </label>
                  </div>
                  <button
                    @click="removeVolumeMount(index)"
                    type="button"
                    class="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove
                  </button>
                </div>
              </div>
              <button
                @click="addVolumeMount"
                type="button"
                class="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                + Add Volume Mount
              </button>
            </div>
          </div>

          <!-- Host Entries -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">Host Entries</h4>
            <div class="space-y-4">
              <div v-for="(entry, index) in form.hostEntries" :key="index" class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 rounded-lg">
                <div>
                  <label :for="`hostname-${index}`" class="block text-sm font-medium text-gray-700">Hostname</label>
                  <input
                    :id="`hostname-${index}`"
                    v-model="entry.hostname"
                    type="text"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="example.com"
                  />
                </div>
                <div class="flex items-end">
                  <div class="flex-1">
                    <label :for="`ipAddress-${index}`" class="block text-sm font-medium text-gray-700">IP Address</label>
                    <input
                      :id="`ipAddress-${index}`"
                      v-model="entry.ipAddress"
                      type="text"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      placeholder="*************"
                    />
                  </div>
                  <button
                    @click="removeHostEntry(index)"
                    type="button"
                    class="ml-2 text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove
                  </button>
                </div>
              </div>
              <button
                @click="addHostEntry"
                type="button"
                class="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                + Add Host Entry
              </button>
            </div>
          </div>

          <!-- Options -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">Options</h4>
            <div class="space-y-4">
              <div class="flex items-center">
                <input
                  id="autoRestart"
                  v-model="form.autoRestart"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="autoRestart" class="ml-2 block text-sm text-gray-900">
                  Auto-restart on failure
                </label>
              </div>
              
              <div class="flex items-center">
                <input
                  id="enabled"
                  v-model="form.enabled"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="enabled" class="ml-2 block text-sm text-gray-900">
                  Enabled
                </label>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              {{ config ? 'Update' : 'Create' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { McpServerConfiguration, ResourceLimits, VolumeMount, HostEntry } from '@/modules/mcp/types/mcp'

interface Props {
  config?: McpServerConfiguration | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  save: [config: McpServerConfiguration]
}>()

// Form data
const form = reactive<McpServerConfiguration>({
  name: '',
  description: '',
  command: '',
  arguments: [],
  environment: {},
  workingDirectory: '',
  dockerImage: '',
  resourceLimits: {
    memoryLimitBytes: undefined,
    cpuLimit: undefined,
    diskLimitBytes: undefined
  },
  volumeMounts: [],
  hostEntries: [],
  timeoutSeconds: 300,
  autoRestart: false,
  enabled: true
})

// Helper for environment variables
const envKeys = ref<string[]>([])

// Memory limit in MB for easier input
const memoryLimitMB = computed({
  get: () => form.resourceLimits?.memoryLimitBytes ? form.resourceLimits.memoryLimitBytes / (1024 * 1024) : undefined,
  set: (value: number | undefined) => {
    if (!form.resourceLimits) form.resourceLimits = {}
    form.resourceLimits.memoryLimitBytes = value ? value * 1024 * 1024 : undefined
  }
})

// Initialize form with existing config
if (props.config) {
  Object.assign(form, {
    ...props.config,
    arguments: props.config.arguments ? [...props.config.arguments] : [],
    environment: props.config.environment ? { ...props.config.environment } : {},
    resourceLimits: props.config.resourceLimits ? { ...props.config.resourceLimits } : {},
    volumeMounts: props.config.volumeMounts ? [...props.config.volumeMounts] : [],
    hostEntries: props.config.hostEntries ? [...props.config.hostEntries] : []
  })
  
  // Initialize environment keys
  envKeys.value = Object.keys(form.environment || {})
}

// Methods
const addArgument = () => {
  if (!form.arguments) form.arguments = []
  form.arguments.push('')
}

const removeArgument = (index: number) => {
  if (form.arguments) {
    form.arguments.splice(index, 1)
  }
}

const addEnvVar = () => {
  if (!form.environment) form.environment = {}
  const newKey = `VAR_${Object.keys(form.environment).length + 1}`
  form.environment[newKey] = ''
  envKeys.value.push(newKey)
}

const removeEnvVar = (key: string) => {
  if (form.environment) {
    delete form.environment[key]
    const index = envKeys.value.indexOf(key)
    if (index > -1) {
      envKeys.value.splice(index, 1)
    }
  }
}

const addVolumeMount = () => {
  if (!form.volumeMounts) form.volumeMounts = []
  form.volumeMounts.push({
    hostPath: '',
    containerPath: '',
    readOnly: false
  })
}

const removeVolumeMount = (index: number) => {
  if (form.volumeMounts) {
    form.volumeMounts.splice(index, 1)
  }
}

const addHostEntry = () => {
  if (!form.hostEntries) form.hostEntries = []
  form.hostEntries.push({
    hostname: '',
    ipAddress: ''
  })
}

const removeHostEntry = (index: number) => {
  if (form.hostEntries) {
    form.hostEntries.splice(index, 1)
  }
}

const updateEnvKey = (index: number) => {
  if (!form.environment) return
  
  const oldKey = Object.keys(form.environment)[index]
  const newKey = envKeys.value[index]
  
  if (oldKey === newKey) return
  
  const value = form.environment[oldKey]
  delete form.environment[oldKey]
  form.environment[newKey] = value
}

const handleSubmit = () => {
  // Clean up empty arguments
  if (form.arguments) {
    form.arguments = form.arguments.filter(arg => arg.trim() !== '')
  }
  
  // Clean up empty environment variables
  if (form.environment) {
    Object.keys(form.environment).forEach(key => {
      if (!form.environment![key] || form.environment![key].trim() === '') {
        delete form.environment![key]
      }
    })
  }
  
  emit('save', { ...form })
}
</script>
