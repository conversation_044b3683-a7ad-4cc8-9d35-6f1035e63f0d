<template>
  <nav class="breadcrumb" aria-label="面包屑导航">
    <ol class="breadcrumb-list">
      <li
        v-for="(item, index) in items"
        :key="index"
        class="breadcrumb-item"
        :class="{ active: item.active || index === items.length - 1 }"
      >
        <!-- 分隔符 -->
        <svg
          v-if="index > 0"
          class="breadcrumb-separator"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fill-rule="evenodd"
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
            clip-rule="evenodd"
          />
        </svg>

        <!-- 链接或文本 -->
        <router-link
          v-if="item.path && !item.active && index !== items.length - 1"
          :to="item.path"
          class="breadcrumb-link"
          :title="item.title"
        >
          <svg
            v-if="item.icon"
            class="breadcrumb-icon"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path :d="getIconPath(item.icon)" />
          </svg>
          <span>{{ item.title }}</span>
        </router-link>

        <span
          v-else
          class="breadcrumb-text"
          :class="{ current: item.active || index === items.length - 1 }"
          :title="item.title"
        >
          <svg
            v-if="item.icon"
            class="breadcrumb-icon"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path :d="getIconPath(item.icon)" />
          </svg>
          <span>{{ item.title }}</span>
        </span>
      </li>
    </ol>
  </nav>
</template>

<script setup lang="ts">
import type { BreadcrumbItem } from '@/modules/devops/types/devops'

interface Props {
  items: BreadcrumbItem[]
  separator?: string
  maxItems?: number
}

const props = withDefaults(defineProps<Props>(), {
  separator: '/',
  maxItems: 0
})

// 图标路径映射
const iconPaths = {
  home: 'M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z',
  project: 'M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z',
  application: 'M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z',
  component: 'M9 12a1 1 0 002 0V6.414l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 6.414V12zM3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z',
  resource: 'M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z',
  ci: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z',
  cd: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z',
  settings: 'M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z'
}

function getIconPath(iconName: string): string {
  return iconPaths[iconName as keyof typeof iconPaths] || iconPaths.home
}
</script>

<style scoped>
.breadcrumb {
  @apply py-2;
}

.breadcrumb-list {
  @apply flex items-center space-x-1 text-sm;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  @apply flex items-center;
}

.breadcrumb-separator {
  @apply w-4 h-4 text-gray-400 mx-2;
}

.breadcrumb-link {
  @apply flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:underline transition-colors;
  text-decoration: none;
}

.breadcrumb-text {
  @apply flex items-center gap-1 text-gray-500;
}

.breadcrumb-text.current {
  @apply text-gray-900 font-medium;
}

.breadcrumb-icon {
  @apply w-4 h-4;
}

.breadcrumb-item.active .breadcrumb-text {
  @apply text-gray-900 font-medium;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .breadcrumb-list {
    @apply text-xs;
  }
  
  .breadcrumb-icon {
    @apply w-3 h-3;
  }
  
  .breadcrumb-separator {
    @apply w-3 h-3 mx-1;
  }
  
  /* 在小屏幕上隐藏中间项，只显示首尾 */
  .breadcrumb-item:not(:first-child):not(:last-child) {
    @apply hidden;
  }
  
  /* 在隐藏中间项时显示省略号 */
  .breadcrumb-item:first-child:not(:last-child)::after {
    content: '...';
    @apply text-gray-400 mx-2;
  }
}

/* 悬停效果 */
.breadcrumb-link:hover .breadcrumb-icon {
  @apply transform scale-110 transition-transform;
}

/* 焦点样式 */
.breadcrumb-link:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 rounded;
}

/* 截断长文本 */
.breadcrumb-link span,
.breadcrumb-text span {
  @apply truncate max-w-32;
}

@media (min-width: 768px) {
  .breadcrumb-link span,
  .breadcrumb-text span {
    @apply max-w-48;
  }
}

@media (min-width: 1024px) {
  .breadcrumb-link span,
  .breadcrumb-text span {
    @apply max-w-64;
  }
}
</style>
