/**
 * StatusTag组件的单元测试
 */

import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import StatusTag from '../StatusTag.vue'

describe('StatusTag', () => {
  it('renders with default props', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'ACTIVE'
      }
    })
    
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.text()).toContain('活跃')
  })

  it('displays correct text for different statuses', () => {
    const testCases = [
      { status: 'ACTIVE', expectedText: '活跃' },
      { status: 'PENDING', expectedText: '等待中' },
      { status: 'RUNNING', expectedText: '运行中' },
      { status: 'COMPLETED', expectedText: '已完成' },
      { status: 'FAILED', expectedText: '失败' },
      { status: 'CANCELLED', expectedText: '已取消' }
    ]

    testCases.forEach(({ status, expectedText }) => {
      const wrapper = mount(StatusTag, {
        props: { status }
      })
      expect(wrapper.text()).toContain(expectedText)
    })
  })

  it('displays custom text when provided', () => {
    const customText = '自定义状态'
    const wrapper = mount(StatusTag, {
      props: {
        status: 'ACTIVE',
        text: customText
      }
    })
    
    expect(wrapper.text()).toContain(customText)
  })

  it('shows icon by default', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'RUNNING'
      }
    })
    
    const icon = wrapper.find('svg')
    expect(icon.exists()).toBe(true)
  })

  it('hides icon when showIcon is false', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'RUNNING',
        showIcon: false
      }
    })
    
    const icon = wrapper.find('svg')
    expect(icon.exists()).toBe(false)
  })

  it('applies correct size classes', () => {
    const sizes = ['small', 'medium', 'large'] as const
    
    sizes.forEach(size => {
      const wrapper = mount(StatusTag, {
        props: {
          status: 'ACTIVE',
          size
        }
      })
      
      const span = wrapper.find('span')
      expect(span.classes()).toContain(`text-${size === 'small' ? 'xs' : size === 'medium' ? 'sm' : 'base'}`)
    })
  })

  it('applies correct variant styles', () => {
    const variants = ['filled', 'outlined', 'subtle'] as const
    
    variants.forEach(variant => {
      const wrapper = mount(StatusTag, {
        props: {
          status: 'ACTIVE',
          variant
        }
      })
      
      const span = wrapper.find('span')
      expect(span.exists()).toBe(true)
      // 验证不同变体的样式类是否正确应用
      if (variant === 'filled') {
        expect(span.classes()).toContain('bg-green-100')
      } else if (variant === 'outlined') {
        expect(span.classes()).toContain('bg-transparent')
      } else if (variant === 'subtle') {
        expect(span.classes()).toContain('bg-green-50')
      }
    })
  })

  it('handles unknown status gracefully', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'UNKNOWN_STATUS'
      }
    })
    
    expect(wrapper.text()).toContain('未知')
  })

  it('applies correct color classes for different statuses', () => {
    const statusColorMap = [
      { status: 'ACTIVE', colorClass: 'text-green-800' },
      { status: 'RUNNING', colorClass: 'text-blue-800' },
      { status: 'FAILED', colorClass: 'text-red-800' },
      { status: 'PENDING', colorClass: 'text-yellow-800' }
    ]

    statusColorMap.forEach(({ status, colorClass }) => {
      const wrapper = mount(StatusTag, {
        props: { status }
      })
      
      const span = wrapper.find('span')
      expect(span.classes()).toContain(colorClass)
    })
  })
})
