<template>
  <div class="form-field" :class="{ error: hasError, required: required }">
    <label v-if="label" :for="fieldId" class="form-label">
      {{ label }}
      <span v-if="required" class="required-mark">*</span>
    </label>
    
    <div class="form-control">
      <!-- 文本输入框 -->
      <input
        v-if="type === 'text' || type === 'email' || type === 'password' || type === 'url'"
        :id="fieldId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        class="form-input"
        :class="inputClasses"
      />
      
      <!-- 数字输入框 -->
      <input
        v-else-if="type === 'number'"
        :id="fieldId"
        type="number"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :min="min"
        :max="max"
        :step="step"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        class="form-input"
        :class="inputClasses"
      />
      
      <!-- 文本域 -->
      <textarea
        v-else-if="type === 'textarea'"
        :id="fieldId"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :rows="rows"
        :maxlength="maxlength"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        class="form-textarea"
        :class="inputClasses"
      ></textarea>
      
      <!-- 选择框 -->
      <select
        v-else-if="type === 'select'"
        :id="fieldId"
        :value="modelValue"
        :disabled="disabled"
        @change="handleChange"
        @blur="handleBlur"
        @focus="handleFocus"
        class="form-select"
        :class="inputClasses"
      >
        <option v-if="placeholder" value="" disabled>{{ placeholder }}</option>
        <option
          v-for="option in options"
          :key="getOptionValue(option)"
          :value="getOptionValue(option)"
        >
          {{ getOptionLabel(option) }}
        </option>
      </select>
      
      <!-- 多选框 -->
      <div v-else-if="type === 'checkbox'" class="checkbox-group">
        <label
          v-for="option in options"
          :key="getOptionValue(option)"
          class="checkbox-item"
        >
          <input
            type="checkbox"
            :value="getOptionValue(option)"
            :checked="isChecked(getOptionValue(option))"
            :disabled="disabled"
            @change="handleCheckboxChange"
            class="checkbox-input"
          />
          <span class="checkbox-label">{{ getOptionLabel(option) }}</span>
        </label>
      </div>
      
      <!-- 单选框 -->
      <div v-else-if="type === 'radio'" class="radio-group">
        <label
          v-for="option in options"
          :key="getOptionValue(option)"
          class="radio-item"
        >
          <input
            type="radio"
            :name="fieldId"
            :value="getOptionValue(option)"
            :checked="modelValue === getOptionValue(option)"
            :disabled="disabled"
            @change="handleRadioChange"
            class="radio-input"
          />
          <span class="radio-label">{{ getOptionLabel(option) }}</span>
        </label>
      </div>
      
      <!-- 开关 -->
      <label v-else-if="type === 'switch'" class="switch-container">
        <input
          type="checkbox"
          :checked="modelValue"
          :disabled="disabled"
          @change="handleSwitchChange"
          class="switch-input"
        />
        <span class="switch-slider"></span>
        <span v-if="switchLabel" class="switch-label">{{ switchLabel }}</span>
      </label>
      
      <!-- 日期选择器 -->
      <input
        v-else-if="type === 'date' || type === 'datetime-local'"
        :id="fieldId"
        :type="type"
        :value="modelValue"
        :disabled="disabled"
        :readonly="readonly"
        :min="min"
        :max="max"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        class="form-input"
        :class="inputClasses"
      />
      
      <!-- 文件上传 -->
      <input
        v-else-if="type === 'file'"
        :id="fieldId"
        type="file"
        :accept="accept"
        :multiple="multiple"
        :disabled="disabled"
        @change="handleFileChange"
        class="form-file"
      />
      
      <!-- 自定义插槽 -->
      <slot v-else :value="modelValue" :onChange="handleInput"></slot>
    </div>
    
    <!-- 帮助文本 -->
    <div v-if="helpText && !hasError" class="form-help">
      {{ helpText }}
    </div>
    
    <!-- 错误信息 -->
    <div v-if="hasError" class="form-error">
      {{ errorMessage }}
    </div>
    
    <!-- 字符计数 -->
    <div v-if="showCount && maxlength" class="form-count">
      {{ (modelValue?.toString() || '').length }} / {{ maxlength }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface Option {
  label: string
  value: any
  disabled?: boolean
}

interface Props {
  modelValue?: any
  type?: 'text' | 'email' | 'password' | 'url' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'switch' | 'date' | 'datetime-local' | 'file'
  label?: string
  placeholder?: string
  helpText?: string
  errorMessage?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  options?: (Option | string)[]
  rows?: number
  maxlength?: number
  min?: number | string
  max?: number | string
  step?: number
  accept?: string
  multiple?: boolean
  showCount?: boolean
  switchLabel?: string
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'filled' | 'outlined'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  rows: 3,
  size: 'medium',
  variant: 'default'
})

const emit = defineEmits<{
  'update:modelValue': [value: any]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  change: [value: any]
}>()

const fieldId = ref(`field-${Math.random().toString(36).substr(2, 9)}`)

const hasError = computed(() => !!props.errorMessage)

const inputClasses = computed(() => {
  const classes = []
  
  if (hasError.value) {
    classes.push('error')
  }
  
  if (props.size) {
    classes.push(`size-${props.size}`)
  }
  
  if (props.variant) {
    classes.push(`variant-${props.variant}`)
  }
  
  return classes
})

const getOptionValue = (option: Option | string) => {
  return typeof option === 'string' ? option : option.value
}

const getOptionLabel = (option: Option | string) => {
  return typeof option === 'string' ? option : option.label
}

const isChecked = (value: any) => {
  if (Array.isArray(props.modelValue)) {
    return props.modelValue.includes(value)
  }
  return props.modelValue === value
}

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement
  let value: (string | number) = target.value
  
  if (props.type === 'number') {
    value = target.value === '' ? null : Number(target.value)
  }
  
  emit('update:modelValue', value)
  emit('change', value)
}

const handleChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  emit('update:modelValue', target.value)
  emit('change', target.value)
}

const handleCheckboxChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value
  let newValue = Array.isArray(props.modelValue) ? [...props.modelValue] : []
  
  if (target.checked) {
    if (!newValue.includes(value)) {
      newValue.push(value)
    }
  } else {
    newValue = newValue.filter(v => v !== value)
  }
  
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

const handleRadioChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
  emit('change', target.value)
}

const handleSwitchChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.checked)
  emit('change', target.checked)
}

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  const value = props.multiple ? Array.from(files || []) : files?.[0] || null
  emit('update:modelValue', value)
  emit('change', value)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}
</script>

<style scoped>
.form-field {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.required-mark {
  @apply text-red-500 ml-1;
}

.form-control {
  @apply relative;
}

/* 基础输入框样式 */
.form-input,
.form-textarea,
.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.form-input:disabled,
.form-textarea:disabled,
.form-select:disabled {
  @apply bg-gray-50 text-gray-500 cursor-not-allowed;
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

/* 尺寸变体 */
.form-input.size-small,
.form-textarea.size-small,
.form-select.size-small {
  @apply px-2 py-1 text-sm;
}

.form-input.size-large,
.form-textarea.size-large,
.form-select.size-large {
  @apply px-4 py-3 text-lg;
}

/* 样式变体 */
.form-input.variant-filled,
.form-textarea.variant-filled,
.form-select.variant-filled {
  @apply bg-gray-50 border-transparent focus:bg-white focus:border-blue-500;
}

.form-input.variant-outlined,
.form-textarea.variant-outlined,
.form-select.variant-outlined {
  @apply border-2 border-gray-300 focus:border-blue-500;
}

/* 文本域特殊样式 */
.form-textarea {
  @apply resize-y min-h-20;
}

/* 选择框样式 */
.form-select {
  @apply bg-white cursor-pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* 复选框组 */
.checkbox-group {
  @apply space-y-2;
}

.checkbox-item {
  @apply flex items-center gap-2 cursor-pointer;
}

.checkbox-input {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
}

.checkbox-label {
  @apply text-sm text-gray-700;
}

/* 单选框组 */
.radio-group {
  @apply space-y-2;
}

.radio-item {
  @apply flex items-center gap-2 cursor-pointer;
}

.radio-input {
  @apply w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500;
}

.radio-label {
  @apply text-sm text-gray-700;
}

/* 开关样式 */
.switch-container {
  @apply flex items-center gap-3 cursor-pointer;
}

.switch-input {
  @apply sr-only;
}

.switch-slider {
  @apply relative inline-block w-11 h-6 bg-gray-200 rounded-full transition-colors;
}

.switch-slider::before {
  @apply absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow transform transition-transform;
  content: '';
}

.switch-input:checked + .switch-slider {
  @apply bg-blue-600;
}

.switch-input:checked + .switch-slider::before {
  @apply translate-x-5;
}

.switch-input:disabled + .switch-slider {
  @apply opacity-50 cursor-not-allowed;
}

.switch-label {
  @apply text-sm text-gray-700;
}

/* 文件上传 */
.form-file {
  @apply w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100;
}

/* 帮助文本 */
.form-help {
  @apply mt-1 text-sm text-gray-500;
}

/* 错误信息 */
.form-error {
  @apply mt-1 text-sm text-red-600;
}

/* 字符计数 */
.form-count {
  @apply mt-1 text-xs text-gray-400 text-right;
}

/* 错误状态 */
.form-field.error .form-label {
  @apply text-red-700;
}

/* 必填字段 */
.form-field.required .form-label {
  @apply font-semibold;
}

/* 焦点状态 */
.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

/* 只读状态 */
.form-input[readonly],
.form-textarea[readonly] {
  @apply bg-gray-50 cursor-default;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .checkbox-group,
  .radio-group {
    @apply space-y-3;
  }

  .checkbox-item,
  .radio-item {
    @apply text-base;
  }
}
</style>
