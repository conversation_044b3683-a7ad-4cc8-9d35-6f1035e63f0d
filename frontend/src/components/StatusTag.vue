<template>
  <span :class="tagClasses">
    <svg v-if="showIcon" :class="iconClasses" viewBox="0 0 20 20" fill="currentColor">
      <path v-if="status === 'RUNNING' || status === 'IN_PROGRESS'" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" />
      <path v-else-if="status === 'COMPLETED' || status === 'SUCCESS' || status === 'ACTIVE'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      <path v-else-if="status === 'FAILED' || status === 'ERROR'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      <path v-else-if="status === 'PENDING' || status === 'WAITING'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
      <path v-else-if="status === 'CANCELLED' || status === 'STOPPED'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd" />
      <path v-else d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" />
    </svg>
    <span>{{ displayText }}</span>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  status: string
  text?: string
  showIcon?: boolean
  size?: 'small' | 'medium' | 'large'
  variant?: 'filled' | 'outlined' | 'subtle'
}

const props = withDefaults(defineProps<Props>(), {
  showIcon: true,
  size: 'medium',
  variant: 'filled'
})

// 状态配置映射
const statusConfig = {
  // 通用状态
  ACTIVE: { color: 'green', label: '活跃' },
  INACTIVE: { color: 'gray', label: '非活跃' },
  
  // 任务状态
  PENDING: { color: 'yellow', label: '等待中' },
  RUNNING: { color: 'blue', label: '运行中' },
  IN_PROGRESS: { color: 'blue', label: '进行中' },
  COMPLETED: { color: 'green', label: '已完成' },
  SUCCESS: { color: 'green', label: '成功' },
  FAILED: { color: 'red', label: '失败' },
  ERROR: { color: 'red', label: '错误' },
  CANCELLED: { color: 'gray', label: '已取消' },
  STOPPED: { color: 'gray', label: '已停止' },
  WAITING: { color: 'yellow', label: '等待中' },
  
  // 部署状态
  DEPLOYING: { color: 'blue', label: '部署中' },
  DEPLOYED: { color: 'green', label: '已部署' },
  ROLLBACK: { color: 'orange', label: '回滚中' },
  
  // 构建状态
  BUILDING: { color: 'blue', label: '构建中' },
  BUILT: { color: 'green', label: '构建完成' },
  BUILD_FAILED: { color: 'red', label: '构建失败' },
  
  // 测试状态
  TESTING: { color: 'blue', label: '测试中' },
  TESTED: { color: 'green', label: '测试通过' },
  TEST_FAILED: { color: 'red', label: '测试失败' },
  
  // 默认状态
  UNKNOWN: { color: 'gray', label: '未知' }
}

const currentConfig = computed(() => {
  return statusConfig[props.status as keyof typeof statusConfig] || statusConfig.UNKNOWN
})

const displayText = computed(() => {
  return props.text || currentConfig.value.label
})

const tagClasses = computed(() => {
  const baseClasses = 'inline-flex items-center gap-1 font-medium rounded-full transition-colors'
  const sizeClasses = {
    small: 'px-2 py-0.5 text-xs',
    medium: 'px-2.5 py-1 text-sm',
    large: 'px-3 py-1.5 text-base'
  }
  
  const colorClasses = getColorClasses(currentConfig.value.color, props.variant)
  
  return [baseClasses, sizeClasses[props.size], colorClasses].join(' ')
})

const iconClasses = computed(() => {
  const sizeClasses = {
    small: 'w-3 h-3',
    medium: 'w-4 h-4',
    large: 'w-5 h-5'
  }
  
  return sizeClasses[props.size]
})

function getColorClasses(color: string, variant: string): string {
  const colorMap = {
    filled: {
      green: 'bg-green-100 text-green-800 border border-green-200',
      blue: 'bg-blue-100 text-blue-800 border border-blue-200',
      red: 'bg-red-100 text-red-800 border border-red-200',
      yellow: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
      orange: 'bg-orange-100 text-orange-800 border border-orange-200',
      gray: 'bg-gray-100 text-gray-800 border border-gray-200',
      purple: 'bg-purple-100 text-purple-800 border border-purple-200'
    },
    outlined: {
      green: 'bg-transparent text-green-700 border-2 border-green-300 hover:bg-green-50',
      blue: 'bg-transparent text-blue-700 border-2 border-blue-300 hover:bg-blue-50',
      red: 'bg-transparent text-red-700 border-2 border-red-300 hover:bg-red-50',
      yellow: 'bg-transparent text-yellow-700 border-2 border-yellow-300 hover:bg-yellow-50',
      orange: 'bg-transparent text-orange-700 border-2 border-orange-300 hover:bg-orange-50',
      gray: 'bg-transparent text-gray-700 border-2 border-gray-300 hover:bg-gray-50',
      purple: 'bg-transparent text-purple-700 border-2 border-purple-300 hover:bg-purple-50'
    },
    subtle: {
      green: 'bg-green-50 text-green-700 border border-transparent hover:bg-green-100',
      blue: 'bg-blue-50 text-blue-700 border border-transparent hover:bg-blue-100',
      red: 'bg-red-50 text-red-700 border border-transparent hover:bg-red-100',
      yellow: 'bg-yellow-50 text-yellow-700 border border-transparent hover:bg-yellow-100',
      orange: 'bg-orange-50 text-orange-700 border border-transparent hover:bg-orange-100',
      gray: 'bg-gray-50 text-gray-700 border border-transparent hover:bg-gray-100',
      purple: 'bg-purple-50 text-purple-700 border border-transparent hover:bg-purple-100'
    }
  }
  
  return colorMap[variant as keyof typeof colorMap]?.[color as keyof typeof colorMap.filled] || 
         colorMap.filled.gray
}
</script>

<style scoped>
/* 动画效果 */
.status-tag {
  transition: all 0.2s ease-in-out;
}

/* 脉冲动画用于运行状态 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.status-tag.running {
  animation: pulse 2s infinite;
}

/* 旋转动画用于加载状态 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-tag.loading svg {
  animation: spin 1s linear infinite;
}
</style>
