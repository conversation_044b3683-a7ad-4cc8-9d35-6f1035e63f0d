// 为js-yaml模块添加模拟测试所需的类型声明
declare module 'js-yaml' {
  // 定义模块的默认导出
  const _default: {
    load: {
      (str: string, opts?: LoadOptions): any;
      mockImplementation: (implementation: () => any) => void;
      mockReturnValue: (value: any) => void;
      toHaveBeenCalled: () => boolean;
    };
    dump: {
      (obj: any, opts?: DumpOptions): string;
      mockReturnValue: (value: string) => void;
      toHaveBeenCalled: () => boolean;
    };
  };
  export default _default;
  // 扩展Error接口，添加YAML错误特有的属性
  interface YAMLError extends Error {
    mark?: { line: number }
    reason?: string
  }
  


  // 定义原始类型
  interface LoadOptions {}
  interface DumpOptions {}

  // 扩展函数类型，添加测试所需的mock方法
  interface LoadFunction extends Function {
    (str: string, opts?: LoadOptions): any
    mockImplementation: (implementation: () => any) => void
    mockReturnValue: (value: any) => void
    toHaveBeenCalled: () => boolean
  }

  interface DumpFunction extends Function {
    (obj: any, opts?: DumpOptions): string
    mockReturnValue: (value: string) => void
    toHaveBeenCalled: () => boolean
  }

  // 声明导出函数
  export const load: LoadFunction;
  export const dump: DumpFunction;
  
  // 为测试提供一个可访问的default属性
  export interface DefaultModule {
    load: LoadFunction & {
      mockImplementation: (implementation: () => any) => void;
      mockReturnValue: (value: any) => void;
      toHaveBeenCalled: () => boolean;
    };
    dump: DumpFunction & {
      mockReturnValue: (value: string) => void;
      toHaveBeenCalled: () => boolean;
    };
  }
  
  // 不再需要JsYaml接口，因为我们已经定义了默认导出
}