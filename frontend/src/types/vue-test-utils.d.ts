// 为Vue测试工具添加类型声明
declare module '@vue/test-utils' {
  import { Component } from 'vue'

  export interface MountingOptions<Props> {
    props?: Props
    global?: {
      stubs?: Record<string, any>
      mocks?: Record<string, any>
      plugins?: any[]
      provide?: Record<string, any>
    }
    attachTo?: Element | string
    attrs?: Record<string, any>
    slots?: Record<string, any>
  }

  export interface VueWrapper<T = any> {
    vm: T
    element: Element
    find(selector: string): DOMWrapper<Element>
    findAll(selector: string): DOMWrapper<Element>[]
    findComponent(selector: Component | string): VueWrapper
    findAllComponents(selector: Component | string): VueWrapper[]
    get(selector: string): DOMWrapper<Element>
    getComponent(selector: Component | string): VueWrapper
    html(): string
    text(): string
    trigger(eventName: string, options?: any): Promise<void>
    emitted(event?: string): Record<string, any[]> | any[]
    unmount(): void
    exists(): boolean
  }

  export interface DOMWrapper<T extends Element> {
    element: T & { value?: string }
    exists(): boolean
    find(selector: string): DOMWrapper<Element>
    findAll(selector: string): DOMWrapperArray<Element>
    html(): string
    text(): string
    trigger(eventName: string, options?: any): Promise<void>
    setValue(value: string): Promise<void>
    setChecked(value: boolean): Promise<void>
    attributes(name?: string): string & { includes?: (searchString: string) => boolean }
  }

  export function mount<T extends Component>(component: T, options?: MountingOptions<any>): VueWrapper<T>
  export function shallowMount<T extends Component>(component: T, options?: MountingOptions<any>): VueWrapper<T>
}